<template>
  <div id="app" @click="isTimeOut">
    <router-view/>
  </div>
</template>

<script>
import {authService} from './service'

export default {
  components: {
    // HeaderBar
  },
    data() {
      return {
        lastTime: null, // 最后一次点击的时间
        currentTime: null, // 当前点击的时间
        timeOut: 120 * 60 * 1000 // 设置超时时间:30分钟
      }
  },
    created(){
      this.lastTime = new Date().getTime();
    },
    mounted(){
       var token= authService.getToken();
       
       if(token==null)
       {
         //去获取token
         this.apiGetToken();
       }
    },
  methods:{
    //获取token授权登录
      apiGetToken(){
         var that=this;
          // var data = {
          //   //认证类型 
          //   grant_type: 'password',
          //   client_id: 'clientanduser',
          //   client_secret:'secret',
          //   username: "m1",
          //   password: "password",
          //   scope:"api1"
          // }
          //注意这里需要使用fromdata的请求
        // ajax.post("/connect/token", qs.stringify(data),{headers:{ 'content-type':'application/x-www-form-urlencoded'}}).then(r=>{
        // ajax.post(apiUrls.GetToken+"?openid=oxVLdsu3n9Mdjh5fEfnx2CqcQ4BU").then(r=>{
        //   r=r.data;
        //   console.log(r);
        //   authService.setToken(r); 
        // }).catch(e=>{
        //   console.log(e);
        // });

      },
      //超时
      isTimeOut () {
        //网页一定时间不操作重新登陆
        var that=this;
        //this.$route.name 'login'
        //this.$router.currentRoute.path  '/login'
        if(that.$route.name=="login"){
          return;
        }
        this.currentTime = new Date().getTime() // 记录这次点击的时间
        if ((this.currentTime - this.lastTime) > this.timeOut) { // 判断上次最后一次点击的时间和这次点击的时间间隔是否大于30分钟
          this.lastTime = new Date().getTime();
          this.currentTime = new Date().getTime() // 记录这次点击的时间
          this.$message.warning('登录状态过期,即将跳转登录页');
          setTimeout(function () {
            that.$router.replace({
              path: "/Login",
            });
          }, 1000);
        } else {
          this.lastTime = new Date().getTime() // 如果在30分钟内点击，则把这次点击的时间记录覆盖掉之前存的最后一次点击的时间
        }
    }
  }

};
</script>

<style>
body{
  margin:0px;
}
#app {
  font-family: 'Avenir', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* text-align: center; */
  font-size: 14px !important;
  color: #4a4a4a !important;
  /*margin-top: 60px; */
}
</style>
