<template>
  <div>
    <div class="SMSDiv">
      <div style="width: 100%">短信模板列表</div>
      <el-container style="width: 100%; height: calc(100vh - 150px); border: 1px solid #eee">
        <el-aside width="185px" style="background-color: rgb(238, 241, 246);margin-top: 20px;">
          <div class="asideDiv" v-for="(item, index) in tableConstData" :key="index" @click="SMSTemplateTask(item, index)"
            :class="SMSTemplateTaskSelect(item.id)">

            <span>{{ item.sms_Name }}</span>
          </div>
        </el-aside>

        <el-container>
          <el-header style="font-size: 16px; padding: 0px">
            <div style="display: flex;justify-content: space-between;">
              <div>
                <span>模板名称：</span>
              <el-input v-model="SMSTemplateList.sms_Name" placeholder="" 
                style="width: 200px; word-wrap: break-word; font-size: 18px"></el-input>
              </div>
              <div class="divRight">
                <el-button type="primary" icon="el-icon-plus" size="small"
                  @click="showSMSdialogVisible()">新增自定义模板</el-button>
              </div>
            </div>
          </el-header>

          <el-main>
            <div>
              <div class="textareaDiv">
                <div class="textareaCon">
                  <span style="font-size: 18px; margin-bottom: 10px">内容设置</span><br />
                  <span>状态：</span>
                  <el-switch v-model="switchValue" active-color="#13ce66" inactive-color="#ff4949">
                  </el-switch>
                </div>
              </div>
              <div class="textareaDiv">
                <div class="textareaCon">
                  <div style="margin-top: 20px; margin-bottom: 20px; height: 10px">
                    <div id="div-template" class="templet-header">
                      <div class="keywords">
                        <div class="detail">
                          <span>关键字：</span>
                          <ul id="memberInfo">
                            <li isFocus="true" v-for="(nounId, index) in SMSnounIds" :key="index" @click="selectDetail">{{
                              nounId.name }}</li>
                          </ul>
                        </div>
                      </div>

                      <div class="templet-input">
                        <div><span class="title">模板内容：</span>
                          <div class="text">
                            <textarea class="infoText" style="height: 148px;" v-model="SMSContent"
                              @click="handleTextareaClick" @keyup="handleKeyup" @keydown="handleKeydown" ref="textarea"
                              :isFocus="true" :rows="5"></textarea>
                          </div>
                        </div>
                        <div><span class="title">模板预览：</span>
                          <div class="text">
                            <div class="textareaCon">
                              <!-- <span>预览：</span> -->
                              <div class="infoText" v-html="SMSPreview" style="background-color: aliceblue;"></div>
                            </div>
                          </div>
                        </div>
                        <!-- <div class="input-bar">
                          <button type="button" data-cmd="saveTemplate" @click="SaveContent">保存模板</button>
                        </div>  -->
                      </div>
                      <div class="careBtn">
                        <el-button type="primary" @click="SaveContent" icon="el-icon-folder-checked">立即保存</el-button>
                      </div>
                    </div>
                    <div></div>
                  </div>
                </div>
              </div>
            </div>
          </el-main>
        </el-container>
      </el-container>
      <el-dialog title="新增短信模板" :visible.sync="SMSdialogVisible" width="80%">
        <div style="max-height: 1000px; overflow-y: auto; position: static">
          <div :style="{ height: dialogHeight }" style="margin-right: 20px">
            <div class="textareaDiv">
              <div class="textareaCon">
                <div style="margin-top: 20px; margin-bottom: 20px; height: 10px">
                  <el-form>
                    <el-form-item label-width="80px">
                      <div>
                        <span>模板名称：</span>
                        <el-input v-model="AddTemplateList.sms_Name" placeholder="请输入模板名称"
                          style="width: 240px"></el-input>
                      </div>
                    </el-form-item>
                    <el-form-item label-width="80px">
                      <div>
                        <span>状态：</span>
                        <el-select v-model="AddTemplateList.state" size="small">
                          <el-option label="启用" value="T"></el-option>
                          <el-option label="禁用" value="F"></el-option>
                        </el-select>
                      </div>
                    </el-form-item>
                    <!-- <el-form-item label-width="80px">
                      <template>
                        <el-button
                          class="textareaNou"
                          v-for="(nounId, index) in SMSnounIds"
                          :key="index"
                          @click="textareaDialogAdd(nounId)"
                          type="blue"
                          size="small"
                          >{{ nounId.name }}</el-button
                        >
                      </template>
                    </el-form-item>
                    <el-form-item label-width="80px">
                      <div></div>
                      <div
                        id="SMSContentTextAreadialog"
                        class="infoText"
                        ref="SMSContentTextAreadialog"
                        :contenteditable="true"
                        v-html="DialogContent"
                        @input="btnHandelDialogClick"
                      ></div>
                    </el-form-item>
                    <el-form-item label-width="80px">
                      <div class="textareaCon">
                        <span>短信预览：</span>
                        <div
                          class="infoText"
                          v-html="DialogPreview"
                          :contenteditable="true"
                          style="pointer-events: none"
                        ></div>
                      </div>
                    </el-form-item> -->
                    <el-form-item label-width="80px">
                      <div class="textareaDiv">
                        <div class="textareaCon">
                          <div style="margin-top: 20px; margin-bottom: 20px; height: 10px">
                            <div id="div-template" class="templet-header">
                              <div class="keywords">
                                <div class="detail">
                                  <span>关键字：</span>
                                  <ul id="memberInfo">
                                    <li isFocus="true" v-for="(nounId, index) in SMSnounIds" :key="index"
                                      @click="dialogSelectDetail">{{
                                        nounId.name }}</li>
                                  </ul>
                                </div>
                              </div>

                              <div class="templet-input">
                                <div><span class="title">模板内容：</span>
                                  <div class="text">
                                    <textarea id="dialogTextarea" class="infoText" style="height: 148px;" v-model="DialogContent"
                                      @click="dialogHandleTextareaClick" @keyup="dialogHandleKeyup" @keydown="dialogHandleKeydown"
                                      ref="textarea1" :isFocus="true" :rows="5"></textarea>
                                  </div>
                                </div>
                                <div><span class="title">模板预览：</span>
                                  <div class="text">
                                    <div class="textareaCon">
                                      <!-- <span>预览：</span> -->
                                      <div class="infoText" v-html="DialogPreview" style="background-color: aliceblue;">
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <!-- <div class="input-bar">
                          <button type="button" data-cmd="saveTemplate" @click="SaveContent">保存模板</button>
                        </div>  -->
                              </div>
                            </div>
                            <div></div>
                          </div>
                        </div>
                      </div>
                    </el-form-item>
                  </el-form>
                </div>
              </div>
            </div>
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="SMSdialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="AddTemplate">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { ajax } from "../../common";
// import{Toast} from 'vant';
import apiUrls from "../../config/apiUrls";
export default {
  name: "SMSTemplate",
  data() {
    return {
      ids: "", //id集合 用于批量删除或单个删除
      height: "calc( 100vh - 250px)",
      formLabelWidth: "240",
      tableData: [], //表数据
      tableCopyTableList: [], //表数据集合
      tableConstData: [], //存放数据 用于筛选数据
      index: 1, //当前页数
      size: 50, //页码
      dialogVisible: false, //新增编辑对话框是否显示
      dialogTitle: "", //对话框的标题
      loading: false,
      num: 0,
      //单位模型
      SMSTemplateList: {
        id: "",
        sms_Code: "",
        sms_Name: "",
        sms_Content: "",
        Preview: "",
        nounId: "",
        state: "",
        parameter: "",
        updateTime: "",
      },
      switchValue: false,
      SMSPreview: "",
      SMSNounList: [],
      SMSnounIds: [],
      SMSContent: "",
      SMSParameter: "",
      spanElementList: "",
      SMSdialogVisible: false,
      dialogHeight: "calc( 100vh - 500px)",
      //单位模型
      AddTemplateList: {
        sms_Name: "",
        sms_Content: "",
        Preview: "",
        nounId: "",
        state: "",
        parameter: "",
      },
      DialogContent: "",
      DialogPreview: "",
      allKeyWords: [],
      keyWordsJson: [],
      lastKeyCode: 0,
    };
  },
  created() {
    this.GetAllSMSTemplateList();
  },
  mounted() { },
  methods: {
    handleTextareaClick() {
      var obj = document.querySelector('textarea');
      this.cursorIndex = this.getFocus(obj);
      this.dealFocusExtend(obj, this.cursorIndex);
    },
    handleKeyup(e) {
      //每次在文本域中输入的时候都要获取其光标位置，以便于其他操作
      var obj = document.querySelector('textarea');
      this.cursorIndex = this.getFocus(obj);

      //由于我们是禁止输入中文中括号的，而中文中括号输入左右情况不同，需要分别处理
      // console.log(e.keyCode);
      if (e.keyCode == 219) {
        e.preventDefault();
        //这里获取到光标左侧的内容
        var leftChar = obj.value.slice(this.cursorIndex - 1, this.cursorIndex);

        //只有输入结束的是右中括号，而且它的前一个字符是左中括号才把它删除，防止把关键字删除掉
        if (/\】/g.test(leftChar) && obj.value.charAt(this.cursorIndex - 2) === '【') {
          obj.value = obj.value.slice(0, this.cursorIndex - 2) + obj.value.slice(this.cursorIndex, obj.value.length);
        }

      } else if (e.keyCode == 221) {
        e.preventDefault();
        //右中括号就好办多了，因为它不会自动带出左中括号
        var leftChar = obj.value.slice(this.cursorIndex - 1, this.cursorIndex);
        if (/\】/g.test(leftChar)) {
          obj.value = obj.value.slice(0, this.cursorIndex - 1) + obj.value.slice(this.cursorIndex, obj.value.length);
        }
      }
      //防止上下左右键移动光标进入关键字中
      if ((e.keyCode == 37 || e.keyCode == 39 || e.keyCode == 38 || e.keyCode == 40) && this.lastKeyCode !== 219) {
        dealFocusMove(obj, this.cursorIndex);
      } else if (e.keyCode == 8) {
        //backspace删除的时候删除整个关键字
        // console.log(obj, this.cursorIndex, this.allKeyWords);
        this.dealFocusL(obj, this.cursorIndex, this.allKeyWords);
      } else if (e.keyCode == 46) {
        //delete删除的时候也是删除整个关键字
        this.dealFocusR(obj, this.cursorIndex, this.allKeyWords)
      }
      if (e.keyCode !== 37 && e.keyCode !== 39) {
        //这里防止手动按得左右键影响左中括号判断
        this.lastKeyCode = e.keyCode;
      }
      this.updateSMSPreview()

    },
    handleKeydown(e) {
      if (e.keyCode == 221 || e.keyCode == 219) {
        e.preventDefault();
      }
      if ((e.keyCode == 37 || e.keyCode == 39) && this.this.lastKeyCode === 219) {
        e.preventDefault();
      }
    },

    // initializeKeyWordsJson() {
    //   const newData = this.data1.concat(this.data2).concat(this.data3);
    //   for (let i = 0; i < newData.length; i++) {
    //     if (this.keyWordsJson[newData[i].name] !== null) {
    //       this.keyWordsJson[newData[i].name] = newData[i].id;
    //     }
    //   }
    // },
    // getFocus() {
    //   return this.$refs.textarea.selectionStart;
    // },
    //处理删除关键字
    dealFocusL(obj, index, allKeyWords) {
      var text = obj.value.slice(0, index);
      var resL, resR, i = 0, j = 0;
      var lastIndex = 0;
      var regL = /\【/g;
      var regR = /\】/g;
      while (resL = regL.exec(text)) {
        i++;
        //获取左中括号位置
        lastIndex = regL.lastIndex;
      }
      while (resR = regR.exec(text)) {
        j++;
      }
      if (i != j) {
        var textAll = obj.value;
        obj.value = textAll.substring(0, lastIndex - 1) + textAll.substring(index, textAll.length);
        this.SMSContent=obj.value;
        // console.log(obj.value);
        // allKeyWords.splice(i - 1, 1);
        obj.setSelectionRange(lastIndex - 1, lastIndex - 1);
    // 移动光标后触发点击事件，使光标闪烁，保持焦点
        obj.click();
      }

    },
    //delete关键字
    dealFocusR(obj, index, allKeyWords) {
      var text = obj.value.slice(index, obj.value.length);
      text = text.split('').reverse().join('');
      var resL, resR, i = 0, j = 0;
      var lastIndex = 0;
      var regL = /\【/g;
      var regR = /\】/g;
      while (resL = regL.exec(text)) {
        i++;
      }
      while (resR = regR.exec(text)) {
        j++;
        lastIndex = regR.lastIndex;
      }
      if (i != j) {
        //获取右中括号位置
        var textAll = obj.value;
        lastIndex = index + text.length - lastIndex + 1;
        allKeyWords.splice(j - 1, 1);
        obj.value = textAll.substring(0, index) + textAll.substring(lastIndex, textAll.length);
        this.SMSContent=obj.value;
        obj.setSelectionRange(index, index);
    // 移动光标后触发点击事件，使光标闪烁，保持焦点
        obj.click();
      }
    },
    //处理光标上下左右移动
    dealFocusMove(obj, index) {
      var text = obj.value.slice(0, index);
      var resL, resR, i = 0, j = 0;
      var lastIndex = 0;
      var _lastIndex = 0;
      var regL = /\【/g;
      var regR = /\】/g;
      while (resL = regL.exec(text)) {
        i++;
        lastIndex = regL.lastIndex;
      }
      while (resR = regR.exec(text)) {
        j++;
      }
      if (i != j) {
        if (index == lastIndex) {
          var rightText = regR.exec(obj.value.slice(index, obj.value.length));
          _lastIndex = rightText['index'];
          index = _lastIndex + index + 1;
        } else {
          index = lastIndex - 1;
        }
        obj.selectionStart = index;
        obj.selectionEnd = index;
      }
    },
    //处理鼠标定位光标
    dealFocusExtend(obj, index) {
      var text = obj.value.slice(index, obj.value.length);
      // var text = obj.value;
      // console.log(text);
      var resL, resR, i = 0, j = 0;
      var lastIndex = 0;
      var firstRightBracketIndex = -1; // 记录第一个右中括号的索引
      var regL = /\【/g;
      var regR = /\】/g;
      while (resL = regL.exec(text)) {
        i++;
        
      }
      while (resR = regR.exec(text)) {
        j++;
        if (firstRightBracketIndex === -1) {
          firstRightBracketIndex = resR.index + index + 1; // 记录第一个右中括号的索引
        }
        lastIndex = regR.index;
      }
      // console.log(index);
      if (i != j) {
        // var text = obj.value;
        index = obj.value.length;
        obj.selectionStart = firstRightBracketIndex;
        obj.selectionEnd = firstRightBracketIndex;
      }
    },
    //获取光标位置
    getFocus(elem) {
      var index = 0;
      if (document.selection) { // IE Support 
        elem.focus();
        var Sel = document.selection.createRange();
        if (elem.nodeName === 'TEXTAREA') { //textarea 
          var Sel2 = Sel.duplicate();
          Sel2.moveToElementText(elem);
          var index = -1;
          while (Sel2.inRange(Sel)) {
            Sel2.moveStart('character');
            index++;
          };
        } else if (elem.nodeName === 'INPUT') { // input 
          Sel.moveStart('character', -elem.value.length);
          index = Sel.text.length;
        }
      } else if (elem.selectionStart || elem.selectionStart == '0') { // Firefox support
        index = elem.selectionStart;
      }
      return (index);
    },
    selectDetail(e) {
      console.log(e);
      var obj = document.querySelector('textarea');
      this.cursorIndex = this.getFocus(obj);
      //首先判断是否有光标，这样我们的光标位置是不存在的
      if (this.cursorIndex !== null) {
        //这里判断是否是我们要点击的是不是关键字
        if (e.target.tagName !== "TEXTAREA" && e.target.getAttribute('isFocus')) {
          //要添加东西当然要先放入光标了，这里会记住之前的光标位置，所以直接focus即可
          obj.focus();
          this.cursorIndex = this.getFocus(obj);
          var text = obj.value;
          //文本中关键字以中括号包裹的形式显示
          var textNode = text.substring(0, this.cursorIndex) + '【' + e.target.innerHTML + '】' + text.substring(this.cursorIndex, text.length);
          this.allKeyWords.push(e.target.innerHTML);
          obj.value = textNode;
          //添加完之后我们要刷新光标位置
          this.SMSContent=obj.value;
          this.updateSMSPreview();
          this.cursorIndex = this.cursorIndex + e.target.innerHTML.length + 2;
          obj.selectionStart = this.cursorIndex;
          obj.selectionEnd = this.cursorIndex;

        } else if (e.target.tagName == "TEXTAREA" && e.target.getAttribute('isFocus')) {
          //点击文本区域操作
          this.cursorIndex = getFocus(obj);
        } else {
          //点击其他地方要将光标位置置空，防止点击关键字添加
          this.cursorIndex = null;
          console.log(1);
        }
      }
    },
    updateSMSPreview() {
      var obj = document.querySelector('textarea');
      // var templatetypename = document.querySelector('[data-type="templateName"]').value||"";
      //模板原始内容
      var templatename = obj.value || "";
      var regex = /\【([^\【\】]+)\】/g;
      let placeholders = [];
      this.SMSTemplateList.preview = templatename.replace(regex, (match) => {
        placeholders.push(`{${placeholders.length}}`);
        return placeholders[placeholders.length - 1];
      });
      this.SMSTemplateList.parameter = this.getParameter(templatename);
      // console.log(placeholders);
      // console.log(this.SMSTemplateList.parameter);
      console.log(this.SMSTemplateList.preview,
        this.SMSTemplateList.parameter);
      this.SMSPreview = this.formatPreviewString(
        this.SMSTemplateList.preview,
        this.SMSTemplateList.parameter
      );
      // console.log(this.SMSPreview);
    },
    formatPreviewString(formatted, parameter) {
      // console.log(formatted, parameter);
      if (parameter) {
        var parameterList = parameter.split(",");
        var args = [];
        for (let i = 0; i < parameterList.length; i++) {
          for (let z = 0; z < this.SMSNounList.length; z++) {
            if (parameterList[i] == this.SMSNounList[z].code) {
              args.push(
                "<span id=" +
                this.SMSNounList[z].code +
                " style='color: blue;'>" +
                this.SMSNounList[z].smSdefault +
                "</span>"
              );
            }
          }
        }
        for (let i = 0; i < args.length; i++) {
          let regexp = new RegExp("\\{" + i + "\\}", "gi");
          formatted = formatted.replace(regexp, args[i]);
        }
      }
      return formatted;
    },
    getParameter(message) {
      // console.log(message);
      // 使用正则表达式提取<span>标签内容
      var regEx = /\【([^\【\】]+)\】/g;
      var spanContents = [];
      var match;

      while ((match = regEx.exec(message))) {
        // 获取匹配到的<span>标签内容
        var spanContent = match[1];
        spanContents.push(spanContent);
      }
      // console.log(spanContents);
      // console.log(spanContents,this.SMSnounIds);
      let nounIds = [];
      for (var i = 0; i < spanContents.length; i++) {
        for (let j = 0; j < this.SMSnounIds.length; j++) {
          // console.log(this.allKeyWords[i]);
          if (spanContents[i] == this.SMSnounIds[j].name) {
            nounIds.push(this.SMSnounIds[j].code);
          }
        }
        // keywords.push(this.keyWordsJson[this.allKeyWords[i]]);
      }
      // console.log(nounIds);
      return nounIds.join(",");
    },
    // SaveContent() {
    //     //模板名称
    //     var obj = document.querySelector('textarea');
    //     // var templatetypename = document.querySelector('[data-type="templateName"]').value||"";
    //     //模板原始内容
    //     var templatename = obj.value||"";
    //     var reg = /\【([^\【\】]+)\】/g;
    //     var res;
    //     //我们把模板做一次变更，方便后端识别，模板关键字变为这种形式：[*]
    //     var templateformula = templatename.replace(reg,'[*]')||"";
    //     var keywords=[];
    //     //取出所有的关键字id，这里的顺序与模板内容的关键字一一对应
    //     for(var i=0;i<this.allKeyWords.length;i++){
    //       keywords.push(this.keyWordsJson[this.allKeyWords[i]]);
    //     }

    //     console.log(templatename,templateformula,keywords);
    //   },
    SaveContent() {
      if (this.switchValue == true) {
        this.SMSTemplateList.state = "T";
      } else {
        this.SMSTemplateList.state = "F";
      }
      var pData = {
        sMSTemplate: this.SMSTemplateList,
      };
      ajax
        .post(apiUrls.UpdateSMSTemplate, pData)
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          this.$message.success("操作成功");
          // this.dialogVisible = false; //成功后关闭对话框
          // this.GetLncList(); //重新加载
          this.GetAllSMSTemplateList();
        })
        .catch((err) => {
          console.log(err);
          this.$message.error("系统繁忙！请稍后再试");
        });
    },
    dialogHandleTextareaClick() {
      var obj = document.querySelector('#dialogTextarea');
      // console.log(obj);
      this.cursorIndex = this.getFocus(obj);
      this.dealFocusExtend(obj, this.cursorIndex);
    },
    dialogHandleKeyup(e) {

      //每次在文本域中输入的时候都要获取其光标位置，以便于其他操作
      var obj = document.querySelector('#dialogTextarea');
      console.log(obj);
      this.cursorIndex = this.getFocus(obj);

      //由于我们是禁止输入中文中括号的，而中文中括号输入左右情况不同，需要分别处理
      if (e.keyCode == 219) {
        e.preventDefault();
        //这里获取到光标左侧的内容
        var leftChar = obj.value.slice(this.cursorIndex - 1, this.cursorIndex);

        //只有输入结束的是右中括号，而且它的前一个字符是左中括号才把它删除，防止把关键字删除掉
        if (/\】/g.test(leftChar) && obj.value.charAt(this.cursorIndex - 2) === '【') {
          obj.value = obj.value.slice(0, this.cursorIndex - 2) + obj.value.slice(this.cursorIndex, obj.value.length);
        }

      } else if (e.keyCode == 221) {
        e.preventDefault();
        //右中括号就好办多了，因为它不会自动带出左中括号
        var leftChar = obj.value.slice(this.cursorIndex - 1, this.cursorIndex);
        if (/\】/g.test(leftChar)) {
          obj.value = obj.value.slice(0, this.cursorIndex - 1) + obj.value.slice(this.cursorIndex, obj.value.length);
        }
      }
      //防止上下左右键移动光标进入关键字中
      if ((e.keyCode == 37 || e.keyCode == 39 || e.keyCode == 38 || e.keyCode == 40) && this.lastKeyCode !== 219) {
        dealFocusMove(obj, this.cursorIndex);
      } else if (e.keyCode == 8) {
        //backspace删除的时候删除整个关键字
        this.dialogDealFocusL(obj, this.cursorIndex, this.allKeyWords);
      } else if (e.keyCode == 46) {
        //delete删除的时候也是删除整个关键字
        this.dialogDealFocusR(obj, this.cursorIndex, this.allKeyWords)
      }
      if (e.keyCode !== 37 && e.keyCode !== 39) {
        //这里防止手动按得左右键影响左中括号判断
        this.lastKeyCode = e.keyCode;
      }
      this.updateDialogSMSPreview()

    },
    dialogDealFocusL(obj, index, allKeyWords) {
      var text = obj.value.slice(0, index);
      var resL, resR, i = 0, j = 0;
      var lastIndex = 0;
      var regL = /\【/g;
      var regR = /\】/g;
      while (resL = regL.exec(text)) {
        i++;
        //获取左中括号位置
        lastIndex = regL.lastIndex;
      }
      while (resR = regR.exec(text)) {
        j++;
      }
      console.log(lastIndex);
      if (i != j) {
        var textAll = obj.value;
        obj.value = textAll.substring(0, lastIndex - 1) + textAll.substring(index, textAll.length);
        this.DialogContent=obj.value;
        // console.log(obj.value);
        // allKeyWords.splice(i - 1, 1);
        obj.setSelectionRange(lastIndex - 1, lastIndex - 1);
    // 移动光标后触发点击事件，使光标闪烁，保持焦点
        obj.click();
      }

    },
    //delete关键字
    dialogDealFocusR(obj, index, allKeyWords) {
      var text = obj.value.slice(index, obj.value.length);
      text = text.split('').reverse().join('');
      var resL, resR, i = 0, j = 0;
      var lastIndex = 0;
      var regL = /\【/g;
      var regR = /\】/g;
      while (resL = regL.exec(text)) {
        i++;
      }
      while (resR = regR.exec(text)) {
        j++;
        lastIndex = regR.lastIndex;
      }
      if (i != j) {
        //获取右中括号位置
        var textAll = obj.value;
        lastIndex = index + text.length - lastIndex + 1;
        allKeyWords.splice(j - 1, 1);
        obj.value = textAll.substring(0, index) + textAll.substring(lastIndex, textAll.length);
        this.DialogContent=obj.value;
        obj.setSelectionRange(index, index);
    // 移动光标后触发点击事件，使光标闪烁，保持焦点
        obj.click();
      }
    },
    dialogHandleKeydown(e) {
      if (e.keyCode == 221 || e.keyCode == 219) {
        e.preventDefault();
      }
      if ((e.keyCode == 37 || e.keyCode == 39) && this.this.lastKeyCode === 219) {
        e.preventDefault();
      }
    },
    dialogSelectDetail(e) {
      
      var obj = document.querySelector('#dialogTextarea');
      this.cursorIndex = this.getFocus(obj);
      //首先判断是否有光标，这样我们的光标位置是不存在的
      if (this.cursorIndex !== null) {
        //这里判断是否是我们要点击的是不是关键字
        if (e.target.tagName !== "TEXTAREA" && e.target.getAttribute('isFocus')) {
          //要添加东西当然要先放入光标了，这里会记住之前的光标位置，所以直接focus即可
          obj.focus();
          this.cursorIndex = this.getFocus(obj);
          var text = obj.value;
          //文本中关键字以中括号包裹的形式显示
          var textNode = text.substring(0, this.cursorIndex) + '【' + e.target.innerHTML + '】' + text.substring(this.cursorIndex, text.length);
          this.allKeyWords.push(e.target.innerHTML);
          obj.value = textNode;
          this.DialogContent=obj.value;
          this.updateDialogSMSPreview();
          //添加完之后我们要刷新光标位置
          this.cursorIndex = this.cursorIndex + e.target.innerHTML.length + 2;
          obj.selectionStart = this.cursorIndex;
          obj.selectionEnd = this.cursorIndex;

        } else if (e.target.tagName == "TEXTAREA" && e.target.getAttribute('isFocus')) {
          //点击文本区域操作
          this.cursorIndex = getFocus(obj);
        } else {
          //点击其他地方要将光标位置置空，防止点击关键字添加
          this.cursorIndex = null;
          console.log(1);
        }
      }
    },
    updateDialogSMSPreview() {
      var obj = document.querySelector('#dialogTextarea');
      // var templatetypename = document.querySelector('[data-type="templateName"]').value||"";
      //模板原始内容
      var templatename = obj.value || "";
      var regex = /\【([^\【\】]+)\】/g;
      let placeholders = [];
      this.AddTemplateList.preview = templatename.replace(regex, (match) => {
        placeholders.push(`{${placeholders.length}}`);
        return placeholders[placeholders.length - 1];
      });
      // console.log(this.AddTemplateList.preview,
      //   this.AddTemplateList.parameter);
      this.AddTemplateList.parameter = this.getParameter(templatename);
      // console.log(placeholders);
      this.DialogPreview = this.formatPreviewString(
        this.AddTemplateList.preview,
        this.AddTemplateList.parameter
      );
      // console.log(this.SMSPreview);
    },





    GetAllSMSTemplateList() {
      var that = this;
      that.loading = false;
      var pData = {
          code: "all",
      };
      ajax
        .post(apiUrls.GetAllSMSTemplate,pData, { nocrypt: true })
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          // 初始化数据
          that.tableConstData = r.data.returnData.st ? r.data.returnData.st.filter((item) => {
                return !item.lnc_Code;
              })
            : [];
          that.SMSNounList = r.data.returnData.sn;
          this.SMSTemplateTask(that.tableConstData[this.num], this.num);
        })
        .catch((err) => {
          alert("获取短信模板失败,请稍后重试");
        });
    },
    SMSTemplateTask(SMSTemplate, index) {
      this.num = index;
      if (SMSTemplate.state == "T") {
        this.switchValue = true;
      } else {
        this.switchValue = false;
      }
      this.SMSTemplateList = SMSTemplate;
      this.SMSPreview = "";
      this.SMSParameter = SMSTemplate.parameter;
      if (SMSTemplate.nounId) {
        var nounId = SMSTemplate.nounId.split(",");
        var SMSnounIds = [];
        for (let i = 0; i < nounId.length; i++) {
          for (let z = 0; z < this.SMSNounList.length; z++) {
            if (nounId[i] == this.SMSNounList[z].code) {
              SMSnounIds.push(this.SMSNounList[z]);
            }
          }
        }
        this.SMSnounIds = SMSnounIds;
      }
      this.SMSPreview = this.formatString(
        SMSTemplate.preview,
        SMSTemplate.parameter
      );
      this.SMSContent = this.formatContent(
        this.SMSTemplateList.preview,
        this.SMSTemplateList.parameter
      );
    },
    formatString(formatted, parameter) {
      if (parameter) {
        var parameterList = parameter.split(",");
        var args = [];
        for (let i = 0; i < parameterList.length; i++) {
          for (let z = 0; z < this.SMSNounList.length; z++) {
            if (parameterList[i] == this.SMSNounList[z].code) {
              args.push(
                "<span id=" +
                this.SMSNounList[z].code +
                " style='color: blue;'>" +
                this.SMSNounList[z].smSdefault +
                "</span>"
              );
            }
          }
        }
        for (let i = 0; i < args.length; i++) {
          let regexp = new RegExp("\\{" + i + "\\}", "gi");
          formatted = formatted.replace(regexp, args[i]);
        }
      }
      return formatted;
    },
    formatContent(formatted, parameter) {
      if (parameter) {
        var parameterList = parameter.split(",");
        var args = [];
        // var spanElement = [];
        for (let i = 0; i < parameterList.length; i++) {
          for (let z = 0; z < this.SMSNounList.length; z++) {
            if (parameterList[i] == this.SMSNounList[z].code) {
              args.push(
                "【" +
                this.SMSNounList[z].name +
                "】"
              );
              // spanElement.push(this.SMSNounList[z].code +i);
            }
          }
        }
        for (let i = 0; i < args.length; i++) {
          let regexp = new RegExp("\\{" + i + "\\}", "gi");
          formatted = formatted.replace(regexp, args[i]);
        }
      }


      // this.spanElementList=spanElement.join(",");
      return formatted;
    },
    // SaveContent() {
    //   if (this.switchValue == true) {
    //     this.SMSTemplateList.state = "T";
    //   } else {
    //     this.SMSTemplateList.state = "F";
    //   }
    //   var pData = {
    //     sMSTemplate: this.SMSTemplateList,
    //   };
    //   ajax
    //     .post(apiUrls.UpdateSMSTemplate, pData)
    //     .then((r) => {
    //       if (!r.data.success) {
    //         alert(r.data.returnMsg);
    //         return;
    //       }
    //       this.$message.success("操作成功");
    //       // this.dialogVisible = false; //成功后关闭对话框
    //       // this.GetLncList(); //重新加载
    //       this.GetAllSMSTemplateList();
    //     })
    //     .catch((err) => {
    //       console.log(err);
    //       this.$message.error("系统繁忙！请稍后再试");
    //     });
    // },
    showSMSdialogVisible() {
      this.DialogContent = this.SMSContent;
      this.DialogPreview = this.SMSPreview;
      this.AddTemplateList.state = "F";
      this.AddTemplateList.NounId = this.SMSTemplateList.nounId;
      this.AddTemplateList.parameter = this.SMSTemplateList.parameter;
      this.AddTemplateList.preview = this.SMSTemplateList.preview;
      this.AddTemplateList.application = this.SMSTemplateList.application;
      this.AddTemplateList.appid = this.SMSTemplateList.appid;
      this.SMSdialogVisible = true;
    },
    verifyTemplate() {
      if (!this.AddTemplateList.sms_Name) {
        this.$message.warning("请输入模板名称");
        return false;
      }
      if (!this.AddTemplateList.state) {
        this.$message.warning("请选择模板状态");
        return false;
      }
      if (!this.AddTemplateList.preview) {
        this.$message.warning("未获取到消息模板");
        return false;
      }
      if (!this.AddTemplateList.parameter) {
        this.$message.warning("未获取到消息模板");
        return false;
      }
      if (!this.AddTemplateList.NounId) {
        this.$message.warning("未获取到消息模板");
        return false;
      }
      return true;
    },
    AddTemplate() {
      //参数验证
      if (!this.verifyTemplate()) {
        return;
      }
      var pData = {
        sMSTemplate: this.AddTemplateList,
      };
      ajax
        .post(apiUrls.AddSMSTemplate, pData)
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          this.$message.success("操作成功");
          // this.dialogVisible = false; //成功后关闭对话框
          // this.GetLncList(); //重新加载
          this.SMSdialogVisible = false;
          this.GetAllSMSTemplateList();
        })
        .catch((err) => {
          console.log(err);
          this.$message.error("系统繁忙！请稍后再试");
        });
    },
    SMSTemplateTaskSelect(val) {
      if (val == this.SMSTemplateList.id) {
        return "SMSTemplateTaskSelectDiv";
      }
    }
  },
};
</script>
  
<style lang="scss">
.SMSDiv {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 96%;
  margin: 20px auto;
  font-size: 16px;
}

.SMSDiv .el-header {
  // background-color: #b3c0d1;
  color: #333;
  line-height: 60px;
}

.SMSDiv .el-aside {
  color: #333;
}

.asideDiv {
  -webkit-appearance: none;
  background-color: #fff;
  background-image: none;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  box-sizing: border-box;
  color: #606266;
  display: inline-block;
  font-size: inherit;
  height: 45px;
  line-height: 40px;
  outline: none;
  padding: 0 15px;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  width: 100%;
  margin-bottom: 20px;
  font-size: 17px;
  text-align: center;
  line-height: 45px;
}

.textareaDiv {
  width: 100%;
}

.textareaDiv .textareaCon {
  margin-top: 20px;
  width: 100%;
  display: block;
  font-size: 16px;
  margin-left: 0px;
  margin-right: auto;
}

.textareaCon textarea {
  font-size: 16px;
}

.textareaCon .infoText {
  height: 156px;
  display: block;
  resize: vertical;
  padding: 5px 15px;
  line-height: 1.5;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
  font-size: inherit;
  color: #606266;
  background-color: #fff;
  background-image: none;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  // max-height: 156px;
  // height: 156px;
  // overflow-y: auto; /* 允许内容垂直滚动 */
  // overflow-x: hidden; /* 隐藏水平滚动条 */
  // white-space: nowrap; /* 防止内容自动换行 */
}

.textareaCon .textareaNou {
  // width: 150px;
  // height: 50px;

  font-size: 16px;
  background-color: #dddddd;
  margin-right: 5px;
  margin-top: 5px;
  float: left;
}

.box {
  width: 228px;
  height: 150px;

  /*给div添加可以调整控制盒子大小的属性*/
  resize: both;
  overflow: auto;

  border: 1px solid #a0b3d6;

  display: inline-block;
  box-sizing: border-box;

  /*控制转中时外边框的颜色*/
  outline-color: #000;

  font-size: 12px;
}

.careBtn {
  margin-top: 20px;
  padding: 20px;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  height: 10%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-right: 35px;
}

#SMSContentTextArea {
  font-size: 16px;
}

#SMSContentTextAreadialog {
  font-size: 16px;
}

.el-form-item__label {
  margin-top: 10px;
  font-size: 16px;
}

.el-container {
  margin-left: 20px;
}

.SMSTemplateTaskSelectDiv {
  background-color: #018bf0;
}




//新
.templet-header {
  background-color: #fff;
  margin-bottom: 10px;
  position: relative;
}

.templet-header .keywords {
  height: 50px;
  line-height: 50px;
  border-bottom: 1px solid #e4e8ee;
  padding-bottom: 3px;
}

.templet-header .line {
  border-bottom: 1px dashed #c5d3de;
  height: 18px;
  text-align: center;
  margin: 0 0 12px 0;
}

.templet-header .line>span {
  background-color: #fff;
  display: inline-block;
  line-height: 35px;
  width: 60px;
  color: #838383;
  font-weight: bold;
}

.templet-header .detail {
  padding: 0 12px;
}

.templet-header .detail>span {
  color: #000;
}

.templet-header .detail>ul {
  width: 88%;
  display: inline-block;
  font-size: 0;
  padding-left: 15px;
}

// .templet-header .detail>ul>li {
//   font-size: 18px;
//   cursor: pointer;
//   color: #696d75;
//   border: 1px solid #DEE2EB;
//   border-radius: 40px;
//   height: 27px;
//   line-height: 27px;
//   padding: 0 15px;
//   display: inline-block;
//   margin-right: 5px;
//   margin-bottom: 2px;
// }

.templet-header .detail>ul>li.on {
  font-weight: bold;
  background: #FFAC63;
  border: 1px solid #FFAC63;
  color: #fff;
}

.templet-header .detail>ul>li:hover {
  font-weight: bold;
  background: #FFAC63;
  border: 1px solid #FFAC63;
  color: #fff;
}

.templet-header .templet-input {
  padding: 12px 20px 12px 14px;
}

.templet-header .templet-input .input-bar {
  line-height: 34px;
}

.templet-header .templet-input .title {
  color: #50545C;
  float: left;
}

.templet-header .templet-input input {
  margin-left: 15px;
  border: 1px solid #E4E8EE;
  border-radius: 4px;
  height: 30px;
  line-height: 30px;
  width: 250px;
  padding-left: 10px;
  outline: none;
}

.templet-header .templet-input input:-webkit-input-placeholder {
  color: #696D75;
}

.templet-header .templet-input button {
  background: #65C0F4;
  border-radius: 40px;
  color: #fff;
  height: 30px;
  line-height: 30px;
  width: 100px;
  float: right;
  border: none;
  outline: none;
}

.templet-header .templet-input .text {
  overflow: hidden;
  margin-top: 14px;
  padding-left: 15px;
}

.templet-header .templet-input .text textarea {
  width: 100%;
  resize: none;
  outline: none;
  border: 1px solid #E4E8EE;
  border-radius: 4px;
  padding: 10px;
  height: 100px;
  box-sizing: border-box;
}

.templet-header .header-mask {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.4);
}
</style>
  