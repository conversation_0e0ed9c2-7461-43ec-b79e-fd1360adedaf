<template>
  <div>
    <div class="lncDiv" v-loading="loading" element-loading-text="获取中。。。">
      <div class="lncTop">
        <div>问卷调查维护(答案)</div>
      </div>
      <div class="lncMid">
        <el-table
          :data="tableData"
          border
          stripe
          :fit="true"
          @selection-change="handleSelectionChangePeople"
          :height="height"
        >
          <el-table-column
            type="selection"
            width="55"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="answerName"
            label="答案列表"
            align="center"
            sortable
          ></el-table-column>
          <el-table-column
            prop="type"
            label="答案类型"
            align="center"
            sortable
            :formatter="formatType"
          ></el-table-column>

          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <el-button
                @click="showAddorEditDialog(scope.row)"
                type="primary"
                plain
                >编辑修改</el-button
              >
              <el-button @click="releEditDialog(scope.row)" type="primary" plain
                >项目关联</el-button
              >
              <!--单个删除-->
            </template>
          </el-table-column>
        </el-table>
        <div class="pageNation">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="index"
            :page-sizes="[50, 100, 200, 300, 500]"
            :page-size="size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableCopyTableList.length"
          ></el-pagination>
        </div>
      </div>
      <!--新增/编辑对话框-->
      <el-dialog
        :title="dialogTitle"
        :visible.sync="dialogVisible"
        :close-on-click-modal="false"
      >
        <div style="width: 70%">
          <div>答案详情</div>
          <div style="display: flex; padding: 10px">
            <div style="width: 100%">
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="items.questionName"
              ></el-input>
              <el-select v-model="questionType" placeholder="请选择填写类型">
                <el-option
                  v-for="item in typeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </div>
          </div>
        </div>
        <div slot="footer">
          <el-button type="primary" size="small" @click="InsertOrUpdateInfo"
            >确定</el-button
          >
          <el-button type="danger" size="small" @click="dialogVisible = false"
            >取消</el-button
          >
        </div>
      </el-dialog>
      <!-- 添加分类dialog -->
      <el-dialog
        title="编辑标题"
        :visible.sync="addQuestionDialogVisible"
        width="50%"
        :close-on-click-modal="false"
      >
        <el-input
          v-model="QuestionTitle"
          placeholder="请输入标题"
          maxlength="15"
          show-word-limit
        ></el-input>
        <el-select v-model="questionType" placeholder="请选择填写类型">
          <el-option
            v-for="item in typeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <div slot="footer">
          <el-button
            type="primary"
            size="small"
            @click="addQuestionDialogVisible = false"
            >取消</el-button
          >
          <el-button type="danger" size="small" @click="confirmAddClassify"
            >确定</el-button
          >
        </div>
      </el-dialog>

      <!--新增/编辑答案-->
      <el-dialog
        title="答案列表"
        :visible.sync="answerDialogVisible"
        :close-on-click-modal="false"
      >
        <div style="width: 70%">
          <!-- <div>答案列表</div> -->
          <div style="display: flex; padding: 10px">
            <div style="width: 100%">
              <el-input
                type="text"
                placeholder="请输入内容"
                v-model="item.answerName"
                v-for="(item, index) in answerList"
                :key="index"
              ></el-input>
            </div>
          </div>
          <div style="width: 100%; float: right">
            <el-button type="primary" style="float: right" @click="addinput"
              >添加</el-button
            >
          </div>
        </div>
        <div slot="footer">
          <el-button type="primary" size="small" @click="InsertOrUpdateAnswer"
            >确定</el-button
          >
          <el-button
            type="danger"
            size="small"
            @click="answerDialogVisible = false"
            >取消</el-button
          >
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { ajax } from "../../common";
import apiUrls from "../../config/apiUrls";
export default {
  name: "Answer",
  data() {
    return {
      ids: "", //id集合 用于批量删除或单个删除
      height: "calc( 100vh - 250px)",
      tableData: [], //表数据
      tableCopyTableList: [], //表数据集合
      index: 1, //当前页数
      size: 50, //页码
      dialogVisible: false, //编辑对话框是否显示
      addQuestionDialogVisible: false, //新增对话框是否显示
      dialogTitle: "", //对话框的标题
      loading: false,
      items: [],
      QuestionTitle: "",
      typeOptions: [
        {
          value: "1",
          label: "单选",
        },
        {
          value: "2",
          label: "多选",
        },
        {
          value: "3",
          label: "填空",
        },
      ], //填写类型
      questionType: "", //问题类型
      answerDialogVisible: false,
      answerList: [],
    };
  },
  created() {
    //加载答案
    this.GetAnswerListAll();
  },
  methods: {
    formatType(row) {
      return row.type == "1" ? "单选" : row.type == "2" ? "多选" : "填空";
    },
    GetAnswerListAll() {
      var that = this;
      that.loading = true;
      ajax
        .post(apiUrls.GetAnswerListAll)
        .then((r) => {
          var data = r.data;
          if (data.success) {
            that.tableData = data.returnData;
            that.loading = false;
            this.tableCopyTableList = this.tableData;
            return;
            // that.$message.success(r.data.returnMsg);
          }
          that.$message.warning(r.data.returnMsg);
          that.loading = false;
          return;
        })
        .catch((e) => {
          that.$message.console.error("获取异常数据失败！请联系管理员");
          that.loading = false;
          return;
        });
    },
    //增加答案
    addinput() {
      // console.log(this.answerList);
      this.answerList.push({
        qId: this.answerList[0].qId,
        answerName: "",
      });
      // console.log(this.answerList);
    },
    showAddorEditDialog(row) {
      console.log(row.questionCode);
      var that = this;
      var pData = {
        questionCode: row.questionCode,
      };
      ajax
        .post(apiUrls.GetQuestionnaireDetail, pData, { nocrypt: true })
        .then((r) => {
          var data = r.data;
          if (data.success) {
            that.items = data.returnData;
            that.dialogVisible = true;
            return;
          }
          that.$message.warning(r.data.returnMsg);
          return;
        })
        .catch((e) => {
          that.$message.console.error("获取异常数据失败！请联系管理员");
        });
    },
    //答案关联
    releEditDialog(row) {
      this.GetAnswerList(row.questionCode);
      console.log(row);
      this.answerDialogVisible = true;
    },
    //获取答案列表
    GetAnswerList(qId) {
      console.log(qId);
      var pData = {
        questionCode: qId,
      };
      ajax.post(apiUrls.GetAnswerList, pData, { nocrypt: true }).then((r) => {
        if (r.data.success) {
          this.answerList = r.data.returnData;
          return;
        }
      });
    },
    //修改问题信息
    InsertOrUpdateInfo() {
      this.items.type = this.questionType;
      var pData = this.items;
      console.log(pData);
      ajax
        .post(apiUrls.UpdateQuestion, pData, { nocrypt: true })
        .then((r) => {
          var data = r.data;
          if (data.success) {
            this.$message.success("保存成功");
            this.dialogVisible = false;
            this.GetQuestionList();
            return;
          }
          this.$message.warning(data.returnMsg);
        })
        .catch((e) => {
          this.$message.error("保存失败！请联系管理员");
          return;
        });
    },
    //修改答案信息
    InsertOrUpdateAnswer() {
      console.log(this.answerList);
      var pData = this.answerList;
      ajax.post(apiUrls.AddAnswerList, pData).then((r) => {
        if (r.data.success) {
          this.$message.success("添加成功！");
          this.answerDialogVisible = false;
          this.GetQuestionList();
          return;
        }
        this.$message.warning(r.data.returnMsg);
      });
    },
    //获取选中行id
    handleSelectionChangePeople(rows) {
      this.ids = rows.map((row) => row.id);
    },

    // 页数改变事件
    handleSizeChange(size) {
      this.size = size;
      this.tableData = this.paging(size, this.index);
    },
    // 页码改变事件
    handleCurrentChange(current) {
      this.index = current;
      this.tableData = this.paging(this.size, current);
    },
    // 本地分页的方法
    paging(size, current) {
      const tableList = JSON.parse(JSON.stringify(this.tableCopyTableList));
      const tablePush = [];
      tableList.forEach((item, index) => {
        if (size * (current - 1) <= index && index <= size * current - 1) {
          tablePush.push(item);
        }
      });
      return tablePush;
    },
    //确认增加分类
    confirmAddClassify() {
      const pData = {
        questionName: this.QuestionTitle,
        questionType: this.questionType,
      };
      ajax.post(apiUrls.AddQuestion, pData, { nocrypt: true }).then((r) => {
        if (!r.data.success) {
          this.$message.error("系统错误，请联系管理员！");
          this.addQuestionDialogVisible = false;
          return;
        }
        this.$message.success("新增成功");
        this.addQuestionDialogVisible = false;
        this.GetQuestionList();
      });
    },
  },
};
</script>

<style lang="scss">
.lncDiv {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 96%;
  margin: 20px auto;
  .lncTop {
    display: flex;
    width: 100%;
    margin-top: 10px;
    justify-content: space-between;
  }
  .lncMid {
    margin-top: 20px;
    width: 100%;
    .pageNation {
      margin-top: 10px;
      .el-pagination {
        display: flex;
        justify-content: flex-end;
      }
    }
  }
}
</style>
<style>
.el-textarea {
  padding: 5px !important;
}
</style>
