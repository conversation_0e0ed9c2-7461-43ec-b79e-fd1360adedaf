<template>
  <div>
    <div class="SMSDiv">
      <div style="width: 100%">短信模板列表</div>
      <el-container
        style="width: 100%; height: calc(100vh - 150px); border: 1px solid #eee"
      >
        <el-aside width="185px" style="background-color: rgb(238, 241, 246);margin-top: 20px;">
          <div
            class="asideDiv"
            v-for="(item, index) in tableConstData"
            :key="index"
            @click="SMSTemplateTask(item, index)"
            :class="SMSTemplateTaskSelect(item.id)"
          >
                  
            <span>{{ item.sms_Name }}</span>
          </div>
        </el-aside>

        <el-container>
          <el-header style="font-size: 16px; padding: 0px">
            <div>
              <span>模板名称：</span>
              <el-input
                v-model="SMSTemplateList.sms_Name"
                placeholder=""
                readonly
                style="width: 200px; word-wrap: break-word; font-size: 18px"
              ></el-input>
              <div class="divRight">
                <el-button
                  type="primary"
                  icon="el-icon-plus"
                  size="small"
                  @click="showSMSdialogVisible()"
                  >新增自定义模板</el-button
                >
              </div>
            </div>
          </el-header>

          <el-main>
            <div>
              <div class="textareaDiv">
                <div class="textareaCon">
                  <span style="font-size: 18px; margin-bottom: 10px"
                    >内容设置</span
                  ><br />
                  <span>状态：</span>
                  <el-switch
                    v-model="switchValue"
                    active-color="#13ce66"
                    inactive-color="#ff4949"
                  >
                  </el-switch>
                </div>
              </div>
              <div class="textareaDiv">
                <div class="textareaCon">
                  <div
                    style="margin-top: 20px; margin-bottom: 20px; height: 10px"
                  >
                    <el-form>
                      <el-form-item label="按钮" :label-width="formLabelWidth">
                        <template>
                          <el-button
                            class="textareaNou"
                            v-for="(nounId, index) in SMSnounIds"
                            :key="index"
                            @click="textareaAdd(nounId)"
                            type="blue"
                            size="small"
                            >{{ nounId.name }}</el-button
                          >
                        </template>
                      </el-form-item>
                      <el-form-item :label-width="formLabelWidth">
                        <div></div>
                        <div
                          id="SMSContentTextArea"
                          class="infoText"
                          ref="SMSContentTextArea"
                          :contenteditable="true"
                          v-html="SMSContent"
                          @input="btnHandelClick"
                        ></div>
                      </el-form-item>
                      <el-form-item :label-width="formLabelWidth">
                        <div class="textareaCon">
                          <span>短信预览：</span>
                          <div
                            class="infoText"
                            v-html="SMSPreview"
                            :contenteditable="true"
                            style="pointer-events: none"
                          ></div>
                        </div>
                      </el-form-item>
                      <el-form-item :label-width="formLabelWidth">
                        <div class="careBtn">
                          <el-button
                            type="primary"
                            @click="SaveContent"
                            icon="el-icon-folder-checked"
                            >立即保存</el-button
                          >
                        </div></el-form-item
                      >
                    </el-form>
                    <br />
                    <div></div>
                  </div>
                  <!-- <div class="textareaCon">
                  <span>预览：</span>
                  <div class="infoText" v-html="SMSPreview"></div>
                </div> -->
                </div>
              </div>
            </div>
          </el-main>
        </el-container>
      </el-container>
      <el-dialog
        title="新增短信模板"
        :visible.sync="SMSdialogVisible"
        width="80%"
      >
        <div style="max-height: 800px; overflow-y: auto; position: static">
          <div :style="{ height: dialogHeight }" style="margin-right: 20px">
            <div class="textareaDiv">
              <div class="textareaCon">
                <div
                  style="margin-top: 20px; margin-bottom: 20px; height: 10px"
                >
                  <el-form>
                    <el-form-item label-width="80px">
                      <div>
                        <span>模板名称：</span>
                        <el-input
                          v-model="AddTemplateList.sms_Name"
                          placeholder="请输入模板名称"
                          style="width: 240px"
                        ></el-input>
                      </div>
                    </el-form-item>
                    <el-form-item label-width="80px">
                      <div>
                        <span>状态：</span>
                        <el-select v-model="AddTemplateList.state" size="small">
                          <el-option label="启用" value="T"></el-option>
                          <el-option label="禁用" value="F"></el-option>
                        </el-select>
                      </div>
                    </el-form-item>
                    <el-form-item label-width="80px">
                      <template>
                        <el-button
                          class="textareaNou"
                          v-for="(nounId, index) in SMSnounIds"
                          :key="index"
                          @click="textareaDialogAdd(nounId)"
                          type="blue"
                          size="small"
                          >{{ nounId.name }}</el-button
                        >
                      </template>
                    </el-form-item>
                    <el-form-item label-width="80px">
                      <div></div>
                      <div
                        id="SMSContentTextAreadialog"
                        class="infoText"
                        ref="SMSContentTextAreadialog"
                        :contenteditable="true"
                        v-html="DialogContent"
                        @input="btnHandelDialogClick"
                       
                      ></div>
                    </el-form-item>
                    <el-form-item label-width="80px">
                      <div class="textareaCon">
                        <span>短信预览：</span>
                        <div
                          class="infoText"
                          v-html="DialogPreview"
                          :contenteditable="true"
                          style="pointer-events: none"
                        ></div>
                      </div>
                    </el-form-item>
                  </el-form>
                </div>
              </div>
            </div>
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="SMSdialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="AddTemplate"
            >确 定</el-button
          >
        </span>
      </el-dialog>
    </div>
  </div>
</template>
  
  <script>
import { ajax } from "../../common";
// import{Toast} from 'vant';
import apiUrls from "../../config/apiUrls";
export default {
  name: "SMSTemplate",
  data() {
    return {
      ids: "", //id集合 用于批量删除或单个删除
      height: "calc( 100vh - 250px)",
      formLabelWidth: "240",
      tableData: [], //表数据
      tableCopyTableList: [], //表数据集合
      tableConstData: [], //存放数据 用于筛选数据
      index: 1, //当前页数
      size: 50, //页码
      dialogVisible: false, //新增编辑对话框是否显示
      dialogTitle: "", //对话框的标题
      loading: false,
      num: 0,
      //单位模型
      SMSTemplateList: {
        id: "",
        sms_Code: "",
        sms_Name: "",
        sms_Content: "",
        Preview: "",
        nounId: "",
        state: "",
        parameter: "",
        updateTime: "",
      },
      switchValue: false,
      SMSPreview: "",
      SMSNounList: [],
      SMSnounIds: [],
      SMSContent: "",
      SMSParameter: "",
      spanElementList: "",
      SMSdialogVisible: false,
      dialogHeight: "calc( 100vh - 500px)",
      //单位模型
      AddTemplateList: {
        sms_Name: "",
        sms_Content: "",
        Preview: "",
        nounId: "",
        state: "",
        parameter: "",
      },
      DialogContent: "",
      DialogPreview: "",
    };
  },
  created() {
    this.GetAllSMSTemplateList();
  },
  mounted() {},
  methods: {
    GetAllSMSTemplateList() {
      var that = this;
      that.loading = false;
      ajax
        .post(apiUrls.GetAllSMSTemplate)
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          // 初始化数据
          that.tableConstData = r.data.returnData.st;
          that.SMSNounList = r.data.returnData.sn;
          this.SMSTemplateTask(that.tableConstData[this.num], this.num);
        })
        .catch((err) => {
          alert("获取短信模板失败,请稍后重试");
        });
    },
    SMSTemplateTask(SMSTemplate, index) {
      this.num = index;
      if (SMSTemplate.state == "T") {
        this.switchValue = true;
      } else {
        this.switchValue = false;
      }
      this.SMSTemplateList = SMSTemplate;
      this.SMSPreview = "";
      this.SMSParameter = SMSTemplate.parameter;
      console.log(!SMSTemplate.nounId);
      if(SMSTemplate.nounId){
        var nounId = SMSTemplate.nounId.split(",");
        var SMSnounIds = [];
        for (let i = 0; i < nounId.length; i++) {
          for (let z = 0; z < this.SMSNounList.length; z++) {
            if (nounId[i] == this.SMSNounList[z].code) {
              SMSnounIds.push(this.SMSNounList[z]);
            }
          }
        }
        this.SMSnounIds = SMSnounIds;
      }
      
      
      this.SMSPreview = this.formatString(
        SMSTemplate.preview,
        SMSTemplate.parameter
      );
      this.SMSContent = this.formatContent(
        this.SMSTemplateList.preview,
        this.SMSTemplateList.parameter
      );
    },
    textareaAdd(nounId) {
      const html = `<span contentEditable="false">${nounId.name}</span>`;
      this.$refs.SMSContentTextArea.focus(); // focus 输入框，否则会跟随光标追加 html
      const sel = window.getSelection();
      // Selection 选区对象，表示用户选择的文本范围或光标的当前位置
      if (sel.getRangeAt && sel.rangeCount) {
        let range = sel.getRangeAt(0); // 包含当前选区内容的区域对象
        range.deleteContents(); // Range.deleteContents(): 移除来自 Document 的 Range 内容
        const el = document.createElement("div"); // 生成一个 div
        el.innerHTML = html; // div 里的内容为上面定义的要添加的元素
        const frag = document.createDocumentFragment();
        let node, lastNode;
        while ((node = el.firstChild)) {
          // 把生成 div 里的内容，即 html，移到 frag 里
          lastNode = frag.appendChild(node);
        }
        range.insertNode(frag);
        if (lastNode) {
          range = range.cloneRange();
          range.setStartAfter(lastNode);
          range.collapse(true);
          sel.removeAllRanges();
          sel.addRange(range);
        }
        this.btnHandelClick();
      }
    },
    getSpanContent(message) {
      // 使用正则表达式提取<span>标签内容
      var regEx = /<span[^>]*>(.*?)<\/span>/g;
      var spanContents = [];
      var match;

      while ((match = regEx.exec(message))) {
        // 获取匹配到的<span>标签内容
        var spanContent = match[1];
        spanContents.push(spanContent);
      }
      let nounIds = [];
      for (let i = 0; i < spanContents.length; i++) {
        for (let z = 0; z < this.SMSnounIds.length; z++) {
          if (spanContents[i] == this.SMSnounIds[z].name) {
            nounIds.push(this.SMSnounIds[z].code);
          }
        }
      }
      return nounIds.join(",");
    },
     removeHtmlTagsAndStyles(htmlString) {
        // 去除HTML标签和样式
        let withoutTagsAndStyles = htmlString.replace(/<[^>]+>|&nbsp;/g, '').trim();
        return withoutTagsAndStyles.replace(/\s{2,}/g, ' ');
    },
    btnHandelClick() {
      let innerHTML = document.getElementById("SMSContentTextArea").innerHTML;
      this.SMSTemplateList.parameter = this.getSpanContent(innerHTML);
      let regex = /<span contenteditable="false">[^<]*<\/span>/g;
      let placeholders = [];
      let previewNoHtml = innerHTML.replace(regex, (match) => {
        placeholders.push(`{${placeholders.length}}`);
        return placeholders[placeholders.length - 1];
      });
      this.SMSTemplateList.preview = this.removeHtmlTagsAndStyles(previewNoHtml);
      // console.log(this.SMSTemplateList.preview);
      this.SMSPreview = this.formatString(
        this.SMSTemplateList.preview,
        this.SMSTemplateList.parameter
      );
    },
    textareaHandle() {
      var index = 0;
      if (!this.SMSTemplateList.parameter) {
        var parameter = this.SMSTemplateList.parameter.split(",");
        for (let i = 0; i < parameter.length; i++) {
        const spanElement = document.getElementById(parameter[i] + i);
        let spanText = spanElement.innerText || spanElement.textContent;
        for (let z = 0; z < this.SMSNounList.length; z++) {
          if (this.SMSNounList[z].code == parameter[i]) {
            if (!spanText.includes(this.SMSNounList[z].name)) {
              if (spanElement !== null && spanElement.parentNode !== null) {
                // 创建新的span标签
                var newSpan = document.createElement("span");
                newSpan.innerHTML = "";

                // 设置新的span标签的ID
                newSpan.setAttribute("id", "newSpanId");
                var Preview = this.SMSTemplateList.preview.split("{" + i + "}");
                this.SMSTemplateList.preview = Preview[0] + Preview[1];
                index = i + 1;
                // 替换原始span标签
                spanElement.parentNode.removeChild(spanElement);
                this.SMSTemplateList.parameter = parameter
                  .filter((item) => item !== parameter[i])
                  .join(",");
                // parameter[i]="newSpanId";
              }
            }
          }
        }
      }
      const SMSContent = document.getElementById("SMSContent").innerHTML;
      var Note = SMSContent;
      parameter = this.SMSTemplateList.parameter.split(",");
      // console.log(parameter);
      for (let i = 0; i < parameter.length; i++) {
        var regex = new RegExp(
          `<span id="${parameter[i] + i}" style="color: blue;">(.*?)<\/span>`,
          "g"
        );
        // console.log("parameter[i] + i}",parameter[i] + i);
        Note = Note.replace(regex, "{" + i + "}");
        // console.log("Note11",Note.replace(regex, "{" + i + "}"));
      }
      this.SMSTemplateList.preview = Note;
      this.SMSPreview = this.formatString(
        this.SMSTemplateList.preview,
        this.SMSTemplateList.parameter
      );
      }else{
        this.SMSPreview=this.SMSTemplateList.preview;
      }
      
      
    },
    formatString(formatted, parameter) {
      if (parameter) {
        var parameterList = parameter.split(",");
        var args = [];
        for (let i = 0; i < parameterList.length; i++) {
          for (let z = 0; z < this.SMSNounList.length; z++) {
            if (parameterList[i] == this.SMSNounList[z].code) {
              args.push(
                "<span id=" +
                  this.SMSNounList[z].code +
                  " style='color: blue;'>" +
                  this.SMSNounList[z].smSdefault +
                  "</span>"
              );
            }
          }
        }
        for (let i = 0; i < args.length; i++) {
          let regexp = new RegExp("\\{" + i + "\\}", "gi");
          formatted = formatted.replace(regexp, args[i]);
        }
      }
      return formatted;
    },
    formatContent(formatted, parameter) {
      if (parameter) {
        var parameterList = parameter.split(",");
      var args = [];
      // var spanElement = [];
      for (let i = 0; i < parameterList.length; i++) {
        for (let z = 0; z < this.SMSNounList.length; z++) {
          if (parameterList[i] == this.SMSNounList[z].code) {
            args.push(
              "<span contentEditable='false'>" +
                this.SMSNounList[z].name +
                "</span>"
            );
            // spanElement.push(this.SMSNounList[z].code +i);
          }
        }
      }
      for (let i = 0; i < args.length; i++) {
        let regexp = new RegExp("\\{" + i + "\\}", "gi");
        formatted = formatted.replace(regexp, args[i]);
      }
      }
      

      // this.spanElementList=spanElement.join(",");
      return formatted;
    },
    SaveContent() {
      if (this.switchValue == true) {
        this.SMSTemplateList.state = "T";
      } else {
        this.SMSTemplateList.state = "F";
      }
      var pData = {
        sMSTemplate: this.SMSTemplateList,
      };
      ajax
        .post(apiUrls.UpdateSMSTemplate, pData)
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          this.$message.success("操作成功");
          // this.dialogVisible = false; //成功后关闭对话框
          // this.GetLncList(); //重新加载
          this.GetAllSMSTemplateList();
        })
        .catch((err) => {
          console.log(err);
          this.$message.error("系统繁忙！请稍后再试");
        });
    },
    showSMSdialogVisible() {
      this.DialogContent = this.SMSContent;
      this.DialogPreview = this.SMSPreview;
      this.AddTemplateList.state = "F";
      this.AddTemplateList.NounId = this.SMSTemplateList.nounId;
      this.AddTemplateList.parameter = this.SMSTemplateList.parameter;
      this.AddTemplateList.preview = this.SMSTemplateList.preview;
      this.AddTemplateList.application = this.SMSTemplateList.application;
      this.AddTemplateList.appid = this.SMSTemplateList.appid;
      this.SMSdialogVisible = true;
    },
    btnHandelDialogClick() {
      let innerHTML = document.getElementById(
        "SMSContentTextAreadialog"
      ).innerHTML;
      this.AddTemplateList.parameter = this.getSpanContent(innerHTML);
      let regex = /<span contenteditable="false">[^<]*<\/span>/g;
      let placeholders = [];
      this.AddTemplateList.preview = innerHTML.replace(regex, (match) => {
        placeholders.push(`{${placeholders.length}}`);
        return placeholders[placeholders.length - 1];
      });
      this.DialogPreview = this.formatString(
        this.AddTemplateList.preview,
        this.AddTemplateList.parameter
      );
    },
    textareaDialogAdd(nounId) {
      const html = `<span contentEditable="false">${nounId.name}</span>`;
      this.$refs.SMSContentTextAreadialog.focus(); // focus 输入框，否则会跟随光标追加 html
      const sel = window.getSelection();
      // Selection 选区对象，表示用户选择的文本范围或光标的当前位置
      if (sel.getRangeAt && sel.rangeCount) {
        let range = sel.getRangeAt(0); // 包含当前选区内容的区域对象
        range.deleteContents(); // Range.deleteContents(): 移除来自 Document 的 Range 内容
        const el = document.createElement("div"); // 生成一个 div
        el.innerHTML = html; // div 里的内容为上面定义的要添加的元素
        const frag = document.createDocumentFragment();
        let node, lastNode;
        while ((node = el.firstChild)) {
          // 把生成 div 里的内容，即 html，移到 frag 里
          lastNode = frag.appendChild(node);
        }
        range.insertNode(frag);
        if (lastNode) {
          range = range.cloneRange();
          range.setStartAfter(lastNode);
          range.collapse(true);
          sel.removeAllRanges();
          sel.addRange(range);
        }
        this.btnHandelDialogClick();
      }
    },
    verifyTemplate() {
      if (!this.AddTemplateList.sms_Name) {
        this.$message.warning("请输入模板名称");
        return false;
      }
      if (!this.AddTemplateList.state) {
        this.$message.warning("请选择模板状态");
        return false;
      }
      if (!this.AddTemplateList.preview) {
        this.$message.warning("未获取到消息模板");
        return false;
      }
      if (!this.AddTemplateList.parameter) {
        this.$message.warning("未获取到消息模板");
        return false;
      }
      if (!this.AddTemplateList.NounId) {
        this.$message.warning("未获取到消息模板");
        return false;
      }
      return true;
    },
    AddTemplate(){
      //参数验证
      if (!this.verifyTemplate()) {
        return;
      }
      var pData = {
        sMSTemplate: this.AddTemplateList,
      };
      ajax
        .post(apiUrls.AddSMSTemplate, pData)
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          this.$message.success("操作成功");
          // this.dialogVisible = false; //成功后关闭对话框
          // this.GetLncList(); //重新加载
          this.SMSdialogVisible = false;
          this.GetAllSMSTemplateList();
        })
        .catch((err) => {
          console.log(err);
          this.$message.error("系统繁忙！请稍后再试");
        });
    },
    SMSTemplateTaskSelect(val){
      if (val==this.SMSTemplateList.id) {
        return "SMSTemplateTaskSelectDiv";
      }
    }
  },
};
</script>
  
  <style lang="scss">
.SMSDiv {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 96%;
  margin: 20px auto;
  font-size: 16px;
}
.SMSDiv .el-header {
  // background-color: #b3c0d1;
  color: #333;
  line-height: 60px;
}

.SMSDiv .el-aside {
  color: #333;
}
.asideDiv {
  -webkit-appearance: none;
  background-color: #fff;
  background-image: none;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  box-sizing: border-box;
  color: #606266;
  display: inline-block;
  font-size: inherit;
  height: 45px;
  line-height: 40px;
  outline: none;
  padding: 0 15px;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  width: 100%;
  margin-bottom: 20px;
  font-size: 17px;
  text-align: center;
  line-height: 45px;
}
.textareaDiv {
  width: 100%;
}
.textareaDiv .textareaCon {
  margin-top: 20px;
  width: 100%;
  display: block;
  font-size: 16px;
  margin-left: 0px;
  margin-right: auto;
}
.textareaCon textarea {
  font-size: 16px;
}
.textareaCon .infoText {
  height: 156px;
  display: block;
  resize: vertical;
  padding: 5px 15px;
  line-height: 1.5;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
  font-size: inherit;
  color: #606266;
  background-color: #fff;
  background-image: none;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  // max-height: 156px;
  // height: 156px;
  // overflow-y: auto; /* 允许内容垂直滚动 */
  // overflow-x: hidden; /* 隐藏水平滚动条 */
  // white-space: nowrap; /* 防止内容自动换行 */
}
.infoText span{
  pointer-events: none;
}
.infoText span[contenteditable="false"] {
  pointer-events: none;
  user-select: none;
}
.textareaCon .textareaNou {
  // width: 150px;
  // height: 50px;

  font-size: 16px;
  background-color: #dddddd;
  margin-right: 5px;
  margin-top: 5px;
  float: left;
}

.box {
  width: 228px;
  height: 150px;

  /*给div添加可以调整控制盒子大小的属性*/
  resize: both;
  overflow: auto;

  border: 1px solid #a0b3d6;

  display: inline-block;
  box-sizing: border-box;

  /*控制转中时外边框的颜色*/
  outline-color: #000;

  font-size: 12px;
}
.careBtn {
  margin-top: 20px;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  height: 10%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-right: 35px;
}
#SMSContentTextArea {
  font-size: 16px;
}
#SMSContentTextAreadialog {
  font-size: 16px;
}
.el-form-item__label {
  margin-top: 10px;
  font-size: 16px;
}
.el-container {
  margin-left: 20px;
}
.SMSTemplateTaskSelectDiv{
  background-color: #018bf0;
}
</style>
  