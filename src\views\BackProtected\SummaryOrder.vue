<template>
  <div>
    <div class="lncDiv">
      <div style="width: 100%">信息汇总</div>
      <div class="lncMid">
        <div class="topCard">
          <div class="contentCard">
            全局号源
            <img src="../../assets/img/team.png" alt="" style="width:3rem;height:3rem;">
          </div>
          <div class="contentCard" style="background-color: #42cac0">
            订单汇总
            <img src="../../assets/img/iconOrder.png" alt="" style="width:3rem;height:3rem;">
          </div>
        </div>
        <el-row>
          <el-col :span="12">
            <div class="grid-content bg-purple">
              <div class="title">
                <div class="block">
                  <span class="demonstration">选择日期</span>
                  <el-date-picker v-model="checktime" type="date" @change="handleDateChange" :clearable="false"
                    placeholder="选择日期">
                  </el-date-picker>
                </div>
              </div>

              <div class="title">
                {{ $dayjs(checktime).format('YYYY-MM-DD') }}已预约体检人数{{ teamlistcont }}（其中上午{{ teammorningCont }}人，下午{{
                  teamafternoon }}人）
              </div>

              <el-table :data="tableData" :span-method="objectSpanMethod" stripe border style="width: 100%">
                <el-table-column prop="startTime" label="" width="50">
                </el-table-column>
                <el-table-column prop="team_Sum" :resizable="false" label="预留号源">
                </el-table-column>
                <el-table-column prop="team_Already" :resizable="false" label="已约"> </el-table-column>
                <el-table-column prop="team_Surplus" :resizable="false" label="剩余">
                </el-table-column>
              </el-table>
              <br>

            </div>
            <div class="perContentCard" style="background-color: #42ca0c;">
              入职及其他号源
              <img src="../../assets/img/SummaryPerson.png" alt="" style="width:3rem;height:3rem;">
            </div>
            <div class="grid-content bg-purple-light">
              <div>
                <div class="title">
                  {{ $dayjs(checktime).format('YYYY-MM-DD') }}已预约体检人数{{ personlistcont }}（其中上午{{ personmorningCont
                  }}人，下午{{ personafternoon }}人）
                </div>
                <el-table :data="tableDataGj" :span-method="objectSpanMethodGj" stripe border style="width: 100%">
                  <el-table-column prop="startTime" label="" width="50">
                  </el-table-column>

                  <el-table-column prop="person_Type" :resizable="false" label="体检类型" width="180">
                    <template slot-scope="scope">
                      {{ Persontype(scope.row.person_Type) }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="person_Sum" :resizable="false" label="预留号源">
                  </el-table-column>
                  <el-table-column prop="person_Already" :resizable="false" label="已约">
                  </el-table-column>
                  <el-table-column prop="person_Surplus" :resizable="false" label="剩余">
                  </el-table-column>
                </el-table>
                <br>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="grid-content bg-purple-light">
              <div>
                <div class="title">
                  {{ $dayjs(checktime).format('YYYY-MM-DD') }}已预约体检人数{{ ordersonlistcont }}人）
                </div>
                <el-table :data="tableDataOrder" stripe border style="width: 100%">
                  <el-table-column prop="type" :resizable="false" label="体检类型">
                    <template slot-scope="scope">
                      {{ Ordersontype(scope.row.type) }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="quantity" :resizable="false" label="订单数(人)">
                  </el-table-column>
                </el-table>
                <br>
              </div>
            </div>
            <div class="perContentCard" style="background-color: #00A5D5;">
              彩超汇总
              <img src="../../assets/img/iconReport.png" alt="" style="width:3rem;height:3rem;">
            </div>
            <div class="grid-content bg-purple-light">
              <div>
                <div class="title">
                  {{ $dayjs(checktime).format('YYYY-MM-DD') }}已预约彩超项目共{{ combsonlistcont }}人）
                </div>
                <el-table :data="tableDataComb" stripe border style="width: 100%">
                  <!-- <el-table-column prop="startTime" label="" width="50">
                  </el-table-column> -->

                  <el-table-column prop="comb_Name" :resizable="false" label="项目名称">
                    <!-- <template slot-scope="scope">
                      {{ Persontype(scope.row.person_Type) }}
                    </template> -->
                  </el-table-column>
                  <el-table-column prop="quantity" :resizable="false" label="订单数(人)">
                  </el-table-column>
                  <!-- <el-table-column prop="person_Already" :resizable="false" label="已约">
                  </el-table-column>
                  <el-table-column prop="person_Surplus" :resizable="false" label="剩余">
                  </el-table-column> -->
                </el-table>
                <br>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import { ajax } from "../../common/ajax";
import apiUrls from "../../config/apiUrls";
export default {
  name: "Lnclist",
  data() {
    return {
      checktime: new Date(),
      tableData: [],
      lncList: [],
      tableDataGj: [],
      teamlistcont: 0,//预约总人数
      teamafternoon: 0,//下午预约人数
      teammorningCont: 0,//上午预约人数
      personlistcont: 0,
      personafternoon: 0,
      personmorningCont: 0,
      spanArr: [], // 记录每一行的合并数
      personspanArr: [], // 记录每一行的合并数
      pos: 0, // 记录索引
      posperson: 0, // 记录索引
      pieData: [],//饼图数据
      chartPie: null,//团检饼图
      chartPiePerson: null,//个检饼图
      teampieData: [],
      pieDataPerson: [],//个检饼图数据
      personpieData: [],
      tableDataOrder: [],
      ordersonlistcont: 0,
      ordersonspanArr: [], // 记录每一行的合并数
      tableDataComb: [],
      combsonlistcont: 0,
    };
  },
  created() {
    this.GetGroupSum();
    this.GetPersonSum();
    this.GetOrderList();
    this.GetOrderListByContainsComb();
    // this.$nextTick(() => {
    //   this.drawPie();
    //   this.drawPiePerson();
    // })
  },
  mounted() {
    this.spanArr = [];
    this.getSpanArr(this.tableData, "1");
    this.personspanArr = [];
    this.getSpanArr(this.tableDataGj, "2");
  },
  watch: {
    //团检预约数
    tableData: function (val, odlval) {
      let num = 0;
      this.teamlistcont = 0;
      this.teammorningCont = 0;
      this.teamafternoon = 0;
      for (let i = 0; i < this.tableData.length; i++) {
        num += this.tableData[i].team_Already
        this.teamlistcont = num
        if (this.tableData[i].startTime == "上午") {
          this.teammorningCont += this.tableData[i].team_Already
        }
        if (this.tableData[i].startTime == "下午") {
          this.teamafternoon += this.tableData[i].team_Already
        }
      }
    },
    //个检预约数
    tableDataGj: function (val, odlval) {
      let num = 0;
      this.personmorningCont = 0;
      this.personafternoon = 0;
      this.personlistcont = 0
      for (let i = 0; i < this.tableDataGj.length; i++) {
        num += this.tableDataGj[i].person_Already
        this.personlistcont = num
        if (this.tableDataGj[i].startTime == "上午") {
          this.personmorningCont += this.tableDataGj[i].person_Already
        }
        if (this.tableDataGj[i].startTime == "下午") {
          this.personafternoon += this.tableDataGj[i].person_Already
        }
      }
    },
    tableDataOrder: function () {
      let num = 0;
      this.ordersonlistcont = 0
      for (let i = 0; i < this.tableDataOrder.length; i++) {
        num += parseInt(this.tableDataOrder[i].quantity);
      }
      this.ordersonlistcont = num;
    },
    tableDataComb: function () {
      let num = 0;
      this.combsonlistcont = 0;
      for (let i = 0; i < this.tableDataComb.length; i++) {
        num += parseInt(this.tableDataComb[i].quantity);
      }
      this.combsonlistcont = num;

    }
  },
  methods: {
    handleDateChange() {
      this.GetGroupSum();
      this.GetPersonSum();
      this.GetOrderList();
      this.GetOrderListByContainsComb();
    },
    // 团检合并单元格
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        const rows = this.spanArr[rowIndex];
        const cols = rows > 0 ? 1 : 0;
        return {
          rowspan: rows,
          colspan: cols,
        };
      }
    },
    //合并单元格
    objectSpanMethodGj({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        const rows = this.personspanArr[rowIndex];
        const cols = rows > 0 ? 1 : 0;
        return {
          rowspan: rows,
          colspan: cols,
        };
      }
    },
    //合并单元格
    objectSpanMethodOrder({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        const rows = this.ordersonspanArr[rowIndex];
        const cols = rows > 0 ? 1 : 0;
        return {
          rowspan: rows,
          colspan: cols,
        };
      }
    },
    //体检类型
    Persontype(typecode) {
      let typename = '';
      switch (typecode) {
        case "PersonalIndex":
          typename = "个人体检";
          break;
        case "Health":
          typename = "健康证体检";
          break;
        case "staff":
          typename = "入职体检";
          break;
        case "vehicle":
          typename = "驾驶证体检";
          break;
        case "Twocancer":
          typename = "两癌筛选";
          break;
        case "Civil":
          typename = "公务员体检";
          break;
        case "Personalized":
          typename = "个性化套餐";
          break;
        default:
          break;
      }
      return typename;
    },
    //体检类型
    Ordersontype(typecode) {
      let typename = '';
      switch (typecode) {
        case "person":
          typename = "个人套餐";
          break;
        case "staff":
          typename = "入职及其他";
          break;
        case "group":
          typename = "团体体检";
          break;
        case "UD":
          typename = "区干部体检";
          break;
        default:
          typename = "未识别";
          break;
      }
      return typename;
    },
    //单位名称
    lcnName(lcncore) {
      let lcnname = '';
      this.lncList.map(r => {
        if (lcncore == r.lnc_Code) {
          lcnname = r.lnc_Name
        }
      })
      return lcnname
    },
    //查询团检号源详情
    GetGroupSum() {
      let pData = {
        team_Date: this.$dayjs(this.checktime).format("YYYY-MM-DD")
      };
      ajax.post(apiUrls.GetGroupSum, pData, { nocrypt: true }).then((r) => {
        if (!r.data.success) {
          alert(r.data.returnMsg);
          return;
        }
        this.tableData = r.data.returnData
        // this.lncList = r.data.returnData.lnclist
        this.getSpanArr(this.tableData, "1");
      })
    },
    //个人号源
    GetPersonSum() {
      let pData = {
        team_Date: this.$dayjs(this.checktime).format("YYYY-MM-DD")
      };
      ajax.post(apiUrls.GetPersonSum, pData, { nocrypt: true }).then((r) => {
        if (!r.data.success) {
          alert(r.data.returnMsg);
          return;
        }
        this.tableDataGj = r.data.returnData
        this.getSpanArr(this.tableDataGj, "2");
      })
    },
    //订单
    GetOrderList() {
      let pData = {
        team_Date: this.$dayjs(this.checktime).format("YYYY-MM-DD")
      };
      ajax.post(apiUrls.GetOrderList, pData, { nocrypt: true }).then((r) => {
        if (!r.data.success) {
          alert(r.data.returnMsg);
          return;
        }
        this.tableDataOrder = r.data.returnData;
        this.getPbArr(this.tableDataOrder, "1");
      })
    },
    //彩超订单
    GetOrderListByContainsComb() {
      let pData = {
        team_Date: this.$dayjs(this.checktime).format("YYYY-MM-DD")
      };
      ajax.post(apiUrls.GetOrderListByContainsComb, pData, { nocrypt: true }).then((r) => {
        if (!r.data.success) {
          alert(r.data.returnMsg);
          return;
        }
        this.tableDataComb = r.data.returnData;
        this.getPbArr(this.tableDataComb, "2");
      })
    },
    //合并行数
    getSpanArr(data, type) {
      if (type == "1") {
        this.spanArr = []
        for (let i = 0; i < data.length; i++) {
          if (i === 0) {
            this.spanArr.push(1);
            this.pos = 0;
          } else {
            if (data[i].startTime === data[i - 1].startTime) {
              this.spanArr[this.pos] += 1;
              this.spanArr.push(0);
            } else {
              this.spanArr.push(1);
              this.pos = i;
            }
          }
        }
      }
      if (type == "2") {
        this.personspanArr = []
        for (let i = 0; i < data.length; i++) {
          if (i === 0) {
            this.personspanArr.push(1);
            this.posperson = 0;
          } else {
            if (data[i].startTime === data[i - 1].startTime) {
              this.personspanArr[this.posperson] += 1;
              this.personspanArr.push(0);
            } else {
              this.personspanArr.push(1);
              this.posperson = i;
            }
          }
        }
      }
      // if (type == "3") {
      //   this.ordersonspanArr = []
      //   for (let i = 0; i < data.length; i++) {
      //     if (i === 0) {
      //       this.ordersonspanArr.push(1);
      //       this.posperson = 0;
      //     } else {
      //       if (data[i].startTime === data[i - 1].startTime) {
      //         this.ordersonspanArr[this.posperson] += 1;
      //         this.ordersonspanArr.push(0);
      //       } else {
      //         this.ordersonspanArr.push(1);
      //         this.posperson = i;
      //       }
      //     }
      //   }
      // }
    },
    getPbArr(data, type) {
      let num = 0;
      if (type == "1") {
        this.ordersonlistcont = 0
        for (let i = 0; i < data.length; i++) {
          num += parseInt(data[i].quantity);
        }
        this.ordersonlistcont = num;
      }
      if (type == "2") {
        this.combsonlistcont = 0;
        for (let i = 0; i < data.length; i++) {
          num += parseInt(data[i].quantity);
        }
        this.combsonlistcont = num;
      }
    },
    //团检饼状图
    drawPie() {
      if (
        this.chartPie != null &&
        this.chartPie != "" &&
        this.chartPie != undefined
      ) {
        this.chartPie.dispose(); //销毁
      }
      this.pieData = [];
      this.teampieData = [];
      let pData = {
        team_Date: this.$dayjs(this.checktime).format("YYYY-MM-DD")
      };
      ajax.post(apiUrls.GetGroupSum, pData, { nocrypt: true }).then((r) => {
        if (!r.data.success) {
          alert(r.data.returnMsg);
          return;
        }
        let already = 0;
        let Surplus = 0;
        let arr = r.data.returnData.orderlist
        for (let i = 0; i < arr.length; i++) {
          already += arr[i].team_Already
          Surplus += arr[i].team_Surplus
        }
        this.pieData.push({
          name: "已约",
          value: already,
        }, {
          name: "剩余",
          value: Surplus
        })
        this.teampieData.push({ already: already, Surplus: Surplus })
      }).catch(err => {
        this.$message.error("系统繁忙");
      })

      this.chartPie = this.$echarts.init(document.getElementById('chartPie'));
      setTimeout(() => {
        // 指定图表的配置项和数据
        var option = {
          legend: {
            type: "plain",
            top: "10%"
          },
          tooltip: {
            trigger: "item",
          },
          series: [{
            type: 'pie',
            data: JSON.parse(JSON.stringify(this.pieData)),
            radius: '50%',
            label: {
              show: true,
              fontWeight: 300,
              fontSize: 14, //文字的字体大小
              formatter: "{b}" + ":" + "{d}%",
            },
          }]
        };
        this.chartPie.setOption(option);
      }, 200);


    },
    //个检饼状图
    drawPiePerson() {
      if (
        this.chartPiePerson != null &&
        this.chartPiePerson != "" &&
        this.chartPiePerson != undefined
      ) {
        this.chartPiePerson.dispose(); //销毁
      }
      this.pieDataPerson = []
      this.personpieData = []
      let pData = {
        team_Date: this.$dayjs(this.checktime).format("YYYY-MM-DD")
      };
      ajax.post(apiUrls.GetPersonSum, pData, { nocrypt: true }).then((r) => {
        if (!r.data.success) {
          alert(r.data.returnMsg);
          return;
        }
        let already = 0;
        let Surplus = 0;
        let arr = r.data.returnData
        for (let i = 0; i < arr.length; i++) {
          already += arr[i].person_Already
          Surplus += arr[i].person_Surplus
        }
        this.pieDataPerson.push({
          name: "已约",
          value: already,
        }, {
          name: "剩余",
          value: Surplus
        })

        this.personpieData.push({ already: already, Surplus: Surplus })
      })
      this.chartPiePerson = this.$echarts.init(document.getElementById('chartPiePerson'));
      setTimeout(() => {
        // 指定图表的配置项和数据
        var option = {
          legend: {
            type: "plain",
            top: "10%"
          },
          tooltip: {
            trigger: "item",
          },
          series: [{
            type: 'pie',
            data: JSON.parse(JSON.stringify(this.pieDataPerson)),
            radius: '50%',
            label: {
              show: true,
              fontWeight: 300,
              fontSize: 14, //文字的字体大小
              formatter: "{b}" + ":" + "{d}%",
            },
          }]
        };
        this.chartPiePerson.setOption(option);
      }, 200);
    },
  },

};
</script>

<style lang="scss">
.lncDiv {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 96%;
  margin: 20px auto;

  .lncTop {
    width: 100%;
    margin-top: 10px;
  }

  .lncMid {
    margin-top: 20px;
    width: 100%;

    .pageNation {
      margin-top: 10px;

      .el-pagination {
        display: flex;
        justify-content: flex-end;
      }
    }
  }
}

.el-row {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.el-col {
  border-radius: 4px;
  border: 1px solid #dddee1;
  background: #ffffff;
}

.bg-purple-dark {
  background: #99a9bf;
}

.bg-purple {
  background: #ffffff;
}

.bg-purple-light {
  background: #ffffff;
}

.grid-content {
  border-radius: 4px;
  min-height: 36px;
  margin: 1rem;
}

.row-bg {
  padding: 10px 0;
  background-color: #f9fafc;
}

.title {
  border: 1px solid #f0f0f0;
  margin-bottom: 1rem;
  padding: 0.3rem;
  font-weight: 700;
}

.topCard {
  display: flex;

  .contentCard {
    width: 50%;
    background-color: #53bae2;
    //padding: 1.2rem;
    color: #ffffff;
    font-size: 1.2rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.8rem;
  }
}

.perContentCard {
  width: 100%;
  background-color: #53bae2;
  //padding: 1.2rem;
  color: #ffffff;
  font-size: 1.2rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.8rem;
}
</style>
