import Vue from 'vue'
import VueRouter from 'vue-router'

Vue.use(VueRouter)
const fixedRouter=[
  {
    path: '/login',
    name: 'login',
    component: (resolve) => require(['../views/Login/login.vue'], resolve)
  }
]

//这个是解决多次点击跳转同一个页面报错的BUG；
const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err)
}

export default new VueRouter({
  routes: fixedRouter
})