const temporaryData =[
    {
        "arr_code":null,
        "person_Date":"2019-11-01",//体检日期
        "shift_no":7,//星期
        "person_Sum":50,//号源数
        "person_Already":0,//已约人数
        "person_Surplus":0,//剩余号源
        "add_num":0,
        "person_Flag":"T"//是否休假（T表示是休假）
    },
    {
        "arr_code":null,
        "person_Date":"2019-11-02",
        "shift_no":1,
        "person_Sum":50,
        "person_Already":49,
         "person_Surplus":0,
        "add_num":0,
        "person_Flag":""
    },
    {
        "arr_code":null,
        "person_Date":"2019-11-03",
        "shift_no":2,
        "person_Sum":0,
        "person_Already":0,
         "person_Surplus":0,
        "add_num":0,
        "person_Flag":""
    },
    {
        "arr_code":null,
        "person_Date":"2019-11-04",
        "shift_no":3,
        "person_Sum":75,
        "person_Already":70,
        "person_Surplus":5,
        "add_num":0,
        "person_Flag":""
    },
    {
        "arr_code":null,
        "person_Date":"2019-11-05",
        "shift_no":4,
        "person_Sum":100,
        "person_Already":96,
        "person_Surplus":4,
        "add_num":0,
        "person_Flag":""
    },
    {
        "arr_code":null,
        "person_Date":"2019-11-06",
        "shift_no":5,
        "person_Sum":100,
        "person_Already":100,
        "person_Surplus":0,
        "add_num":0,
        "person_Flag":""
    },
    {
        "arr_code":null,
        "person_Date":"2019-11-07",
        "shift_no":6,
        "person_Sum":70,
        "person_Already":70,
        "person_Surplus":0,
        "add_num":0,
        "person_Flag":"T"
    },
    {
        "arr_code":null,
        "person_Date":"2019-11-08",
        "shift_no":7,
        "person_Sum":70,
        "person_Already":69,
        "person_Surplus":1,
        "add_num":0,
        "person_Flag":"T"
    },
    {
        "arr_code":null,
        "person_Date":"2019-11-09",
        "shift_no":1,
        "person_Sum":77,
        "person_Already":77,
        "person_Surplus":0,
        "add_num":0,
        "person_Flag":""
    },
    {
        "arr_code":null,
        "person_Date":"2019-11-10",
        "shift_no":2,
        "person_Sum":0,
        "person_Already":0,
        "person_Surplus":0,
        "add_num":0,
        "person_Flag":""
    },
    {
        "arr_code":null,
        "person_Date":"2019-11-11",
        "shift_no":3,
        "person_Sum":83,
        "person_Already":83,
        "person_Surplus":0,
        "add_num":0,
        "person_Flag":""
    },
    {
        "arr_code":null,
        "person_Date":"2019-11-12",
        "shift_no":null,
        "person_Sum":55,
        "person_Already":55,
        "person_Surplus":0,
        "add_num":0,
        "person_Flag":""
    },
    {
        "arr_code":null,
        "person_Date":"2019-11-13",
        "shift_no":null,
        "person_Sum":55,
        "person_Already":55,
        "person_Surplus":0,
        "add_num":0,
        "person_Flag":""
    },
    {
        "arr_code":null,
        "person_Date":"2019-11-14",
        "shift_no":5,
        "person_Sum":65,
        "person_Already":65,
        "person_Surplus":0,
        "add_num":0,
        "person_Flag":""
    },
    {
        "arr_code":null,
        "person_Date":"2019-11-15",
        "shift_no":null,
        "person_Sum":57,
        "person_Already":56,
        "person_Surplus":1,
        "add_num":0,
        "person_Flag":""
    },
    {
        "arr_code":null,
        "person_Date":"2019-11-16",
        "shift_no":7,
        "person_Sum":55,
        "person_Already":55,
        "person_Surplus":0,
        "add_num":0,
        "person_Flag":"T"
    },
    {
        "arr_code":null,
        "person_Date":"2019-11-17",
        "shift_no":1,
        "person_Sum":0,
        "person_Already":0,
        "person_Surplus":0,
        "add_num":0,
        "person_Flag":""
    },
    {
        "arr_code":null,
        "person_Date":"2019-11-18",
        "shift_no":null,
        "person_Sum":55,
        "person_Already":55,
        "person_Surplus":0,
        "add_num":0,
        "person_Flag":""
    },
    {
        "arr_code":null,
        "person_Date":"2019-11-19",
        "shift_no":null,
        "person_Sum":70,
        "person_Already":70,
        "person_Surplus":0,
        "add_num":0,
        "person_Flag":""
    },
    {
        "arr_code":null,
        "person_Date":"2019-11-20",
        "shift_no":null,
        "person_Sum":80,
        "person_Already":76,
        "person_Surplus":4,
        "add_num":0,
        "person_Flag":""
    },
    {
        "arr_code":null,
        "person_Date":"2019-11-21",
        "shift_no":4,
        "person_Sum":85,
        "person_Already":80,
        "person_Surplus":5,
        "add_num":0,
        "person_Flag":""
    },
    {
        "arr_code":null,
        "person_Date":"2019-11-22",
        "shift_no":6,
        "person_Sum":65,
        "person_Already":65,
        "person_Surplus":0,
        "add_num":0,
        "person_Flag":""
    },
    {
        "arr_code":null,
        "person_Date":"2019-11-23",
        "shift_no":null,
        "person_Sum":62,
        "person_Already":62,
        "person_Surplus":0,
        "add_num":0,
        "person_Flag":""
    },
    {
        "arr_code":null,
        "person_Date":"2019-11-24",
        "shift_no":null,
        "person_Sum":0,
        "person_Already":0,
        "person_Surplus":0,
        "add_num":0,
        "person_Flag":"T"
    },
    {
        "arr_code":null,
        "person_Date":"2019-11-25",
        "shift_no":null,
        "person_Sum":75,
        "person_Already":67,
        "person_Surplus":8,
        "add_num":0,
        "person_Flag":""
    },
    {
        "arr_code":null,
        "person_Date":"2019-11-26",
        "shift_no":null,
        "person_Sum":64,
        "person_Already":63,
        "person_Surplus":1,
        "add_num":0,
        "person_Flag":""
    },
    {
        "arr_code":null,
        "person_Date":"2019-11-27",
        "shift_no":7,
        "person_Sum":57,
        "person_Already":57,
        "person_Surplus":0,
        "add_num":0,
        "person_Flag":"T"
    },
    {
        "arr_code":null,
        "person_Date":"2019-11-28",
        "shift_no":1,
        "person_Sum":65,
        "person_Already":65,
        "person_Surplus":0,
        "add_num":0,
        "person_Flag":""
    },
    {
        "arr_code":2,
        "person_Date":"2019-11-29",
        "shift_no":2,
        "person_Sum":75,
        "person_Already":72,
        "person_Surplus":0,
        "add_num":0,
        "person_Flag":""
    },
    {
        "arr_code":3,
        "person_Date":"2019-11-30",
        "shift_no":3,
        "person_Sum":82,
        "person_Already":82,
        "person_Surplus":0,
        "add_num":0,
        "person_Flag":""
    },
    {
        "arr_code":3,
        "person_Date":"2019-11-31",
        "shift_no":3,
        "person_Sum":82,
        "person_Already":82,
        "person_Surplus":0,
        "add_num":0,
        "person_Flag":""
    }
]
export default temporaryData;