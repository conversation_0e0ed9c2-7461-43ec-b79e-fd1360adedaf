<template>
  <div>
    <div class="clusDiv">
      <div style="width: 100%">套餐列表</div>
      <div class="clusTop">
        <el-input
          placeholder="套餐名称(为空时默认加载全部)"
          style="width: 240px"
          v-model="ClusList.clus_Name"
          size="small"
        ></el-input>
        <el-button
          type="primary"
          icon="el-icon-search"
          @click="GetNewData"
          style="margin-left: 10px"
          size="small"
          >查询</el-button
        >
        <el-button
          type="success"
          icon="el-icon-plus"
          @click="SyncClus()"
          size="small"
          >同步</el-button
        >
        <el-button
          type="danger"
          icon="el-icon-delete"
          @click="DeleteClus()"
          size="small"
          >批量删除</el-button
        >
      </div>

      <div class="clusMid">
        <el-table
          :data="tableData"
          v-loading="loading"
          element-loading-text="拼命加载中"
          border
          stripe
          :fit="true"
          row-key="id"
          @selection-change="handleSelectionChangePeople"
          :height="height"
        >
          <el-table-column
            type="selection"
            width="55"
            align="center"
            :reserve-selection="true"
          ></el-table-column>
          <el-table-column
            prop="clus_Code"
            label="套餐编码"
            width="200"
            align="center"
            sortable
          ></el-table-column>
          <el-table-column
            prop="clus_Name"
            label="套餐名称"
            align="center"
            sortable
          ></el-table-column>
          <el-table-column
            prop="clus_Note"
            label="套餐简介"
            width="100"
            align="center"
            sortable
          ></el-table-column>
          <el-table-column
            prop="price"
            label="价格"
            align="center"
            sortable
          ></el-table-column>
          <el-table-column
            prop="clus_sex"
            label="性别"
            align="center"
            sortable
          ></el-table-column>
          <el-table-column
            prop="clusType"
            label="分类"
            width="100"
            align="center"
            sortable
          ></el-table-column>
          <el-table-column
            prop="state"
            label="状态"
            align="center"
            sortable
          ></el-table-column>
          <el-table-column label="操作" width="300" align="center">
            <template slot-scope="scope">
              <el-button
                @click="showAddorEditDialog(scope.row)"
                type="primary"
                plain
                >编辑</el-button
              >
              <el-button @click="DeleteClus(scope.row.id)" type="danger" plain
                >删除</el-button
              >
              <!--单个删除-->
            </template>
          </el-table-column>
        </el-table>
        <div class="pageNation">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="index"
            :page-sizes="[50, 100, 200, 300, 500]"
            :page-size="size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableCopyTableList.length"
          ></el-pagination>
        </div>

        <!--新增/编辑对话框-->
        <el-dialog :title="dialogTitle" :visible.sync="dialogVisible">
          <el-form :label-position="'right'" label-width="80px">
            <el-form-item label="套餐编码">
              <el-input
                v-model="ClusList.clus_Code"
                placeholder="请输入套餐编码"
                size="small"
                readonly=""
              ></el-input>
            </el-form-item>
            <el-form-item label="套餐名称">
              <el-input
                v-model="ClusList.clus_Name"
                placeholder="请输入套餐名称"
                size="small"
              ></el-input>
            </el-form-item>
            <el-form-item label="套餐价格">
              <el-input
                v-model="ClusList.price"
                placeholder="请输入套餐价格"
                size="small"
                readonly
              ></el-input>
            </el-form-item>
            <el-form-item label="性别">
              <el-input
                v-model="ClusList.clus_sex"
                placeholder="请输入性别"
                size="small"
                readonly
              ></el-input>
            </el-form-item>
            <el-form-item label="分类">
              <el-input
                v-model="ClusList.clusType"
                placeholder="请输入分类"
                size="small"
                readonly
              ></el-input>
            </el-form-item>
            <el-form-item label="是否启用">
              <el-select v-model="ClusList.state" size="small">
                <el-option label="启用" value="T"></el-option>
                <el-option label="禁用" value="F"></el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <div slot="footer">
            <el-button type="primary" size="small" @click="addOrEditClus"
              >确定</el-button
            >
            <el-button type="danger" size="small" @click="dialogVisible = false"
              >取消</el-button
            >
          </div>
        </el-dialog>
        <el-drawer
          title="我是标题"
          :visible.sync="drawer"
          :direction="direction"
          :before-close="handleClose">
          <span>我来啦!</span>
        </el-drawer>
      </div>
    </div>
  </div>
</template>

<script>
import { ajax } from "../../common";
// import{Toast} from 'vant';
import apiUrls from "../../config/apiUrls";
export default {
  name: "Cluslist",
  data() {
    return {
      ids: "", //id集合 用于批量删除或单个删除
      height: "calc( 100vh - 250px)",
      tableData: [], //表数据
      tableCopyTableList: [], //表数据集合
      tableConstData: [], //存放数据 用于筛选数据
      index: 1, //当前页数
      size: 50, //页码
      dialogVisible: false, //新增编辑对话框是否显示
      dialogTitle: "", //对话框的标题
      loading: false,
      //套餐模型
      ClusList: {
        id: "",
        clus_Code: "",
        clus_Name: "",
        state: "",
      },
      drawer: false,
      direction: 'rtl',
    };
  },
  created() {
    this.GetClusList();
  },
  methods: {
    //获取选中行id
    handleSelectionChangePeople(rows) {
      console.log("row", rows);
      this.ids = rows.map((row) => row.id);
    },
    //显示模态框
    showAddorEditDialog(row) {
      if (row == undefined) {
        this.dialogTitle = "新增套餐";
      } else {
        this.dialogTitle = "查看/编辑套餐";
      }
      this.ClusList.id = row ? row.id : "";
      this.ClusList.clus_Code = row ? row.clus_Code : "";
      this.ClusList.clus_Name = row ? row.clus_Name : "";
      this.ClusList.clusType = row ? row.clusType : "";
      this.ClusList.clus_sex = row ? row.clus_sex : "";
      this.ClusList.price = row ? row.price : "";
      this.ClusList.clus_Note = row ? row.clus_Note : "";
      this.ClusList.state = (row ? row.state : "启用") == "启用" ? "T" : "F";
      this.dialogVisible = true;
      // this.drawer = true;
    },
    // 页数改变事件
    handleSizeChange(size) {
      this.size = size;
      this.tableData = this.paging(size, this.index);
    },
    // 页码改变事件
    handleCurrentChange(current) {
      this.index = current;
      this.tableData = this.paging(this.size, current);
    },
    // 本地分页的方法
    paging(size, current) {
      const tableList = JSON.parse(JSON.stringify(this.tableCopyTableList));
      const tablePush = [];
      tableList.forEach((item, index) => {
        if (size * (current - 1) <= index && index <= size * current - 1) {
          tablePush.push(item);
        }
      });
      return tablePush;
    },
    //获取所有套餐信息
    GetClusList() {
      var that = this;
      that.loading = false;
      ajax
        .post(apiUrls.GetClusList)
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }

          // 初始化数据
          that.tableConstData = r.data.returnData;
          that.tableCopyTableList = r.data.returnData.map((val) => {
            switch (val.clus_sex) {
              case "1":
                val.clus_sex = "男";
                break;
              case "0":
                val.clus_sex = "女";
                break;
              case "%":
                val.clus_sex = "不限";
                break;
              default:
                break;
            }
            switch (val.clusType) {
              case "01":
                val.clusType = "健康体检";
                break;
              case "02":
                val.clusType = "职业体检";
                break;
              case "03":
                val.clusType = "从业体检";
                break;
              case "04":
                val.clusType = "招工体检";
                break;
              case "05":
                val.clusType = "学生体检";
                break;
              case "06":
                val.clusType = "征兵体检";
                break;
              default:
                break;
            }
            return val;
          });
          that.tableData = that.paging(that.size, that.index);
        })
        .catch((err) => {
          alert("获取套餐失败,请稍后重试");
        });
    },
    SyncClus() {
      this.loading = true;
      ajax.post(apiUrls.SyncClus).then((r) => {
        if (r.data.success) {
          this.$message.success(r.data.returnMsg);

          this.GetClusList();
        }
      });
    },
    //删除套餐
    DeleteClus(ids) {
      let idArr = [];
      if (ids) {
        idArr = [ids];
      } else {
        idArr = this.ids;
      }
      if (idArr.length == 0) {
        this.$message.warning("请选择套餐");
        return;
      }

      this.$confirm("确定删除此套餐吗, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "error",
      }).then(() => {
        let pData = {
          data: {
            ids: idArr,
          },
        };
        ajax
          .post(apiUrls.DeleteById, pData)
          .then((r) => {
            if (!r.data.success) {
              this.$message.error(r.returnMsg);
              return;
            }
            this.$message.success("删除成功");
            this.GetClusList();
          })
          .catch((err) => {
            console.log(err);
            this.$message.error("系统繁忙！请稍后再试");
          });
      });
    },
    //检查输入的参数
    checkAdminInfo() {
      if (!this.ClusList.clus_Code) {
        this.$message.warning("请输入套餐编码");
        return false;
      }
      if (!this.ClusList.clus_Name) {
        this.$message.warning("请输入套餐名称");
        return false;
      }
      if (this.ClusList.clus_sex == "男") {
        this.ClusList.clus_sex = "1";
      } else if (this.ClusList.clus_sex == "女") {
        this.ClusList.clus_sex = "0";
      } else {
        this.ClusList.clus_sex = "%";
      }
      return true;
    },
    //新增或者修改套餐
    addOrEditClus() {
      //参数验证
      if (!this.checkAdminInfo()) {
        return;
      }
      var pData = {
        cluster: this.ClusList,
      };
      ajax
        .post(apiUrls.UpdateClus, pData)
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          this.$message.success("操作成功");
          this.dialogVisible = false; //成功后关闭对话框
          this.GetClusList(); //重新加载
        })
        .catch((err) => {
          console.log(err);
          this.$message.error("系统繁忙！请稍后再试");
        });
    },
    //表格筛选
    GetNewData() {
      this.tableCopyTableList = this.tableConstData.filter((item) => {
        //筛选
        return (
          !this.ClusList.clus_Name ||
          item.clus_Name.includes(this.ClusList.clus_Name)
        );
      });
      this.tableData = this.paging(this.size, this.index);
    },
    handleClose(done) {
        this.$confirm('确认关闭？')
          .then(_ => {
            done();
          })
          .catch(_ => {});
      },
  },
};
</script>

<style lang="scss">
.clusDiv {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 96%;
  margin: 20px auto;
  .clusTop {
    width: 100%;
    margin-top: 10px;
  }
  .clusMid {
    margin-top: 20px;
    width: 100%;
    .pageNation {
      margin-top: 10px;
      .el-pagination {
        display: flex;
        justify-content: flex-end;
      }
    }
  }
}
</style>
