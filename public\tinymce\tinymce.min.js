// 4.9.3 (2019-01-31)
!function(){"use strict";var o=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t]},H=function(n,r){return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return n(r.apply(null,e))}},j=function(e){return function(){return e}},q=function(e){return e};function d(r){for(var o=[],e=1;e<arguments.length;e++)o[e-1]=arguments[e];return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=o.concat(e);return r.apply(null,n)}}var e,t,n,r,i,a,u,s,c,l,f,m,g,p,h,v,b,y=function(n){return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return!n.apply(null,e)}},C=j(!1),x=j(!0),w=C,N=x,E=function(){return S},S=(r={fold:function(e,t){return e()},is:w,isSome:w,isNone:N,getOr:n=function(e){return e},getOrThunk:t=function(e){return e()},getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},getOrNull:function(){return null},getOrUndefined:function(){return undefined},or:n,orThunk:t,map:E,ap:E,each:function(){},bind:E,flatten:E,exists:w,forall:N,filter:E,equals:e=function(e){return e.isNone()},equals_:e,toArray:function(){return[]},toString:j("none()")},Object.freeze&&Object.freeze(r),r),k=function(n){var e=function(){return n},t=function(){return o},r=function(e){return e(n)},o={fold:function(e,t){return t(n)},is:function(e){return n===e},isSome:N,isNone:w,getOr:e,getOrThunk:e,getOrDie:e,getOrNull:e,getOrUndefined:e,or:t,orThunk:t,map:function(e){return k(e(n))},ap:function(e){return e.fold(E,function(e){return k(e(n))})},each:function(e){e(n)},bind:r,flatten:e,exists:r,forall:r,filter:function(e){return e(n)?o:S},equals:function(e){return e.is(n)},equals_:function(e,t){return e.fold(w,function(e){return t(n,e)})},toArray:function(){return[n]},toString:function(){return"some("+n+")"}};return o},A={some:k,none:E,from:function(e){return null===e||e===undefined?S:k(e)}},T=function(t){return function(e){return function(e){if(null===e)return"null";var t=typeof e;return"object"===t&&Array.prototype.isPrototypeOf(e)?"array":"object"===t&&String.prototype.isPrototypeOf(e)?"string":t}(e)===t}},R=T("string"),_=T("object"),D=T("array"),B=T("null"),O=T("boolean"),P=T("function"),L=T("number"),I=(i=Array.prototype.indexOf)===undefined?function(e,t){return X(e,t)}:function(e,t){return i.call(e,t)},M=function(e,t){return-1<I(e,t)},$=function(e,t){for(var n=e.length,r=new Array(n),o=0;o<n;o++){var i=e[o];r[o]=t(i,o,e)}return r},F=function(e,t){for(var n=0,r=e.length;n<r;n++)t(e[n],n,e)},W=function(e,t){for(var n=[],r=[],o=0,i=e.length;o<i;o++){var a=e[o];(t(a,o,e)?n:r).push(a)}return{pass:n,fail:r}},z=function(e,t){for(var n=[],r=0,o=e.length;r<o;r++){var i=e[r];t(i,r,e)&&n.push(i)}return n},U=function(e,t,n){return F(e,function(e){n=t(n,e)}),n},V=function(e,t){for(var n=0,r=e.length;n<r;n++){var o=e[n];if(t(o,n,e))return A.some(o)}return A.none()},K=function(e,t){for(var n=0,r=e.length;n<r;n++)if(t(e[n],n,e))return A.some(n);return A.none()},X=function(e,t){for(var n=0,r=e.length;n<r;++n)if(e[n]===t)return n;return-1},Y=Array.prototype.push,G=function(e,t){return function(e){for(var t=[],n=0,r=e.length;n<r;++n){if(!Array.prototype.isPrototypeOf(e[n]))throw new Error("Arr.flatten item "+n+" was not an array, input: "+e);Y.apply(t,e[n])}return t}($(e,t))},J=function(e,t){for(var n=0,r=e.length;n<r;++n)if(!0!==t(e[n],n,e))return!1;return!0},Q=Array.prototype.slice,Z=function(e,t){return z(e,function(e){return!M(t,e)})},ee=function(e){return 0===e.length?A.none():A.some(e[0])},te=function(e){return 0===e.length?A.none():A.some(e[e.length-1])},ne=P(Array.from)?Array.from:function(e){return Q.call(e)},re="undefined"!=typeof window?window:Function("return this;")(),oe=function(e,t){return function(e,t){for(var n=t!==undefined&&null!==t?t:re,r=0;r<e.length&&n!==undefined&&null!==n;++r)n=n[e[r]];return n}(e.split("."),t)},ie={getOrDie:function(e,t){var n=oe(e,t);if(n===undefined||null===n)throw e+" not available on this browser";return n}},ae=function(){return ie.getOrDie("URL")},ue={createObjectURL:function(e){return ae().createObjectURL(e)},revokeObjectURL:function(e){ae().revokeObjectURL(e)}},se=navigator,ce=se.userAgent,le=function(e){return"matchMedia"in window&&matchMedia(e).matches};g=/Android/.test(ce),u=(u=!(a=/WebKit/.test(ce))&&/MSIE/gi.test(ce)&&/Explorer/gi.test(se.appName))&&/MSIE (\w+)\./.exec(ce)[1],s=-1!==ce.indexOf("Trident/")&&(-1!==ce.indexOf("rv:")||-1!==se.appName.indexOf("Netscape"))&&11,c=-1!==ce.indexOf("Edge/")&&!u&&!s&&12,u=u||s||c,l=!a&&!s&&/Gecko/.test(ce),f=-1!==ce.indexOf("Mac"),m=/(iPad|iPhone)/.test(ce),p="FormData"in window&&"FileReader"in window&&"URL"in window&&!!ue.createObjectURL,h=le("only screen and (max-device-width: 480px)")&&(g||m),v=le("only screen and (min-width: 800px)")&&(g||m),b=-1!==ce.indexOf("Windows Phone"),c&&(a=!1);var fe,de={opera:!1,webkit:a,ie:u,gecko:l,mac:f,iOS:m,android:g,contentEditable:!m||p||534<=parseInt(ce.match(/AppleWebKit\/(\d*)/)[1],10),transparentSrc:"data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",caretAfter:8!==u,range:window.getSelection&&"Range"in window,documentMode:u&&!c?document.documentMode||7:10,fileApi:p,ceFalse:!1===u||8<u,cacheSuffix:null,container:null,overrideViewPort:null,experimentalShadowDom:!1,canHaveCSP:!1===u||11<u,desktop:!h&&!v,windowsPhone:b},me=window.Promise?window.Promise:function(){function r(e,t){return function(){e.apply(t,arguments)}}var e=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)},i=function(e){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof e)throw new TypeError("not a function");this._state=null,this._value=null,this._deferreds=[],l(e,r(o,this),r(u,this))},t=i.immediateFn||"function"==typeof setImmediate&&setImmediate||function(e){setTimeout(e,1)};function a(r){var o=this;null!==this._state?t(function(){var e=o._state?r.onFulfilled:r.onRejected;if(null!==e){var t;try{t=e(o._value)}catch(n){return void r.reject(n)}r.resolve(t)}else(o._state?r.resolve:r.reject)(o._value)}):this._deferreds.push(r)}function o(e){try{if(e===this)throw new TypeError("A promise cannot be resolved with itself.");if(e&&("object"==typeof e||"function"==typeof e)){var t=e.then;if("function"==typeof t)return void l(r(t,e),r(o,this),r(u,this))}this._state=!0,this._value=e,s.call(this)}catch(n){u.call(this,n)}}function u(e){this._state=!1,this._value=e,s.call(this)}function s(){for(var e=0,t=this._deferreds.length;e<t;e++)a.call(this,this._deferreds[e]);this._deferreds=null}function c(e,t,n,r){this.onFulfilled="function"==typeof e?e:null,this.onRejected="function"==typeof t?t:null,this.resolve=n,this.reject=r}function l(e,t,n){var r=!1;try{e(function(e){r||(r=!0,t(e))},function(e){r||(r=!0,n(e))})}catch(o){if(r)return;r=!0,n(o)}}return i.prototype["catch"]=function(e){return this.then(null,e)},i.prototype.then=function(n,r){var o=this;return new i(function(e,t){a.call(o,new c(n,r,e,t))})},i.all=function(){var s=Array.prototype.slice.call(1===arguments.length&&e(arguments[0])?arguments[0]:arguments);return new i(function(o,i){if(0===s.length)return o([]);var a=s.length;function u(t,e){try{if(e&&("object"==typeof e||"function"==typeof e)){var n=e.then;if("function"==typeof n)return void n.call(e,function(e){u(t,e)},i)}s[t]=e,0==--a&&o(s)}catch(r){i(r)}}for(var e=0;e<s.length;e++)u(e,s[e])})},i.resolve=function(t){return t&&"object"==typeof t&&t.constructor===i?t:new i(function(e){e(t)})},i.reject=function(n){return new i(function(e,t){t(n)})},i.race=function(o){return new i(function(e,t){for(var n=0,r=o.length;n<r;n++)o[n].then(e,t)})},i}(),ge=function(e,t){return"number"!=typeof t&&(t=0),setTimeout(e,t)},pe=function(e,t){return"number"!=typeof t&&(t=1),setInterval(e,t)},he=function(t,n){var r,e;return(e=function(){var e=arguments;clearTimeout(r),r=ge(function(){t.apply(this,e)},n)}).stop=function(){clearTimeout(r)},e},ve={requestAnimationFrame:function(e,t){fe?fe.then(e):fe=new me(function(e){t||(t=document.body),function(e,t){var n,r=window.requestAnimationFrame,o=["ms","moz","webkit"];for(n=0;n<o.length&&!r;n++)r=window[o[n]+"RequestAnimationFrame"];r||(r=function(e){window.setTimeout(e,0)}),r(e,t)}(e,t)}).then(e)},setTimeout:ge,setInterval:pe,setEditorTimeout:function(e,t,n){return ge(function(){e.removed||t()},n)},setEditorInterval:function(e,t,n){var r;return r=pe(function(){e.removed?clearInterval(r):t()},n)},debounce:he,throttle:he,clearInterval:function(e){return clearInterval(e)},clearTimeout:function(e){return clearTimeout(e)}},be=/^(?:mouse|contextmenu)|click/,ye={keyLocation:1,layerX:1,layerY:1,returnValue:1,webkitMovementX:1,webkitMovementY:1,keyIdentifier:1},Ce=function(){return!1},xe=function(){return!0},we=function(e,t,n,r){e.addEventListener?e.addEventListener(t,n,r||!1):e.attachEvent&&e.attachEvent("on"+t,n)},Ne=function(e,t,n,r){e.removeEventListener?e.removeEventListener(t,n,r||!1):e.detachEvent&&e.detachEvent("on"+t,n)},Ee=function(e,t){var n,r,o=t||{};for(n in e)ye[n]||(o[n]=e[n]);if(o.target||(o.target=o.srcElement||document),de.experimentalShadowDom&&(o.target=function(e,t){if(e.composedPath){var n=e.composedPath();if(n&&0<n.length)return n[0]}return t}(e,o.target)),e&&be.test(e.type)&&e.pageX===undefined&&e.clientX!==undefined){var i=o.target.ownerDocument||document,a=i.documentElement,u=i.body;o.pageX=e.clientX+(a&&a.scrollLeft||u&&u.scrollLeft||0)-(a&&a.clientLeft||u&&u.clientLeft||0),o.pageY=e.clientY+(a&&a.scrollTop||u&&u.scrollTop||0)-(a&&a.clientTop||u&&u.clientTop||0)}return o.preventDefault=function(){o.isDefaultPrevented=xe,e&&(e.preventDefault?e.preventDefault():e.returnValue=!1)},o.stopPropagation=function(){o.isPropagationStopped=xe,e&&(e.stopPropagation?e.stopPropagation():e.cancelBubble=!0)},!(o.stopImmediatePropagation=function(){o.isImmediatePropagationStopped=xe,o.stopPropagation()})==((r=o).isDefaultPrevented===xe||r.isDefaultPrevented===Ce)&&(o.isDefaultPrevented=Ce,o.isPropagationStopped=Ce,o.isImmediatePropagationStopped=Ce),"undefined"==typeof o.metaKey&&(o.metaKey=!1),o},Se=function(e,t,n){var r=e.document,o={type:"ready"};if(n.domLoaded)t(o);else{var i=function(){return"complete"===r.readyState||"interactive"===r.readyState&&r.body},a=function(){n.domLoaded||(n.domLoaded=!0,t(o))},u=function(){i()&&(Ne(r,"readystatechange",u),a())},s=function(){try{r.documentElement.doScroll("left")}catch(e){return void ve.setTimeout(s)}a()};!r.addEventListener||de.ie&&de.ie<11?(we(r,"readystatechange",u),r.documentElement.doScroll&&e.self===e.top&&s()):i()?a():we(e,"DOMContentLoaded",a),we(e,"load",a)}},ke=function(){var m,g,p,h,v,b=this,y={};g="mce-data-"+(+new Date).toString(32),h="onmouseenter"in document.documentElement,p="onfocusin"in document.documentElement,v={mouseenter:"mouseover",mouseleave:"mouseout"},m=1,b.domLoaded=!1,b.events=y;var C=function(e,t){var n,r,o,i,a=y[t];if(n=a&&a[e.type])for(r=0,o=n.length;r<o;r++)if((i=n[r])&&!1===i.func.call(i.scope,e)&&e.preventDefault(),e.isImmediatePropagationStopped())return};b.bind=function(e,t,n,r){var o,i,a,u,s,c,l,f=window,d=function(e){C(Ee(e||f.event),o)};if(e&&3!==e.nodeType&&8!==e.nodeType){for(e[g]?o=e[g]:(o=m++,e[g]=o,y[o]={}),r=r||e,a=(t=t.split(" ")).length;a--;)c=d,s=l=!1,"DOMContentLoaded"===(u=t[a])&&(u="ready"),b.domLoaded&&"ready"===u&&"complete"===e.readyState?n.call(r,Ee({type:u})):(h||(s=v[u])&&(c=function(e){var t,n;if(t=e.currentTarget,(n=e.relatedTarget)&&t.contains)n=t.contains(n);else for(;n&&n!==t;)n=n.parentNode;n||((e=Ee(e||f.event)).type="mouseout"===e.type?"mouseleave":"mouseenter",e.target=t,C(e,o))}),p||"focusin"!==u&&"focusout"!==u||(l=!0,s="focusin"===u?"focus":"blur",c=function(e){(e=Ee(e||f.event)).type="focus"===e.type?"focusin":"focusout",C(e,o)}),(i=y[o][u])?"ready"===u&&b.domLoaded?n({type:u}):i.push({func:n,scope:r}):(y[o][u]=i=[{func:n,scope:r}],i.fakeName=s,i.capture=l,i.nativeHandler=c,"ready"===u?Se(e,c,b):we(e,s||u,c,l)));return e=i=0,n}},b.unbind=function(e,t,n){var r,o,i,a,u,s;if(!e||3===e.nodeType||8===e.nodeType)return b;if(r=e[g]){if(s=y[r],t){for(i=(t=t.split(" ")).length;i--;)if(o=s[u=t[i]]){if(n)for(a=o.length;a--;)if(o[a].func===n){var c=o.nativeHandler,l=o.fakeName,f=o.capture;(o=o.slice(0,a).concat(o.slice(a+1))).nativeHandler=c,o.fakeName=l,o.capture=f,s[u]=o}n&&0!==o.length||(delete s[u],Ne(e,o.fakeName||u,o.nativeHandler,o.capture))}}else{for(u in s)o=s[u],Ne(e,o.fakeName||u,o.nativeHandler,o.capture);s={}}for(u in s)return b;delete y[r];try{delete e[g]}catch(d){e[g]=null}}return b},b.fire=function(e,t,n){var r;if(!e||3===e.nodeType||8===e.nodeType)return b;for((n=Ee(null,n)).type=t,n.target=e;(r=e[g])&&C(n,r),(e=e.parentNode||e.ownerDocument||e.defaultView||e.parentWindow)&&!n.isPropagationStopped(););return b},b.clean=function(e){var t,n,r=b.unbind;if(!e||3===e.nodeType||8===e.nodeType)return b;if(e[g]&&r(e),e.getElementsByTagName||(e=e.document),e&&e.getElementsByTagName)for(r(e),t=(n=e.getElementsByTagName("*")).length;t--;)(e=n[t])[g]&&r(e);return b},b.destroy=function(){y={}},b.cancel=function(e){return e&&(e.preventDefault(),e.stopImmediatePropagation()),!1}};ke.Event=new ke,ke.Event.bind(window,"ready",function(){});var Te,Ae,Re,_e,De,Be,Oe,Pe,Le,Ie,Me,Fe,ze,Ue,Ve,He,je,qe,$e="sizzle"+-new Date,We=window.document,Ke=0,Xe=0,Ye=Tt(),Ge=Tt(),Je=Tt(),Qe=function(e,t){return e===t&&(Me=!0),0},Ze=typeof undefined,et={}.hasOwnProperty,tt=[],nt=tt.pop,rt=tt.push,ot=tt.push,it=tt.slice,at=tt.indexOf||function(e){for(var t=0,n=this.length;t<n;t++)if(this[t]===e)return t;return-1},ut="[\\x20\\t\\r\\n\\f]",st="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",ct="\\["+ut+"*("+st+")(?:"+ut+"*([*^$|!~]?=)"+ut+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+st+"))|)"+ut+"*\\]",lt=":("+st+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+ct+")*)|.*)\\)|)",ft=new RegExp("^"+ut+"+|((?:^|[^\\\\])(?:\\\\.)*)"+ut+"+$","g"),dt=new RegExp("^"+ut+"*,"+ut+"*"),mt=new RegExp("^"+ut+"*([>+~]|"+ut+")"+ut+"*"),gt=new RegExp("="+ut+"*([^\\]'\"]*?)"+ut+"*\\]","g"),pt=new RegExp(lt),ht=new RegExp("^"+st+"$"),vt={ID:new RegExp("^#("+st+")"),CLASS:new RegExp("^\\.("+st+")"),TAG:new RegExp("^("+st+"|[*])"),ATTR:new RegExp("^"+ct),PSEUDO:new RegExp("^"+lt),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+ut+"*(even|odd|(([+-]|)(\\d*)n|)"+ut+"*(?:([+-]|)"+ut+"*(\\d+)|))"+ut+"*\\)|)","i"),bool:new RegExp("^(?:checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped)$","i"),needsContext:new RegExp("^"+ut+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+ut+"*((?:-\\d)?\\d*)"+ut+"*\\)|)(?=[^-]|$)","i")},bt=/^(?:input|select|textarea|button)$/i,yt=/^h\d$/i,Ct=/^[^{]+\{\s*\[native \w/,xt=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,wt=/[+~]/,Nt=/'|\\/g,Et=new RegExp("\\\\([\\da-f]{1,6}"+ut+"?|("+ut+")|.)","ig"),St=function(e,t,n){var r="0x"+t-65536;return r!=r||n?t:r<0?String.fromCharCode(r+65536):String.fromCharCode(r>>10|55296,1023&r|56320)};try{ot.apply(tt=it.call(We.childNodes),We.childNodes),tt[We.childNodes.length].nodeType}catch(VN){ot={apply:tt.length?function(e,t){rt.apply(e,it.call(t))}:function(e,t){for(var n=e.length,r=0;e[n++]=t[r++];);e.length=n-1}}}var kt=function(e,t,n,r){var o,i,a,u,s,c,l,f,d,m;if((t?t.ownerDocument||t:We)!==ze&&Fe(t),n=n||[],!e||"string"!=typeof e)return n;if(1!==(u=(t=t||ze).nodeType)&&9!==u)return[];if(Ve&&!r){if(o=xt.exec(e))if(a=o[1]){if(9===u){if(!(i=t.getElementById(a))||!i.parentNode)return n;if(i.id===a)return n.push(i),n}else if(t.ownerDocument&&(i=t.ownerDocument.getElementById(a))&&qe(t,i)&&i.id===a)return n.push(i),n}else{if(o[2])return ot.apply(n,t.getElementsByTagName(e)),n;if((a=o[3])&&Ae.getElementsByClassName)return ot.apply(n,t.getElementsByClassName(a)),n}if(Ae.qsa&&(!He||!He.test(e))){if(f=l=$e,d=t,m=9===u&&e,1===u&&"object"!==t.nodeName.toLowerCase()){for(c=Be(e),(l=t.getAttribute("id"))?f=l.replace(Nt,"\\$&"):t.setAttribute("id",f),f="[id='"+f+"'] ",s=c.length;s--;)c[s]=f+Lt(c[s]);d=wt.test(e)&&Ot(t.parentNode)||t,m=c.join(",")}if(m)try{return ot.apply(n,d.querySelectorAll(m)),n}catch(g){}finally{l||t.removeAttribute("id")}}}return Pe(e.replace(ft,"$1"),t,n,r)};function Tt(){var r=[];return function e(t,n){return r.push(t+" ")>Re.cacheLength&&delete e[r.shift()],e[t+" "]=n}}function At(e){return e[$e]=!0,e}function Rt(e,t){var n=t&&e,r=n&&1===e.nodeType&&1===t.nodeType&&(~t.sourceIndex||1<<31)-(~e.sourceIndex||1<<31);if(r)return r;if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}function _t(t){return function(e){return"input"===e.nodeName.toLowerCase()&&e.type===t}}function Dt(n){return function(e){var t=e.nodeName.toLowerCase();return("input"===t||"button"===t)&&e.type===n}}function Bt(a){return At(function(i){return i=+i,At(function(e,t){for(var n,r=a([],e.length,i),o=r.length;o--;)e[n=r[o]]&&(e[n]=!(t[n]=e[n]))})})}function Ot(e){return e&&typeof e.getElementsByTagName!==Ze&&e}for(Te in Ae=kt.support={},De=kt.isXML=function(e){var t=e&&(e.ownerDocument||e).documentElement;return!!t&&"HTML"!==t.nodeName},Fe=kt.setDocument=function(e){var t,s=e?e.ownerDocument||e:We,n=s.defaultView;return s!==ze&&9===s.nodeType&&s.documentElement?(Ue=(ze=s).documentElement,Ve=!De(s),n&&n!==function(e){try{return e.top}catch(t){}return null}(n)&&(n.addEventListener?n.addEventListener("unload",function(){Fe()},!1):n.attachEvent&&n.attachEvent("onunload",function(){Fe()})),Ae.attributes=!0,Ae.getElementsByTagName=!0,Ae.getElementsByClassName=Ct.test(s.getElementsByClassName),Ae.getById=!0,Re.find.ID=function(e,t){if(typeof t.getElementById!==Ze&&Ve){var n=t.getElementById(e);return n&&n.parentNode?[n]:[]}},Re.filter.ID=function(e){var t=e.replace(Et,St);return function(e){return e.getAttribute("id")===t}},Re.find.TAG=Ae.getElementsByTagName?function(e,t){if(typeof t.getElementsByTagName!==Ze)return t.getElementsByTagName(e)}:function(e,t){var n,r=[],o=0,i=t.getElementsByTagName(e);if("*"===e){for(;n=i[o++];)1===n.nodeType&&r.push(n);return r}return i},Re.find.CLASS=Ae.getElementsByClassName&&function(e,t){if(Ve)return t.getElementsByClassName(e)},je=[],He=[],Ae.disconnectedMatch=!0,He=He.length&&new RegExp(He.join("|")),je=je.length&&new RegExp(je.join("|")),t=Ct.test(Ue.compareDocumentPosition),qe=t||Ct.test(Ue.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,r=t&&t.parentNode;return e===r||!(!r||1!==r.nodeType||!(n.contains?n.contains(r):e.compareDocumentPosition&&16&e.compareDocumentPosition(r)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},Qe=t?function(e,t){if(e===t)return Me=!0,0;var n=!e.compareDocumentPosition-!t.compareDocumentPosition;return n||(1&(n=(e.ownerDocument||e)===(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!Ae.sortDetached&&t.compareDocumentPosition(e)===n?e===s||e.ownerDocument===We&&qe(We,e)?-1:t===s||t.ownerDocument===We&&qe(We,t)?1:Ie?at.call(Ie,e)-at.call(Ie,t):0:4&n?-1:1)}:function(e,t){if(e===t)return Me=!0,0;var n,r=0,o=e.parentNode,i=t.parentNode,a=[e],u=[t];if(!o||!i)return e===s?-1:t===s?1:o?-1:i?1:Ie?at.call(Ie,e)-at.call(Ie,t):0;if(o===i)return Rt(e,t);for(n=e;n=n.parentNode;)a.unshift(n);for(n=t;n=n.parentNode;)u.unshift(n);for(;a[r]===u[r];)r++;return r?Rt(a[r],u[r]):a[r]===We?-1:u[r]===We?1:0},s):ze},kt.matches=function(e,t){return kt(e,null,null,t)},kt.matchesSelector=function(e,t){if((e.ownerDocument||e)!==ze&&Fe(e),t=t.replace(gt,"='$1']"),Ae.matchesSelector&&Ve&&(!je||!je.test(t))&&(!He||!He.test(t)))try{var n=(void 0).call(e,t);if(n||Ae.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(VN){}return 0<kt(t,ze,null,[e]).length},kt.contains=function(e,t){return(e.ownerDocument||e)!==ze&&Fe(e),qe(e,t)},kt.attr=function(e,t){(e.ownerDocument||e)!==ze&&Fe(e);var n=Re.attrHandle[t.toLowerCase()],r=n&&et.call(Re.attrHandle,t.toLowerCase())?n(e,t,!Ve):undefined;return r!==undefined?r:Ae.attributes||!Ve?e.getAttribute(t):(r=e.getAttributeNode(t))&&r.specified?r.value:null},kt.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},kt.uniqueSort=function(e){var t,n=[],r=0,o=0;if(Me=!Ae.detectDuplicates,Ie=!Ae.sortStable&&e.slice(0),e.sort(Qe),Me){for(;t=e[o++];)t===e[o]&&(r=n.push(o));for(;r--;)e.splice(n[r],1)}return Ie=null,e},_e=kt.getText=function(e){var t,n="",r=0,o=e.nodeType;if(o){if(1===o||9===o||11===o){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=_e(e)}else if(3===o||4===o)return e.nodeValue}else for(;t=e[r++];)n+=_e(t);return n},(Re=kt.selectors={cacheLength:50,createPseudo:At,match:vt,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(Et,St),e[3]=(e[3]||e[4]||e[5]||"").replace(Et,St),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||kt.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&kt.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return vt.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&pt.test(n)&&(t=Be(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(Et,St).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=Ye[e+" "];return t||(t=new RegExp("(^|"+ut+")"+e+"("+ut+"|$)"))&&Ye(e,function(e){return t.test("string"==typeof e.className&&e.className||typeof e.getAttribute!==Ze&&e.getAttribute("class")||"")})},ATTR:function(n,r,o){return function(e){var t=kt.attr(e,n);return null==t?"!="===r:!r||(t+="","="===r?t===o:"!="===r?t!==o:"^="===r?o&&0===t.indexOf(o):"*="===r?o&&-1<t.indexOf(o):"$="===r?o&&t.slice(-o.length)===o:"~="===r?-1<(" "+t+" ").indexOf(o):"|="===r&&(t===o||t.slice(0,o.length+1)===o+"-"))}},CHILD:function(m,e,t,g,p){var h="nth"!==m.slice(0,3),v="last"!==m.slice(-4),b="of-type"===e;return 1===g&&0===p?function(e){return!!e.parentNode}:function(e,t,n){var r,o,i,a,u,s,c=h!==v?"nextSibling":"previousSibling",l=e.parentNode,f=b&&e.nodeName.toLowerCase(),d=!n&&!b;if(l){if(h){for(;c;){for(i=e;i=i[c];)if(b?i.nodeName.toLowerCase()===f:1===i.nodeType)return!1;s=c="only"===m&&!s&&"nextSibling"}return!0}if(s=[v?l.firstChild:l.lastChild],v&&d){for(u=(r=(o=l[$e]||(l[$e]={}))[m]||[])[0]===Ke&&r[1],a=r[0]===Ke&&r[2],i=u&&l.childNodes[u];i=++u&&i&&i[c]||(a=u=0)||s.pop();)if(1===i.nodeType&&++a&&i===e){o[m]=[Ke,u,a];break}}else if(d&&(r=(e[$e]||(e[$e]={}))[m])&&r[0]===Ke)a=r[1];else for(;(i=++u&&i&&i[c]||(a=u=0)||s.pop())&&((b?i.nodeName.toLowerCase()!==f:1!==i.nodeType)||!++a||(d&&((i[$e]||(i[$e]={}))[m]=[Ke,a]),i!==e)););return(a-=p)===g||a%g==0&&0<=a/g}}},PSEUDO:function(e,i){var t,a=Re.pseudos[e]||Re.setFilters[e.toLowerCase()]||kt.error("unsupported pseudo: "+e);return a[$e]?a(i):1<a.length?(t=[e,e,"",i],Re.setFilters.hasOwnProperty(e.toLowerCase())?At(function(e,t){for(var n,r=a(e,i),o=r.length;o--;)e[n=at.call(e,r[o])]=!(t[n]=r[o])}):function(e){return a(e,0,t)}):a}},pseudos:{not:At(function(e){var r=[],o=[],u=Oe(e.replace(ft,"$1"));return u[$e]?At(function(e,t,n,r){for(var o,i=u(e,null,r,[]),a=e.length;a--;)(o=i[a])&&(e[a]=!(t[a]=o))}):function(e,t,n){return r[0]=e,u(r,null,n,o),!o.pop()}}),has:At(function(t){return function(e){return 0<kt(t,e).length}}),contains:At(function(t){return t=t.replace(Et,St),function(e){return-1<(e.textContent||e.innerText||_e(e)).indexOf(t)}}),lang:At(function(n){return ht.test(n||"")||kt.error("unsupported lang: "+n),n=n.replace(Et,St).toLowerCase(),function(e){var t;do{if(t=Ve?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(t=t.toLowerCase())===n||0===t.indexOf(n+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var t=window.location&&window.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===Ue},focus:function(e){return e===ze.activeElement&&(!ze.hasFocus||ze.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:function(e){return!1===e.disabled},disabled:function(e){return!0===e.disabled},checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!Re.pseudos.empty(e)},header:function(e){return yt.test(e.nodeName)},input:function(e){return bt.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){var t;return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:Bt(function(){return[0]}),last:Bt(function(e,t){return[t-1]}),eq:Bt(function(e,t,n){return[n<0?n+t:n]}),even:Bt(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:Bt(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:Bt(function(e,t,n){for(var r=n<0?n+t:n;0<=--r;)e.push(r);return e}),gt:Bt(function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e})}}).pseudos.nth=Re.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})Re.pseudos[Te]=_t(Te);for(Te in{submit:!0,reset:!0})Re.pseudos[Te]=Dt(Te);function Pt(){}function Lt(e){for(var t=0,n=e.length,r="";t<n;t++)r+=e[t].value;return r}function It(a,e,t){var u=e.dir,s=t&&"parentNode"===u,c=Xe++;return e.first?function(e,t,n){for(;e=e[u];)if(1===e.nodeType||s)return a(e,t,n)}:function(e,t,n){var r,o,i=[Ke,c];if(n){for(;e=e[u];)if((1===e.nodeType||s)&&a(e,t,n))return!0}else for(;e=e[u];)if(1===e.nodeType||s){if((r=(o=e[$e]||(e[$e]={}))[u])&&r[0]===Ke&&r[1]===c)return i[2]=r[2];if((o[u]=i)[2]=a(e,t,n))return!0}}}function Mt(o){return 1<o.length?function(e,t,n){for(var r=o.length;r--;)if(!o[r](e,t,n))return!1;return!0}:o[0]}function Ft(e,t,n,r,o){for(var i,a=[],u=0,s=e.length,c=null!=t;u<s;u++)(i=e[u])&&(n&&!n(i,r,o)||(a.push(i),c&&t.push(u)));return a}function zt(m,g,p,h,v,e){return h&&!h[$e]&&(h=zt(h)),v&&!v[$e]&&(v=zt(v,e)),At(function(e,t,n,r){var o,i,a,u=[],s=[],c=t.length,l=e||function(e,t,n){for(var r=0,o=t.length;r<o;r++)kt(e,t[r],n);return n}(g||"*",n.nodeType?[n]:n,[]),f=!m||!e&&g?l:Ft(l,u,m,n,r),d=p?v||(e?m:c||h)?[]:t:f;if(p&&p(f,d,n,r),h)for(o=Ft(d,s),h(o,[],n,r),i=o.length;i--;)(a=o[i])&&(d[s[i]]=!(f[s[i]]=a));if(e){if(v||m){if(v){for(o=[],i=d.length;i--;)(a=d[i])&&o.push(f[i]=a);v(null,d=[],o,r)}for(i=d.length;i--;)(a=d[i])&&-1<(o=v?at.call(e,a):u[i])&&(e[o]=!(t[o]=a))}}else d=Ft(d===t?d.splice(c,d.length):d),v?v(null,t,d,r):ot.apply(t,d)})}function Ut(e){for(var r,t,n,o=e.length,i=Re.relative[e[0].type],a=i||Re.relative[" "],u=i?1:0,s=It(function(e){return e===r},a,!0),c=It(function(e){return-1<at.call(r,e)},a,!0),l=[function(e,t,n){return!i&&(n||t!==Le)||((r=t).nodeType?s(e,t,n):c(e,t,n))}];u<o;u++)if(t=Re.relative[e[u].type])l=[It(Mt(l),t)];else{if((t=Re.filter[e[u].type].apply(null,e[u].matches))[$e]){for(n=++u;n<o&&!Re.relative[e[n].type];n++);return zt(1<u&&Mt(l),1<u&&Lt(e.slice(0,u-1).concat({value:" "===e[u-2].type?"*":""})).replace(ft,"$1"),t,u<n&&Ut(e.slice(u,n)),n<o&&Ut(e=e.slice(n)),n<o&&Lt(e))}l.push(t)}return Mt(l)}Pt.prototype=Re.filters=Re.pseudos,Re.setFilters=new Pt,Be=kt.tokenize=function(e,t){var n,r,o,i,a,u,s,c=Ge[e+" "];if(c)return t?0:c.slice(0);for(a=e,u=[],s=Re.preFilter;a;){for(i in n&&!(r=dt.exec(a))||(r&&(a=a.slice(r[0].length)||a),u.push(o=[])),n=!1,(r=mt.exec(a))&&(n=r.shift(),o.push({value:n,type:r[0].replace(ft," ")}),a=a.slice(n.length)),Re.filter)!(r=vt[i].exec(a))||s[i]&&!(r=s[i](r))||(n=r.shift(),o.push({value:n,type:i,matches:r}),a=a.slice(n.length));if(!n)break}return t?a.length:a?kt.error(e):Ge(e,u).slice(0)},Oe=kt.compile=function(e,t){var n,h,v,b,y,r,o=[],i=[],a=Je[e+" "];if(!a){for(t||(t=Be(e)),n=t.length;n--;)(a=Ut(t[n]))[$e]?o.push(a):i.push(a);(a=Je(e,(h=i,b=0<(v=o).length,y=0<h.length,r=function(e,t,n,r,o){var i,a,u,s=0,c="0",l=e&&[],f=[],d=Le,m=e||y&&Re.find.TAG("*",o),g=Ke+=null==d?1:Math.random()||.1,p=m.length;for(o&&(Le=t!==ze&&t);c!==p&&null!=(i=m[c]);c++){if(y&&i){for(a=0;u=h[a++];)if(u(i,t,n)){r.push(i);break}o&&(Ke=g)}b&&((i=!u&&i)&&s--,e&&l.push(i))}if(s+=c,b&&c!==s){for(a=0;u=v[a++];)u(l,f,t,n);if(e){if(0<s)for(;c--;)l[c]||f[c]||(f[c]=nt.call(r));f=Ft(f)}ot.apply(r,f),o&&!e&&0<f.length&&1<s+v.length&&kt.uniqueSort(r)}return o&&(Ke=g,Le=d),l},b?At(r):r))).selector=e}return a},Pe=kt.select=function(e,t,n,r){var o,i,a,u,s,c="function"==typeof e&&e,l=!r&&Be(e=c.selector||e);if(n=n||[],1===l.length){if(2<(i=l[0]=l[0].slice(0)).length&&"ID"===(a=i[0]).type&&Ae.getById&&9===t.nodeType&&Ve&&Re.relative[i[1].type]){if(!(t=(Re.find.ID(a.matches[0].replace(Et,St),t)||[])[0]))return n;c&&(t=t.parentNode),e=e.slice(i.shift().value.length)}for(o=vt.needsContext.test(e)?0:i.length;o--&&(a=i[o],!Re.relative[u=a.type]);)if((s=Re.find[u])&&(r=s(a.matches[0].replace(Et,St),wt.test(i[0].type)&&Ot(t.parentNode)||t))){if(i.splice(o,1),!(e=r.length&&Lt(i)))return ot.apply(n,r),n;break}}return(c||Oe(e,l))(r,t,!Ve,n,wt.test(e)&&Ot(t.parentNode)||t),n},Ae.sortStable=$e.split("").sort(Qe).join("")===$e,Ae.detectDuplicates=!!Me,Fe(),Ae.sortDetached=!0;var Vt=Array.isArray,Ht=function(e,t,n){var r,o;if(!e)return 0;if(n=n||e,e.length!==undefined){for(r=0,o=e.length;r<o;r++)if(!1===t.call(n,e[r],r,e))return 0}else for(r in e)if(e.hasOwnProperty(r)&&!1===t.call(n,e[r],r,e))return 0;return 1},jt=function(e,t,n){var r,o;for(r=0,o=e.length;r<o;r++)if(t.call(n,e[r],r,e))return r;return-1},qt={isArray:Vt,toArray:function(e){var t,n,r=e;if(!Vt(e))for(r=[],t=0,n=e.length;t<n;t++)r[t]=e[t];return r},each:Ht,map:function(n,r){var o=[];return Ht(n,function(e,t){o.push(r(e,t,n))}),o},filter:function(n,r){var o=[];return Ht(n,function(e,t){r&&!r(e,t,n)||o.push(e)}),o},indexOf:function(e,t){var n,r;if(e)for(n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1},reduce:function(e,t,n,r){var o=0;for(arguments.length<3&&(n=e[0]);o<e.length;o++)n=t.call(r,n,e[o],o);return n},findIndex:jt,find:function(e,t,n){var r=jt(e,t,n);return-1!==r?e[r]:undefined},last:function(e){return e[e.length-1]}},$t=/^\s*|\s*$/g,Wt=function(e){return null===e||e===undefined?"":(""+e).replace($t,"")},Kt=function(e,t){return t?!("array"!==t||!qt.isArray(e))||typeof e===t:e!==undefined},Xt=function(e,n,r,o){o=o||this,e&&(r&&(e=e[r]),qt.each(e,function(e,t){if(!1===n.call(o,e,t,r))return!1;Xt(e,n,r,o)}))},Yt={trim:Wt,isArray:qt.isArray,is:Kt,toArray:qt.toArray,makeMap:function(e,t,n){var r;for(t=t||",","string"==typeof(e=e||[])&&(e=e.split(t)),n=n||{},r=e.length;r--;)n[e[r]]={};return n},each:qt.each,map:qt.map,grep:qt.filter,inArray:qt.indexOf,hasOwn:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},extend:function(e,t){for(var n,r,o,i=[],a=2;a<arguments.length;a++)i[a-2]=arguments[a];var u,s=arguments;for(n=1,r=s.length;n<r;n++)for(o in t=s[n])t.hasOwnProperty(o)&&(u=t[o])!==undefined&&(e[o]=u);return e},create:function(e,t,n){var r,o,i,a,u,s=this,c=0;if(e=/^((static) )?([\w.]+)(:([\w.]+))?/.exec(e),i=e[3].match(/(^|\.)(\w+)$/i)[2],!(o=s.createNS(e[3].replace(/\.\w+$/,""),n))[i]){if("static"===e[2])return o[i]=t,void(this.onCreate&&this.onCreate(e[2],e[3],o[i]));t[i]||(t[i]=function(){},c=1),o[i]=t[i],s.extend(o[i].prototype,t),e[5]&&(r=s.resolve(e[5]).prototype,a=e[5].match(/\.(\w+)$/i)[1],u=o[i],o[i]=c?function(){return r[a].apply(this,arguments)}:function(){return this.parent=r[a],u.apply(this,arguments)},o[i].prototype[i]=o[i],s.each(r,function(e,t){o[i].prototype[t]=r[t]}),s.each(t,function(e,t){r[t]?o[i].prototype[t]=function(){return this.parent=r[t],e.apply(this,arguments)}:t!==i&&(o[i].prototype[t]=e)})),s.each(t["static"],function(e,t){o[i][t]=e})}},walk:Xt,createNS:function(e,t){var n,r;for(t=t||window,e=e.split("."),n=0;n<e.length;n++)t[r=e[n]]||(t[r]={}),t=t[r];return t},resolve:function(e,t){var n,r;for(t=t||window,n=0,r=(e=e.split(".")).length;n<r&&(t=t[e[n]]);n++);return t},explode:function(e,t){return!e||Kt(e,"array")?e:qt.map(e.split(t||","),Wt)},_addCacheSuffix:function(e){var t=de.cacheSuffix;return t&&(e+=(-1===e.indexOf("?")?"?":"&")+t),e}},Gt=document,Jt=Array.prototype.push,Qt=Array.prototype.slice,Zt=/^(?:[^#<]*(<[\w\W]+>)[^>]*$|#([\w\-]*)$)/,en=ke.Event,tn=Yt.makeMap("children,contents,next,prev"),nn=function(e){return void 0!==e},rn=function(e){return"string"==typeof e},on=function(e,t){var n,r,o;for(o=(t=t||Gt).createElement("div"),n=t.createDocumentFragment(),o.innerHTML=e;r=o.firstChild;)n.appendChild(r);return n},an=function(e,t,n,r){var o;if(rn(t))t=on(t,Cn(e[0]));else if(t.length&&!t.nodeType){if(t=pn.makeArray(t),r)for(o=t.length-1;0<=o;o--)an(e,t[o],n,r);else for(o=0;o<t.length;o++)an(e,t[o],n,r);return e}if(t.nodeType)for(o=e.length;o--;)n.call(e[o],t);return e},un=function(e,t){return e&&t&&-1!==(" "+e.className+" ").indexOf(" "+t+" ")},sn=function(e,t,n){var r,o;return t=pn(t)[0],e.each(function(){var e=this;n&&r===e.parentNode||(r=e.parentNode,o=t.cloneNode(!1),e.parentNode.insertBefore(o,e)),o.appendChild(e)}),e},cn=Yt.makeMap("fillOpacity fontWeight lineHeight opacity orphans widows zIndex zoom"," "),ln=Yt.makeMap("checked compact declare defer disabled ismap multiple nohref noshade nowrap readonly selected"," "),fn={"for":"htmlFor","class":"className",readonly:"readOnly"},dn={"float":"cssFloat"},mn={},gn={},pn=function(e,t){return new pn.fn.init(e,t)},hn=/^\s*|\s*$/g,vn=function(e){return null===e||e===undefined?"":(""+e).replace(hn,"")},bn=function(e,t){var n,r,o,i;if(e)if((n=e.length)===undefined){for(r in e)if(e.hasOwnProperty(r)&&(i=e[r],!1===t.call(i,r,i)))break}else for(o=0;o<n&&(i=e[o],!1!==t.call(i,o,i));o++);return e},yn=function(e,n){var r=[];return bn(e,function(e,t){n(t,e)&&r.push(t)}),r},Cn=function(e){return e?9===e.nodeType?e:e.ownerDocument:Gt};pn.fn=pn.prototype={constructor:pn,selector:"",context:null,length:0,init:function(e,t){var n,r,o=this;if(!e)return o;if(e.nodeType)return o.context=o[0]=e,o.length=1,o;if(t&&t.nodeType)o.context=t;else{if(t)return pn(e).attr(t);o.context=t=document}if(rn(e)){if(!(n="<"===(o.selector=e).charAt(0)&&">"===e.charAt(e.length-1)&&3<=e.length?[null,e,null]:Zt.exec(e)))return pn(t).find(e);if(n[1])for(r=on(e,Cn(t)).firstChild;r;)Jt.call(o,r),r=r.nextSibling;else{if(!(r=Cn(t).getElementById(n[2])))return o;if(r.id!==n[2])return o.find(e);o.length=1,o[0]=r}}else this.add(e,!1);return o},toArray:function(){return Yt.toArray(this)},add:function(e,t){var n,r,o=this;if(rn(e))return o.add(pn(e));if(!1!==t)for(n=pn.unique(o.toArray().concat(pn.makeArray(e))),o.length=n.length,r=0;r<n.length;r++)o[r]=n[r];else Jt.apply(o,pn.makeArray(e));return o},attr:function(t,n){var e,r=this;if("object"==typeof t)bn(t,function(e,t){r.attr(e,t)});else{if(!nn(n)){if(r[0]&&1===r[0].nodeType){if((e=mn[t])&&e.get)return e.get(r[0],t);if(ln[t])return r.prop(t)?t:undefined;null===(n=r[0].getAttribute(t,2))&&(n=undefined)}return n}this.each(function(){var e;if(1===this.nodeType){if((e=mn[t])&&e.set)return void e.set(this,n);null===n?this.removeAttribute(t,2):this.setAttribute(t,n,2)}})}return r},removeAttr:function(e){return this.attr(e,null)},prop:function(e,t){var n=this;if("object"==typeof(e=fn[e]||e))bn(e,function(e,t){n.prop(e,t)});else{if(!nn(t))return n[0]&&n[0].nodeType&&e in n[0]?n[0][e]:t;this.each(function(){1===this.nodeType&&(this[e]=t)})}return n},css:function(n,r){var e,o,i=this,t=function(e){return e.replace(/-(\D)/g,function(e,t){return t.toUpperCase()})},a=function(e){return e.replace(/[A-Z]/g,function(e){return"-"+e})};if("object"==typeof n)bn(n,function(e,t){i.css(e,t)});else if(nn(r))n=t(n),"number"!=typeof r||cn[n]||(r=r.toString()+"px"),i.each(function(){var e=this.style;if((o=gn[n])&&o.set)o.set(this,r);else{try{this.style[dn[n]||n]=r}catch(t){}null!==r&&""!==r||(e.removeProperty?e.removeProperty(a(n)):e.removeAttribute(n))}});else{if(e=i[0],(o=gn[n])&&o.get)return o.get(e);if(!e.ownerDocument.defaultView)return e.currentStyle?e.currentStyle[t(n)]:"";try{return e.ownerDocument.defaultView.getComputedStyle(e,null).getPropertyValue(a(n))}catch(u){return undefined}}return i},remove:function(){for(var e,t=this.length;t--;)e=this[t],en.clean(e),e.parentNode&&e.parentNode.removeChild(e);return this},empty:function(){for(var e,t=this.length;t--;)for(e=this[t];e.firstChild;)e.removeChild(e.firstChild);return this},html:function(e){var t,n=this;if(nn(e)){t=n.length;try{for(;t--;)n[t].innerHTML=e}catch(r){pn(n[t]).empty().append(e)}return n}return n[0]?n[0].innerHTML:""},text:function(e){var t,n=this;if(nn(e)){for(t=n.length;t--;)"innerText"in n[t]?n[t].innerText=e:n[0].textContent=e;return n}return n[0]?n[0].innerText||n[0].textContent:""},append:function(){return an(this,arguments,function(e){(1===this.nodeType||this.host&&1===this.host.nodeType)&&this.appendChild(e)})},prepend:function(){return an(this,arguments,function(e){(1===this.nodeType||this.host&&1===this.host.nodeType)&&this.insertBefore(e,this.firstChild)},!0)},before:function(){return this[0]&&this[0].parentNode?an(this,arguments,function(e){this.parentNode.insertBefore(e,this)}):this},after:function(){return this[0]&&this[0].parentNode?an(this,arguments,function(e){this.parentNode.insertBefore(e,this.nextSibling)},!0):this},appendTo:function(e){return pn(e).append(this),this},prependTo:function(e){return pn(e).prepend(this),this},replaceWith:function(e){return this.before(e).remove()},wrap:function(e){return sn(this,e)},wrapAll:function(e){return sn(this,e,!0)},wrapInner:function(e){return this.each(function(){pn(this).contents().wrapAll(e)}),this},unwrap:function(){return this.parent().each(function(){pn(this).replaceWith(this.childNodes)})},clone:function(){var e=[];return this.each(function(){e.push(this.cloneNode(!0))}),pn(e)},addClass:function(e){return this.toggleClass(e,!0)},removeClass:function(e){return this.toggleClass(e,!1)},toggleClass:function(o,i){var e=this;return"string"!=typeof o||(-1!==o.indexOf(" ")?bn(o.split(" "),function(){e.toggleClass(this,i)}):e.each(function(e,t){var n,r;(r=un(t,o))!==i&&(n=t.className,r?t.className=vn((" "+n+" ").replace(" "+o+" "," ")):t.className+=n?" "+o:o)})),e},hasClass:function(e){return un(this[0],e)},each:function(e){return bn(this,e)},on:function(e,t){return this.each(function(){en.bind(this,e,t)})},off:function(e,t){return this.each(function(){en.unbind(this,e,t)})},trigger:function(e){return this.each(function(){"object"==typeof e?en.fire(this,e.type,e):en.fire(this,e)})},show:function(){return this.css("display","")},hide:function(){return this.css("display","none")},slice:function(){return new pn(Qt.apply(this,arguments))},eq:function(e){return-1===e?this.slice(e):this.slice(e,+e+1)},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},find:function(e){var t,n,r=[];for(t=0,n=this.length;t<n;t++)pn.find(e,this[t],r);return pn(r)},filter:function(n){return pn("function"==typeof n?yn(this.toArray(),function(e,t){return n(t,e)}):pn.filter(n,this.toArray()))},closest:function(n){var r=[];return n instanceof pn&&(n=n[0]),this.each(function(e,t){for(;t;){if("string"==typeof n&&pn(t).is(n)){r.push(t);break}if(t===n){r.push(t);break}t=t.parentNode}}),pn(r)},offset:function(e){var t,n,r,o,i=0,a=0;return e?this.css(e):((t=this[0])&&(r=(n=t.ownerDocument).documentElement,t.getBoundingClientRect&&(i=(o=t.getBoundingClientRect()).left+(r.scrollLeft||n.body.scrollLeft)-r.clientLeft,a=o.top+(r.scrollTop||n.body.scrollTop)-r.clientTop)),{left:i,top:a})},push:Jt,sort:[].sort,splice:[].splice},Yt.extend(pn,{extend:Yt.extend,makeArray:function(e){return(t=e)&&t===t.window||e.nodeType?[e]:Yt.toArray(e);var t},inArray:function(e,t){var n;if(t.indexOf)return t.indexOf(e);for(n=t.length;n--;)if(t[n]===e)return n;return-1},isArray:Yt.isArray,each:bn,trim:vn,grep:yn,find:kt,expr:kt.selectors,unique:kt.uniqueSort,text:kt.getText,contains:kt.contains,filter:function(e,t,n){var r=t.length;for(n&&(e=":not("+e+")");r--;)1!==t[r].nodeType&&t.splice(r,1);return t=1===t.length?pn.find.matchesSelector(t[0],e)?[t[0]]:[]:pn.find.matches(e,t)}});var xn=function(e,t,n){var r=[],o=e[t];for("string"!=typeof n&&n instanceof pn&&(n=n[0]);o&&9!==o.nodeType;){if(n!==undefined){if(o===n)break;if("string"==typeof n&&pn(o).is(n))break}1===o.nodeType&&r.push(o),o=o[t]}return r},wn=function(e,t,n,r){var o=[];for(r instanceof pn&&(r=r[0]);e;e=e[t])if(!n||e.nodeType===n){if(r!==undefined){if(e===r)break;if("string"==typeof r&&pn(e).is(r))break}o.push(e)}return o},Nn=function(e,t,n){for(e=e[t];e;e=e[t])if(e.nodeType===n)return e;return null};bn({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return xn(e,"parentNode")},next:function(e){return Nn(e,"nextSibling",1)},prev:function(e){return Nn(e,"previousSibling",1)},children:function(e){return wn(e.firstChild,"nextSibling",1)},contents:function(e){return Yt.toArray(("iframe"===e.nodeName?e.contentDocument||e.contentWindow.document:e).childNodes)}},function(e,r){pn.fn[e]=function(t){var n=[];return this.each(function(){var e=r.call(n,this,t,n);e&&(pn.isArray(e)?n.push.apply(n,e):n.push(e))}),1<this.length&&(tn[e]||(n=pn.unique(n)),0===e.indexOf("parents")&&(n=n.reverse())),n=pn(n),t?n.filter(t):n}}),bn({parentsUntil:function(e,t){return xn(e,"parentNode",t)},nextUntil:function(e,t){return wn(e,"nextSibling",1,t).slice(1)},prevUntil:function(e,t){return wn(e,"previousSibling",1,t).slice(1)}},function(r,o){pn.fn[r]=function(t,e){var n=[];return this.each(function(){var e=o.call(n,this,t,n);e&&(pn.isArray(e)?n.push.apply(n,e):n.push(e))}),1<this.length&&(n=pn.unique(n),0!==r.indexOf("parents")&&"prevUntil"!==r||(n=n.reverse())),n=pn(n),e?n.filter(e):n}}),pn.fn.is=function(e){return!!e&&0<this.filter(e).length},pn.fn.init.prototype=pn.fn,pn.overrideDefaults=function(n){var r,o=function(e,t){return r=r||n(),0===arguments.length&&(e=r.element),t||(t=r.context),new o.fn.init(e,t)};return pn.extend(o,this),o};var En=function(n,r,e){bn(e,function(e,t){n[e]=n[e]||{},n[e][r]=t})};de.ie&&de.ie<8&&(En(mn,"get",{maxlength:function(e){var t=e.maxLength;return 2147483647===t?undefined:t},size:function(e){var t=e.size;return 20===t?undefined:t},"class":function(e){return e.className},style:function(e){var t=e.style.cssText;return 0===t.length?undefined:t}}),En(mn,"set",{"class":function(e,t){e.className=t},style:function(e,t){e.style.cssText=t}})),de.ie&&de.ie<9&&(dn["float"]="styleFloat",En(gn,"set",{opacity:function(e,t){var n=e.style;null===t||""===t?n.removeAttribute("filter"):(n.zoom=1,n.filter="alpha(opacity="+100*t+")")}})),pn.attrHooks=mn,pn.cssHooks=gn;var Sn,kn,Tn,An=function(e,t){var n=function(e,t){for(var n=0;n<e.length;n++){var r=e[n];if(r.test(t))return r}return undefined}(e,t);if(!n)return{major:0,minor:0};var r=function(e){return Number(t.replace(n,"$"+e))};return _n(r(1),r(2))},Rn=function(){return _n(0,0)},_n=function(e,t){return{major:e,minor:t}},Dn={nu:_n,detect:function(e,t){var n=String(t).toLowerCase();return 0===e.length?Rn():An(e,n)},unknown:Rn},Bn="Firefox",On=function(e,t){return function(){return t===e}},Pn=function(e){var t=e.current;return{current:t,version:e.version,isEdge:On("Edge",t),isChrome:On("Chrome",t),isIE:On("IE",t),isOpera:On("Opera",t),isFirefox:On(Bn,t),isSafari:On("Safari",t)}},Ln={unknown:function(){return Pn({current:undefined,version:Dn.unknown()})},nu:Pn,edge:j("Edge"),chrome:j("Chrome"),ie:j("IE"),opera:j("Opera"),firefox:j(Bn),safari:j("Safari")},In="Windows",Mn="Android",Fn="Solaris",zn="FreeBSD",Un=function(e,t){return function(){return t===e}},Vn=function(e){var t=e.current;return{current:t,version:e.version,isWindows:Un(In,t),isiOS:Un("iOS",t),isAndroid:Un(Mn,t),isOSX:Un("OSX",t),isLinux:Un("Linux",t),isSolaris:Un(Fn,t),isFreeBSD:Un(zn,t)}},Hn={unknown:function(){return Vn({current:undefined,version:Dn.unknown()})},nu:Vn,windows:j(In),ios:j("iOS"),android:j(Mn),linux:j("Linux"),osx:j("OSX"),solaris:j(Fn),freebsd:j(zn)},jn=function(e,t){var n=String(t).toLowerCase();return V(e,function(e){return e.search(n)})},qn=function(e,n){return jn(e,n).map(function(e){var t=Dn.detect(e.versionRegexes,n);return{current:e.name,version:t}})},$n=function(e,n){return jn(e,n).map(function(e){var t=Dn.detect(e.versionRegexes,n);return{current:e.name,version:t}})},Wn=function(e,t){return-1!==e.indexOf(t)},Kn=function(e){return e.replace(/^\s+|\s+$/g,"")},Xn=function(e){return e.replace(/\s+$/g,"")},Yn=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Gn=function(t){return function(e){return Wn(e,t)}},Jn=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(e){return Wn(e,"edge/")&&Wn(e,"chrome")&&Wn(e,"safari")&&Wn(e,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Yn],search:function(e){return Wn(e,"chrome")&&!Wn(e,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(e){return Wn(e,"msie")||Wn(e,"trident")}},{name:"Opera",versionRegexes:[Yn,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:Gn("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:Gn("firefox")},{name:"Safari",versionRegexes:[Yn,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(e){return(Wn(e,"safari")||Wn(e,"mobile/"))&&Wn(e,"applewebkit")}}],Qn=[{name:"Windows",search:Gn("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(e){return Wn(e,"iphone")||Wn(e,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:Gn("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:Gn("os x"),versionRegexes:[/.*?os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:Gn("linux"),versionRegexes:[]},{name:"Solaris",search:Gn("sunos"),versionRegexes:[]},{name:"FreeBSD",search:Gn("freebsd"),versionRegexes:[]}],Zn={browsers:j(Jn),oses:j(Qn)},er=function(e){var t,n,r,o,i,a,u,s,c,l,f,d=Zn.browsers(),m=Zn.oses(),g=qn(d,e).fold(Ln.unknown,Ln.nu),p=$n(m,e).fold(Hn.unknown,Hn.nu);return{browser:g,os:p,deviceType:(n=g,r=e,o=(t=p).isiOS()&&!0===/ipad/i.test(r),i=t.isiOS()&&!o,a=t.isAndroid()&&3===t.version.major,u=t.isAndroid()&&4===t.version.major,s=o||a||u&&!0===/mobile/i.test(r),c=t.isiOS()||t.isAndroid(),l=c&&!s,f=n.isSafari()&&t.isiOS()&&!1===/safari/i.test(r),{isiPad:j(o),isiPhone:j(i),isTablet:j(s),isPhone:j(l),isTouch:j(c),isAndroid:t.isAndroid,isiOS:t.isiOS,isWebView:j(f)})}},tr={detect:(Sn=function(){var e=navigator.userAgent;return er(e)},Tn=!1,function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return Tn||(Tn=!0,kn=Sn.apply(null,e)),kn})},nr=function(e){if(null===e||e===undefined)throw new Error("Node cannot be null or undefined");return{dom:j(e)}},rr={fromHtml:function(e,t){var n=(t||document).createElement("div");if(n.innerHTML=e,!n.hasChildNodes()||1<n.childNodes.length)throw console.error("HTML does not have a single root node",e),new Error("HTML must have a single root node");return nr(n.childNodes[0])},fromTag:function(e,t){var n=(t||document).createElement(e);return nr(n)},fromText:function(e,t){var n=(t||document).createTextNode(e);return nr(n)},fromDom:nr,fromPoint:function(e,t,n){var r=e.dom();return A.from(r.elementFromPoint(t,n)).map(nr)}},or=(Node.ATTRIBUTE_NODE,Node.CDATA_SECTION_NODE,Node.COMMENT_NODE,Node.DOCUMENT_NODE),ir=(Node.DOCUMENT_TYPE_NODE,Node.DOCUMENT_FRAGMENT_NODE,Node.ELEMENT_NODE),ar=Node.TEXT_NODE,ur=(Node.PROCESSING_INSTRUCTION_NODE,Node.ENTITY_REFERENCE_NODE,Node.ENTITY_NODE,Node.NOTATION_NODE,function(e){return e.dom().nodeName.toLowerCase()}),sr=function(t){return function(e){return e.dom().nodeType===t}},cr=sr(ir),lr=sr(ar),fr=Object.keys,dr=Object.hasOwnProperty,mr=function(e,t){for(var n=fr(e),r=0,o=n.length;r<o;r++){var i=n[r];t(e[i],i,e)}},gr=function(r,o){var i={};return mr(r,function(e,t){var n=o(e,t,r);i[n.k]=n.v}),i},pr=function(e){return e.style!==undefined},hr=function(e,t,n){if(!(R(n)||O(n)||L(n)))throw console.error("Invalid call to Attr.set. Key ",t,":: Value ",n,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,n+"")},vr=function(e,t,n){hr(e.dom(),t,n)},br=function(e,t){var n=e.dom();mr(t,function(e,t){hr(n,t,e)})},yr=function(e,t){var n=e.dom().getAttribute(t);return null===n?undefined:n},Cr=function(e,t){e.dom().removeAttribute(t)},xr=function(e,t){var n=e.dom();mr(t,function(e,t){!function(e,t,n){if(!R(n))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",n,":: Element ",e),new Error("CSS value must be a string: "+n);pr(e)&&e.style.setProperty(t,n)}(n,t,e)})},wr=function(e,t){var n,r,o=e.dom(),i=window.getComputedStyle(o).getPropertyValue(t),a=""!==i||(r=lr(n=e)?n.dom().parentNode:n.dom())!==undefined&&null!==r&&r.ownerDocument.body.contains(r)?i:Nr(o,t);return null===a?undefined:a},Nr=function(e,t){return pr(e)?e.style.getPropertyValue(t):""},Er=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];if(t.length!==n.length)throw new Error('Wrong number of arguments to struct. Expected "['+t.length+']", got '+n.length+" arguments");var r={};return F(t,function(e,t){r[e]=j(n[t])}),r}},Sr=function(e,t){for(var n=[],r=function(e){return n.push(e),t(e)},o=t(e);(o=o.bind(r)).isSome(););return n},kr=function(){return ie.getOrDie("Node")},Tr=function(e,t,n){return 0!=(e.compareDocumentPosition(t)&n)},Ar=function(e,t){return Tr(e,t,kr().DOCUMENT_POSITION_CONTAINED_BY)},Rr=ir,_r=or,Dr=function(e,t){var n=e.dom();if(n.nodeType!==Rr)return!1;if(n.matches!==undefined)return n.matches(t);if(n.msMatchesSelector!==undefined)return n.msMatchesSelector(t);if(n.webkitMatchesSelector!==undefined)return n.webkitMatchesSelector(t);if(n.mozMatchesSelector!==undefined)return n.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")},Br=function(e){return e.nodeType!==Rr&&e.nodeType!==_r||0===e.childElementCount},Or=function(e,t){return e.dom()===t.dom()},Pr=tr.detect().browser.isIE()?function(e,t){return Ar(e.dom(),t.dom())}:function(e,t){var n=e.dom(),r=t.dom();return n!==r&&n.contains(r)},Lr=function(e){return rr.fromDom(e.dom().ownerDocument)},Ir=function(e){var t=e.dom();return A.from(t.parentNode).map(rr.fromDom)},Mr=function(e){var t=e.dom();return A.from(t.previousSibling).map(rr.fromDom)},Fr=function(e){var t=e.dom();return A.from(t.nextSibling).map(rr.fromDom)},zr=function(e){return t=Sr(e,Mr),(n=Q.call(t,0)).reverse(),n;var t,n},Ur=function(e){return Sr(e,Fr)},Vr=function(e){var t=e.dom();return $(t.childNodes,rr.fromDom)},Hr=function(e,t){var n=e.dom().childNodes;return A.from(n[t]).map(rr.fromDom)},jr=function(e){return Hr(e,0)},qr=function(e){return Hr(e,e.dom().childNodes.length-1)},$r=(Er("element","offset"),tr.detect().browser),Wr=function(e){return V(e,cr)},Kr={getPos:function(e,t,n){var r,o,i,a=0,u=0,s=e.ownerDocument;if(n=n||e,t){if(n===e&&t.getBoundingClientRect&&"static"===wr(rr.fromDom(e),"position"))return{x:a=(o=t.getBoundingClientRect()).left+(s.documentElement.scrollLeft||e.scrollLeft)-s.documentElement.clientLeft,y:u=o.top+(s.documentElement.scrollTop||e.scrollTop)-s.documentElement.clientTop};for(r=t;r&&r!==n&&r.nodeType;)a+=r.offsetLeft||0,u+=r.offsetTop||0,r=r.offsetParent;for(r=t.parentNode;r&&r!==n&&r.nodeType;)a-=r.scrollLeft||0,u-=r.scrollTop||0,r=r.parentNode;u+=(i=rr.fromDom(t),$r.isFirefox()&&"table"===ur(i)?Wr(Vr(i)).filter(function(e){return"caption"===ur(e)}).bind(function(o){return Wr(Ur(o)).map(function(e){var t=e.dom().offsetTop,n=o.dom().offsetTop,r=o.dom().offsetHeight;return t<=n?-r:0})}).getOr(0):0)}return{x:a,y:u}}},Xr=function(e){var n=A.none(),t=[],r=function(e){o()?a(e):t.push(e)},o=function(){return n.isSome()},i=function(e){F(e,a)},a=function(t){n.each(function(e){setTimeout(function(){t(e)},0)})};return e(function(e){n=A.some(e),i(t),t=[]}),{get:r,map:function(n){return Xr(function(t){r(function(e){t(n(e))})})},isReady:o}},Yr={nu:Xr,pure:function(t){return Xr(function(e){e(t)})}},Gr=function(t){var e=function(e){var r;t((r=e,function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=this;setTimeout(function(){r.apply(n,e)},0)}))},n=function(){return Yr.nu(e)};return{map:function(r){return Gr(function(n){e(function(e){var t=r(e);n(t)})})},bind:function(n){return Gr(function(t){e(function(e){n(e).get(t)})})},anonBind:function(n){return Gr(function(t){e(function(e){n.get(t)})})},toLazy:n,toCached:function(){var t=null;return Gr(function(e){null===t&&(t=n()),t.get(e)})},get:e}},Jr={nu:Gr,pure:function(t){return Gr(function(e){e(t)})}},Qr=function(a,e){return e(function(r){var o=[],i=0;0===a.length?r([]):F(a,function(e,t){var n;e.get((n=t,function(e){o[n]=e,++i>=a.length&&r(o)}))})})},Zr=function(e){return Qr(e,Jr.nu)},eo=function(n){return{is:function(e){return n===e},isValue:x,isError:C,getOr:j(n),getOrThunk:j(n),getOrDie:j(n),or:function(e){return eo(n)},orThunk:function(e){return eo(n)},fold:function(e,t){return t(n)},map:function(e){return eo(e(n))},mapError:function(e){return eo(n)},each:function(e){e(n)},bind:function(e){return e(n)},exists:function(e){return e(n)},forall:function(e){return e(n)},toOption:function(){return A.some(n)}}},to=function(n){return{is:C,isValue:C,isError:x,getOr:q,getOrThunk:function(e){return e()},getOrDie:function(){return e=String(n),function(){throw new Error(e)}();var e},or:function(e){return e},orThunk:function(e){return e()},fold:function(e,t){return e(n)},map:function(e){return to(n)},mapError:function(e){return to(e(n))},each:o,bind:function(e){return to(n)},exists:C,forall:x,toOption:A.none}},no={value:eo,error:to};function ro(e,u){var t=e,n=function(e,t,n,r){var o,i;if(e){if(!r&&e[t])return e[t];if(e!==u){if(o=e[n])return o;for(i=e.parentNode;i&&i!==u;i=i.parentNode)if(o=i[n])return o}}};this.current=function(){return t},this.next=function(e){return t=n(t,"firstChild","nextSibling",e)},this.prev=function(e){return t=n(t,"lastChild","previousSibling",e)},this.prev2=function(e){return t=function(e,t,n,r){var o,i,a;if(e){if(o=e[n],u&&o===u)return;if(o){if(!r)for(a=o[t];a;a=a[t])if(!a[t])return a;return o}if((i=e.parentNode)&&i!==u)return i}}(t,"lastChild","previousSibling",e)}}var oo,io,ao,uo=function(t){var n;return function(e){return(n=n||function(e,t){for(var n={},r=0,o=e.length;r<o;r++){var i=e[r];n[String(i)]=t(i,r)}return n}(t,j(!0))).hasOwnProperty(ur(e))}},so=uo(["h1","h2","h3","h4","h5","h6"]),co=uo(["article","aside","details","div","dt","figcaption","footer","form","fieldset","header","hgroup","html","main","nav","section","summary","body","p","dl","multicol","dd","figure","address","center","blockquote","h1","h2","h3","h4","h5","h6","listing","xmp","pre","plaintext","menu","dir","ul","ol","li","hr","table","tbody","thead","tfoot","th","tr","td","caption"]),lo=function(e){return cr(e)&&!co(e)},fo=function(e){return cr(e)&&"br"===ur(e)},mo=uo(["h1","h2","h3","h4","h5","h6","p","div","address","pre","form","blockquote","center","dir","fieldset","header","footer","article","section","hgroup","aside","nav","figure"]),go=uo(["ul","ol","dl"]),po=uo(["li","dd","dt"]),ho=uo(["area","base","basefont","br","col","frame","hr","img","input","isindex","link","meta","param","embed","source","wbr","track"]),vo=uo(["thead","tbody","tfoot"]),bo=uo(["td","th"]),yo=uo(["pre","script","textarea","style"]),Co=function(t){return function(e){return!!e&&e.nodeType===t}},xo=Co(1),wo=function(e){var r=e.toLowerCase().split(" ");return function(e){var t,n;if(e&&e.nodeType)for(n=e.nodeName.toLowerCase(),t=0;t<r.length;t++)if(n===r[t])return!0;return!1}},No=function(t){return function(e){if(xo(e)){if(e.contentEditable===t)return!0;if(e.getAttribute("data-mce-contenteditable")===t)return!0}return!1}},Eo=Co(3),So=Co(8),ko=Co(9),To=Co(11),Ao=wo("br"),Ro=No("true"),_o=No("false"),Do={isText:Eo,isElement:xo,isComment:So,isDocument:ko,isDocumentFragment:To,isBr:Ao,isContentEditableTrue:Ro,isContentEditableFalse:_o,matchNodeNames:wo,hasPropValue:function(t,n){return function(e){return xo(e)&&e[t]===n}},hasAttribute:function(t,e){return function(e){return xo(e)&&e.hasAttribute(t)}},hasAttributeValue:function(t,n){return function(e){return xo(e)&&e.getAttribute(t)===n}},matchStyleValues:function(r,e){var o=e.toLowerCase().split(" ");return function(e){var t;if(xo(e))for(t=0;t<o.length;t++){var n=e.ownerDocument.defaultView.getComputedStyle(e,null);if((n?n.getPropertyValue(r):null)===o[t])return!0}return!1}},isBogus:function(e){return xo(e)&&e.hasAttribute("data-mce-bogus")},isBogusAll:function(e){return xo(e)&&"all"===e.getAttribute("data-mce-bogus")},isTable:function(e){return xo(e)&&"TABLE"===e.tagName}},Bo=function(e){return e&&"SPAN"===e.tagName&&"bookmark"===e.getAttribute("data-mce-type")},Oo=function(e,t){var n,r=t.childNodes;if(!Do.isElement(t)||!Bo(t)){for(n=r.length-1;0<=n;n--)Oo(e,r[n]);if(!1===Do.isDocument(t)){if(Do.isText(t)&&0<t.nodeValue.length){var o=Yt.trim(t.nodeValue).length;if(e.isBlock(t.parentNode)||0<o)return;if(0===o&&(a=(i=t).previousSibling&&"SPAN"===i.previousSibling.nodeName,u=i.nextSibling&&"SPAN"===i.nextSibling.nodeName,a&&u))return}else if(Do.isElement(t)&&(1===(r=t.childNodes).length&&Bo(r[0])&&t.parentNode.insertBefore(r[0],t),r.length||ho(rr.fromDom(t))))return;e.remove(t)}var i,a,u;return t}},Po={trimNode:Oo},Lo=Yt.makeMap,Io=/[&<>\"\u0060\u007E-\uD7FF\uE000-\uFFEF]|[\uD800-\uDBFF][\uDC00-\uDFFF]/g,Mo=/[<>&\u007E-\uD7FF\uE000-\uFFEF]|[\uD800-\uDBFF][\uDC00-\uDFFF]/g,Fo=/[<>&\"\']/g,zo=/&#([a-z0-9]+);?|&([a-z0-9]+);/gi,Uo={128:"\u20ac",130:"\u201a",131:"\u0192",132:"\u201e",133:"\u2026",134:"\u2020",135:"\u2021",136:"\u02c6",137:"\u2030",138:"\u0160",139:"\u2039",140:"\u0152",142:"\u017d",145:"\u2018",146:"\u2019",147:"\u201c",148:"\u201d",149:"\u2022",150:"\u2013",151:"\u2014",152:"\u02dc",153:"\u2122",154:"\u0161",155:"\u203a",156:"\u0153",158:"\u017e",159:"\u0178"};io={'"':"&quot;","'":"&#39;","<":"&lt;",">":"&gt;","&":"&amp;","`":"&#96;"},ao={"&lt;":"<","&gt;":">","&amp;":"&","&quot;":'"',"&apos;":"'"};var Vo=function(e,t){var n,r,o,i={};if(e){for(e=e.split(","),t=t||10,n=0;n<e.length;n+=2)r=String.fromCharCode(parseInt(e[n],t)),io[r]||(o="&"+e[n+1]+";",i[r]=o,i[o]=r);return i}};oo=Vo("50,nbsp,51,iexcl,52,cent,53,pound,54,curren,55,yen,56,brvbar,57,sect,58,uml,59,copy,5a,ordf,5b,laquo,5c,not,5d,shy,5e,reg,5f,macr,5g,deg,5h,plusmn,5i,sup2,5j,sup3,5k,acute,5l,micro,5m,para,5n,middot,5o,cedil,5p,sup1,5q,ordm,5r,raquo,5s,frac14,5t,frac12,5u,frac34,5v,iquest,60,Agrave,61,Aacute,62,Acirc,63,Atilde,64,Auml,65,Aring,66,AElig,67,Ccedil,68,Egrave,69,Eacute,6a,Ecirc,6b,Euml,6c,Igrave,6d,Iacute,6e,Icirc,6f,Iuml,6g,ETH,6h,Ntilde,6i,Ograve,6j,Oacute,6k,Ocirc,6l,Otilde,6m,Ouml,6n,times,6o,Oslash,6p,Ugrave,6q,Uacute,6r,Ucirc,6s,Uuml,6t,Yacute,6u,THORN,6v,szlig,70,agrave,71,aacute,72,acirc,73,atilde,74,auml,75,aring,76,aelig,77,ccedil,78,egrave,79,eacute,7a,ecirc,7b,euml,7c,igrave,7d,iacute,7e,icirc,7f,iuml,7g,eth,7h,ntilde,7i,ograve,7j,oacute,7k,ocirc,7l,otilde,7m,ouml,7n,divide,7o,oslash,7p,ugrave,7q,uacute,7r,ucirc,7s,uuml,7t,yacute,7u,thorn,7v,yuml,ci,fnof,sh,Alpha,si,Beta,sj,Gamma,sk,Delta,sl,Epsilon,sm,Zeta,sn,Eta,so,Theta,sp,Iota,sq,Kappa,sr,Lambda,ss,Mu,st,Nu,su,Xi,sv,Omicron,t0,Pi,t1,Rho,t3,Sigma,t4,Tau,t5,Upsilon,t6,Phi,t7,Chi,t8,Psi,t9,Omega,th,alpha,ti,beta,tj,gamma,tk,delta,tl,epsilon,tm,zeta,tn,eta,to,theta,tp,iota,tq,kappa,tr,lambda,ts,mu,tt,nu,tu,xi,tv,omicron,u0,pi,u1,rho,u2,sigmaf,u3,sigma,u4,tau,u5,upsilon,u6,phi,u7,chi,u8,psi,u9,omega,uh,thetasym,ui,upsih,um,piv,812,bull,816,hellip,81i,prime,81j,Prime,81u,oline,824,frasl,88o,weierp,88h,image,88s,real,892,trade,89l,alefsym,8cg,larr,8ch,uarr,8ci,rarr,8cj,darr,8ck,harr,8dl,crarr,8eg,lArr,8eh,uArr,8ei,rArr,8ej,dArr,8ek,hArr,8g0,forall,8g2,part,8g3,exist,8g5,empty,8g7,nabla,8g8,isin,8g9,notin,8gb,ni,8gf,prod,8gh,sum,8gi,minus,8gn,lowast,8gq,radic,8gt,prop,8gu,infin,8h0,ang,8h7,and,8h8,or,8h9,cap,8ha,cup,8hb,int,8hk,there4,8hs,sim,8i5,cong,8i8,asymp,8j0,ne,8j1,equiv,8j4,le,8j5,ge,8k2,sub,8k3,sup,8k4,nsub,8k6,sube,8k7,supe,8kl,oplus,8kn,otimes,8l5,perp,8m5,sdot,8o8,lceil,8o9,rceil,8oa,lfloor,8ob,rfloor,8p9,lang,8pa,rang,9ea,loz,9j0,spades,9j3,clubs,9j5,hearts,9j6,diams,ai,OElig,aj,oelig,b0,Scaron,b1,scaron,bo,Yuml,m6,circ,ms,tilde,802,ensp,803,emsp,809,thinsp,80c,zwnj,80d,zwj,80e,lrm,80f,rlm,80j,ndash,80k,mdash,80o,lsquo,80p,rsquo,80q,sbquo,80s,ldquo,80t,rdquo,80u,bdquo,810,dagger,811,Dagger,81g,permil,81p,lsaquo,81q,rsaquo,85c,euro",32);var Ho=function(e,t){return e.replace(t?Io:Mo,function(e){return io[e]||e})},jo=function(e,t){return e.replace(t?Io:Mo,function(e){return 1<e.length?"&#"+(1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320)+65536)+";":io[e]||"&#"+e.charCodeAt(0)+";"})},qo=function(e,t,n){return n=n||oo,e.replace(t?Io:Mo,function(e){return io[e]||n[e]||e})},$o={encodeRaw:Ho,encodeAllRaw:function(e){return(""+e).replace(Fo,function(e){return io[e]||e})},encodeNumeric:jo,encodeNamed:qo,getEncodeFunc:function(e,t){var n=Vo(t)||oo,r=Lo(e.replace(/\+/g,","));return r.named&&r.numeric?function(e,t){return e.replace(t?Io:Mo,function(e){return io[e]!==undefined?io[e]:n[e]!==undefined?n[e]:1<e.length?"&#"+(1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320)+65536)+";":"&#"+e.charCodeAt(0)+";"})}:r.named?t?function(e,t){return qo(e,t,n)}:qo:r.numeric?jo:Ho},decode:function(e){return e.replace(zo,function(e,t){return t?65535<(t="x"===t.charAt(0).toLowerCase()?parseInt(t.substr(1),16):parseInt(t,10))?(t-=65536,String.fromCharCode(55296+(t>>10),56320+(1023&t))):Uo[t]||String.fromCharCode(t):ao[e]||oo[e]||(n=e,(r=rr.fromTag("div").dom()).innerHTML=n,r.textContent||r.innerText||n);var n,r})}},Wo={},Ko={},Xo=Yt.makeMap,Yo=Yt.each,Go=Yt.extend,Jo=Yt.explode,Qo=Yt.inArray,Zo=function(e,t){return(e=Yt.trim(e))?e.split(t||" "):[]},ei=function(e){var u,t,n,r,o,i,s={},a=function(e,t,n){var r,o,i,a=function(e,t){var n,r,o={};for(n=0,r=e.length;n<r;n++)o[e[n]]=t||{};return o};for(t=t||"","string"==typeof(n=n||[])&&(n=Zo(n)),r=(e=Zo(e)).length;r--;)i={attributes:a(o=Zo([u,t].join(" "))),attributesOrder:o,children:a(n,Ko)},s[e[r]]=i},c=function(e,t){var n,r,o,i;for(n=(e=Zo(e)).length,t=Zo(t);n--;)for(r=s[e[n]],o=0,i=t.length;o<i;o++)r.attributes[t[o]]={},r.attributesOrder.push(t[o])};return Wo[e]?Wo[e]:(u="id accesskey class dir lang style tabindex title role",t="address blockquote div dl fieldset form h1 h2 h3 h4 h5 h6 hr menu ol p pre table ul",n="a abbr b bdo br button cite code del dfn em embed i iframe img input ins kbd label map noscript object q s samp script select small span strong sub sup textarea u var #text #comment","html4"!==e&&(u+=" contenteditable contextmenu draggable dropzone hidden spellcheck translate",t+=" article aside details dialog figure main header footer hgroup section nav",n+=" audio canvas command datalist mark meter output picture progress time wbr video ruby bdi keygen"),"html5-strict"!==e&&(u+=" xml:lang",n=[n,i="acronym applet basefont big font strike tt"].join(" "),Yo(Zo(i),function(e){a(e,"",n)}),t=[t,o="center dir isindex noframes"].join(" "),r=[t,n].join(" "),Yo(Zo(o),function(e){a(e,"",r)})),r=r||[t,n].join(" "),a("html","manifest","head body"),a("head","","base command link meta noscript script style title"),a("title hr noscript br"),a("base","href target"),a("link","href rel media hreflang type sizes hreflang"),a("meta","name http-equiv content charset"),a("style","media type scoped"),a("script","src async defer type charset"),a("body","onafterprint onbeforeprint onbeforeunload onblur onerror onfocus onhashchange onload onmessage onoffline ononline onpagehide onpageshow onpopstate onresize onscroll onstorage onunload",r),a("address dt dd div caption","",r),a("h1 h2 h3 h4 h5 h6 pre p abbr code var samp kbd sub sup i b u bdo span legend em strong small s cite dfn","",n),a("blockquote","cite",r),a("ol","reversed start type","li"),a("ul","","li"),a("li","value",r),a("dl","","dt dd"),a("a","href target rel media hreflang type",n),a("q","cite",n),a("ins del","cite datetime",r),a("img","src sizes srcset alt usemap ismap width height"),a("iframe","src name width height",r),a("embed","src type width height"),a("object","data type typemustmatch name usemap form width height",[r,"param"].join(" ")),a("param","name value"),a("map","name",[r,"area"].join(" ")),a("area","alt coords shape href target rel media hreflang type"),a("table","border","caption colgroup thead tfoot tbody tr"+("html4"===e?" col":"")),a("colgroup","span","col"),a("col","span"),a("tbody thead tfoot","","tr"),a("tr","","td th"),a("td","colspan rowspan headers",r),a("th","colspan rowspan headers scope abbr",r),a("form","accept-charset action autocomplete enctype method name novalidate target",r),a("fieldset","disabled form name",[r,"legend"].join(" ")),a("label","form for",n),a("input","accept alt autocomplete checked dirname disabled form formaction formenctype formmethod formnovalidate formtarget height list max maxlength min multiple name pattern readonly required size src step type value width"),a("button","disabled form formaction formenctype formmethod formnovalidate formtarget name type value","html4"===e?r:n),a("select","disabled form multiple name required size","option optgroup"),a("optgroup","disabled label","option"),a("option","disabled label selected value"),a("textarea","cols dirname disabled form maxlength name readonly required rows wrap"),a("menu","type label",[r,"li"].join(" ")),a("noscript","",r),"html4"!==e&&(a("wbr"),a("ruby","",[n,"rt rp"].join(" ")),a("figcaption","",r),a("mark rt rp summary bdi","",n),a("canvas","width height",r),a("video","src crossorigin poster preload autoplay mediagroup loop muted controls width height buffered",[r,"track source"].join(" ")),a("audio","src crossorigin preload autoplay mediagroup loop muted controls buffered volume",[r,"track source"].join(" ")),a("picture","","img source"),a("source","src srcset type media sizes"),a("track","kind src srclang label default"),a("datalist","",[n,"option"].join(" ")),a("article section nav aside main header footer","",r),a("hgroup","","h1 h2 h3 h4 h5 h6"),a("figure","",[r,"figcaption"].join(" ")),a("time","datetime",n),a("dialog","open",r),a("command","type label icon disabled checked radiogroup command"),a("output","for form name",n),a("progress","value max",n),a("meter","value min max low high optimum",n),a("details","open",[r,"summary"].join(" ")),a("keygen","autofocus challenge disabled form keytype name")),"html5-strict"!==e&&(c("script","language xml:space"),c("style","xml:space"),c("object","declare classid code codebase codetype archive standby align border hspace vspace"),c("embed","align name hspace vspace"),c("param","valuetype type"),c("a","charset name rev shape coords"),c("br","clear"),c("applet","codebase archive code object alt name width height align hspace vspace"),c("img","name longdesc align border hspace vspace"),c("iframe","longdesc frameborder marginwidth marginheight scrolling align"),c("font basefont","size color face"),c("input","usemap align"),c("select","onchange"),c("textarea"),c("h1 h2 h3 h4 h5 h6 div p legend caption","align"),c("ul","type compact"),c("li","type"),c("ol dl menu dir","compact"),c("pre","width xml:space"),c("hr","align noshade size width"),c("isindex","prompt"),c("table","summary width frame rules cellspacing cellpadding align bgcolor"),c("col","width align char charoff valign"),c("colgroup","width align char charoff valign"),c("thead","align char charoff valign"),c("tr","align char charoff valign bgcolor"),c("th","axis align char charoff valign nowrap bgcolor width height"),c("form","accept"),c("td","abbr axis scope align char charoff valign nowrap bgcolor width height"),c("tfoot","align char charoff valign"),c("tbody","align char charoff valign"),c("area","nohref"),c("body","background bgcolor text link vlink alink")),"html4"!==e&&(c("input button select textarea","autofocus"),c("input textarea","placeholder"),c("a","download"),c("link script img","crossorigin"),c("iframe","sandbox seamless allowfullscreen")),Yo(Zo("a form meter progress dfn"),function(e){s[e]&&delete s[e].children[e]}),delete s.caption.children.table,delete s.script,Wo[e]=s)},ti=function(e,n){var r;return e&&(r={},"string"==typeof e&&(e={"*":e}),Yo(e,function(e,t){r[t]=r[t.toUpperCase()]="map"===n?Xo(e,/[, ]/):Jo(e,/[, ]/)})),r};function ni(i){var e,t,n,r,o,a,u,s,c,l,f,d,m,N={},g={},E=[],p={},h={},v=function(e,t,n){var r=i[e];return r?r=Xo(r,/[, ]/,Xo(r.toUpperCase(),/[, ]/)):(r=Wo[e])||(r=Xo(t," ",Xo(t.toUpperCase()," ")),r=Go(r,n),Wo[e]=r),r};n=ei((i=i||{}).schema),!1===i.verify_html&&(i.valid_elements="*[*]"),e=ti(i.valid_styles),t=ti(i.invalid_styles,"map"),s=ti(i.valid_classes,"map"),r=v("whitespace_elements","pre script noscript style textarea video audio iframe object code"),o=v("self_closing_elements","colgroup dd dt li option p td tfoot th thead tr"),a=v("short_ended_elements","area base basefont br col frame hr img input isindex link meta param embed source wbr track"),u=v("boolean_attributes","checked compact declare defer disabled ismap multiple nohref noresize noshade nowrap readonly selected autoplay loop controls"),l=v("non_empty_elements","td th iframe video audio object script pre code",a),f=v("move_caret_before_on_enter_elements","table",l),d=v("text_block_elements","h1 h2 h3 h4 h5 h6 p div address pre form blockquote center dir fieldset header footer article section hgroup aside main nav figure"),c=v("block_elements","hr table tbody thead tfoot th tr td li ol ul caption dl dt dd noscript menu isindex option datalist select optgroup figcaption details summary",d),m=v("text_inline_elements","span strong b em i font strike u var cite dfn code mark q sup sub samp"),Yo((i.special||"script noscript noframes noembed title style textarea xmp").split(" "),function(e){h[e]=new RegExp("</"+e+"[^>]*>","gi")});var S=function(e){return new RegExp("^"+e.replace(/([?+*])/g,".$1")+"$")},b=function(e){var t,n,r,o,i,a,u,s,c,l,f,d,m,g,p,h,v,b,y,C=/^([#+\-])?([^\[!\/]+)(?:\/([^\[!]+))?(?:(!?)\[([^\]]+)\])?$/,x=/^([!\-])?(\w+[\\:]:\w+|[^=:<]+)?(?:([=:<])(.*))?$/,w=/[*?+]/;if(e)for(e=Zo(e,","),N["@"]&&(h=N["@"].attributes,v=N["@"].attributesOrder),t=0,n=e.length;t<n;t++)if(i=C.exec(e[t])){if(g=i[1],c=i[2],p=i[3],s=i[5],a={attributes:d={},attributesOrder:m=[]},"#"===g&&(a.paddEmpty=!0),"-"===g&&(a.removeEmpty=!0),"!"===i[4]&&(a.removeEmptyAttrs=!0),h){for(b in h)d[b]=h[b];m.push.apply(m,v)}if(s)for(r=0,o=(s=Zo(s,"|")).length;r<o;r++)if(i=x.exec(s[r])){if(u={},f=i[1],l=i[2].replace(/[\\:]:/g,":"),g=i[3],y=i[4],"!"===f&&(a.attributesRequired=a.attributesRequired||[],a.attributesRequired.push(l),u.required=!0),"-"===f){delete d[l],m.splice(Qo(m,l),1);continue}g&&("="===g&&(a.attributesDefault=a.attributesDefault||[],a.attributesDefault.push({name:l,value:y}),u.defaultValue=y),":"===g&&(a.attributesForced=a.attributesForced||[],a.attributesForced.push({name:l,value:y}),u.forcedValue=y),"<"===g&&(u.validValues=Xo(y,"?"))),w.test(l)?(a.attributePatterns=a.attributePatterns||[],u.pattern=S(l),a.attributePatterns.push(u)):(d[l]||m.push(l),d[l]=u)}h||"@"!==c||(h=d,v=m),p&&(a.outputName=c,N[p]=a),w.test(c)?(a.pattern=S(c),E.push(a)):N[c]=a}},y=function(e){N={},E=[],b(e),Yo(n,function(e,t){g[t]=e.children})},C=function(e){var a=/^(~)?(.+)$/;e&&(Wo.text_block_elements=Wo.block_elements=null,Yo(Zo(e,","),function(e){var t=a.exec(e),n="~"===t[1],r=n?"span":"div",o=t[2];if(g[o]=g[r],p[o]=r,n||(c[o.toUpperCase()]={},c[o]={}),!N[o]){var i=N[r];delete(i=Go({},i)).removeEmptyAttrs,delete i.removeEmpty,N[o]=i}Yo(g,function(e,t){e[r]&&(g[t]=e=Go({},g[t]),e[o]=e[r])})}))},x=function(e){var o=/^([+\-]?)(\w+)\[([^\]]+)\]$/;Wo[i.schema]=null,e&&Yo(Zo(e,","),function(e){var t,n,r=o.exec(e);r&&(n=r[1],t=n?g[r[2]]:g[r[2]]={"#comment":{}},t=g[r[2]],Yo(Zo(r[3],"|"),function(e){"-"===n?delete t[e]:t[e]={}}))})},w=function(e){var t,n=N[e];if(n)return n;for(t=E.length;t--;)if((n=E[t]).pattern.test(e))return n};return i.valid_elements?y(i.valid_elements):(Yo(n,function(e,t){N[t]={attributes:e.attributes,attributesOrder:e.attributesOrder},g[t]=e.children}),"html5"!==i.schema&&Yo(Zo("strong/b em/i"),function(e){e=Zo(e,"/"),N[e[1]].outputName=e[0]}),Yo(Zo("ol ul sub sup blockquote span font a table tbody tr strong em b i"),function(e){N[e]&&(N[e].removeEmpty=!0)}),Yo(Zo("p h1 h2 h3 h4 h5 h6 th td pre div address caption li"),function(e){N[e].paddEmpty=!0}),Yo(Zo("span"),function(e){N[e].removeEmptyAttrs=!0})),C(i.custom_elements),x(i.valid_children),b(i.extended_valid_elements),x("+ol[ul|ol],+ul[ul|ol]"),Yo({dd:"dl",dt:"dl",li:"ul ol",td:"tr",th:"tr",tr:"tbody thead tfoot",tbody:"table",thead:"table",tfoot:"table",legend:"fieldset",area:"map",param:"video audio object"},function(e,t){N[t]&&(N[t].parentsRequired=Zo(e))}),i.invalid_elements&&Yo(Jo(i.invalid_elements),function(e){N[e]&&delete N[e]}),w("span")||b("span[!data-mce-type|*]"),{children:g,elements:N,getValidStyles:function(){return e},getValidClasses:function(){return s},getBlockElements:function(){return c},getInvalidStyles:function(){return t},getShortEndedElements:function(){return a},getTextBlockElements:function(){return d},getTextInlineElements:function(){return m},getBoolAttrs:function(){return u},getElementRule:w,getSelfClosingElements:function(){return o},getNonEmptyElements:function(){return l},getMoveCaretBeforeOnEnterElements:function(){return f},getWhiteSpaceElements:function(){return r},getSpecialElements:function(){return h},isValidChild:function(e,t){var n=g[e.toLowerCase()];return!(!n||!n[t.toLowerCase()])},isValid:function(e,t){var n,r,o=w(e);if(o){if(!t)return!0;if(o.attributes[t])return!0;if(n=o.attributePatterns)for(r=n.length;r--;)if(n[r].pattern.test(e))return!0}return!1},getCustomElements:function(){return p},addValidElements:b,setValidElements:y,addCustomElements:C,addValidChildren:x}}var ri=function(e,t,n,r){var o=function(e){return 1<(e=parseInt(e,10).toString(16)).length?e:"0"+e};return"#"+o(t)+o(n)+o(r)};function oi(y,e){var C,t,c,l,x=/rgb\s*\(\s*([0-9]+)\s*,\s*([0-9]+)\s*,\s*([0-9]+)\s*\)/gi,w=/(?:url(?:(?:\(\s*\"([^\"]+)\"\s*\))|(?:\(\s*\'([^\']+)\'\s*\))|(?:\(\s*([^)\s]+)\s*\))))|(?:\'([^\']+)\')|(?:\"([^\"]+)\")/gi,N=/\s*([^:]+):\s*([^;]+);?/g,E=/\s+$/,S={},k="\ufeff";for(y=y||{},e&&(c=e.getValidStyles(),l=e.getInvalidStyles()),t=("\\\" \\' \\; \\: ; : "+k).split(" "),C=0;C<t.length;C++)S[t[C]]=k+C,S[k+C]=t[C];return{toHex:function(e){return e.replace(x,ri)},parse:function(e){var t,n,r,o,i,a,u,s,c={},l=y.url_converter,f=y.url_converter_scope||this,d=function(e,t,n){var r,o,i,a;if((r=c[e+"-top"+t])&&(o=c[e+"-right"+t])&&(i=c[e+"-bottom"+t])&&(a=c[e+"-left"+t])){var u=[r,o,i,a];for(C=u.length-1;C--&&u[C]===u[C+1];);-1<C&&n||(c[e+t]=-1===C?u[0]:u.join(" "),delete c[e+"-top"+t],delete c[e+"-right"+t],delete c[e+"-bottom"+t],delete c[e+"-left"+t])}},m=function(e){var t,n=c[e];if(n){for(t=(n=n.split(" ")).length;t--;)if(n[t]!==n[0])return!1;return c[e]=n[0],!0}},g=function(e){return o=!0,S[e]},p=function(e,t){return o&&(e=e.replace(/\uFEFF[0-9]/g,function(e){return S[e]})),t||(e=e.replace(/\\([\'\";:])/g,"$1")),e},h=function(e){return String.fromCharCode(parseInt(e.slice(1),16))},v=function(e){return e.replace(/\\[0-9a-f]+/gi,h)},b=function(e,t,n,r,o,i){if(o=o||i)return"'"+(o=p(o)).replace(/\'/g,"\\'")+"'";if(t=p(t||n||r),!y.allow_script_urls){var a=t.replace(/[\s\r\n]+/g,"");if(/(java|vb)script:/i.test(a))return"";if(!y.allow_svg_data_urls&&/^data:image\/svg/i.test(a))return""}return l&&(t=l.call(f,t,"style")),"url('"+t.replace(/\'/g,"\\'")+"')"};if(e){for(e=(e=e.replace(/[\u0000-\u001F]/g,"")).replace(/\\[\"\';:\uFEFF]/g,g).replace(/\"[^\"]+\"|\'[^\']+\'/g,function(e){return e.replace(/[;:]/g,g)});t=N.exec(e);)if(N.lastIndex=t.index+t[0].length,n=t[1].replace(E,"").toLowerCase(),r=t[2].replace(E,""),n&&r){if(n=v(n),r=v(r),-1!==n.indexOf(k)||-1!==n.indexOf('"'))continue;if(!y.allow_script_urls&&("behavior"===n||/expression\s*\(|\/\*|\*\//.test(r)))continue;"font-weight"===n&&"700"===r?r="bold":"color"!==n&&"background-color"!==n||(r=r.toLowerCase()),r=(r=r.replace(x,ri)).replace(w,b),c[n]=o?p(r,!0):r}d("border","",!0),d("border","-width"),d("border","-color"),d("border","-style"),d("padding",""),d("margin",""),i="border",u="border-style",s="border-color",m(a="border-width")&&m(u)&&m(s)&&(c[i]=c[a]+" "+c[u]+" "+c[s],delete c[a],delete c[u],delete c[s]),"medium none"===c.border&&delete c.border,"none"===c["border-image"]&&delete c["border-image"]}return c},serialize:function(i,e){var t,n,r,o,a,u="",s=function(e){var t,n,r,o;if(t=c[e])for(n=0,r=t.length;n<r;n++)e=t[n],(o=i[e])&&(u+=(0<u.length?" ":"")+e+": "+o+";")};if(e&&c)s("*"),s(e);else for(t in i)!(n=i[t])||l&&(r=t,o=e,a=void 0,(a=l["*"])&&a[r]||(a=l[o])&&a[r])||(u+=(0<u.length?" ":"")+t+": "+n+";");return u}}}var ii,ai=Yt.each,ui=Yt.grep,si=de.ie,ci=/^([a-z0-9],?)+$/i,li=/^[ \t\r\n]*$/,fi=function(n,r,o){var e={},i=r.keep_values,t={set:function(e,t,n){r.url_converter&&(t=r.url_converter.call(r.url_converter_scope||o(),t,n,e[0])),e.attr("data-mce-"+n,t).attr(n,t)},get:function(e,t){return e.attr("data-mce-"+t)||e.attr(t)}};return e={style:{set:function(e,t){null===t||"object"!=typeof t?(i&&e.attr("data-mce-style",t),e.attr("style",t)):e.css(t)},get:function(e){var t=e.attr("data-mce-style")||e.attr("style");return t=n.serialize(n.parse(t),e[0].nodeName)}}},i&&(e.href=e.src=t),e},di=function(e,t){var n=t.attr("style"),r=e.serialize(e.parse(n),t[0].nodeName);r||(r=null),t.attr("data-mce-style",r)},mi=function(e,t){var n,r,o=0;if(e)for(n=e.nodeType,e=e.previousSibling;e;e=e.previousSibling)r=e.nodeType,(!t||3!==r||r!==n&&e.nodeValue.length)&&(o++,n=r);return o};function gi(a,u){var s,c=this;void 0===u&&(u={});var r={},i=window,o={},t=0,e=function(m,g){void 0===g&&(g={});var p,h=0,v={};p=g.maxLoadTime||5e3;var b=function(e){m.getElementsByTagName("head")[0].appendChild(e)},n=function(e,t,n){var o,r,i,a,u=function(){for(var e=a.passed,t=e.length;t--;)e[t]();a.status=2,a.passed=[],a.failed=[]},s=function(){for(var e=a.failed,t=e.length;t--;)e[t]();a.status=3,a.passed=[],a.failed=[]},c=function(e,t){e()||((new Date).getTime()-i<p?ve.setTimeout(t):s())},l=function(){c(function(){for(var e,t,n=m.styleSheets,r=n.length;r--;)if((t=(e=n[r]).ownerNode?e.ownerNode:e.owningElement)&&t.id===o.id)return u(),!0},l)},f=function(){c(function(){try{var e=r.sheet.cssRules;return u(),!!e}catch(t){}},f)};if(e=Yt._addCacheSuffix(e),v[e]?a=v[e]:(a={passed:[],failed:[]},v[e]=a),t&&a.passed.push(t),n&&a.failed.push(n),1!==a.status)if(2!==a.status)if(3!==a.status){if(a.status=1,(o=m.createElement("link")).rel="stylesheet",o.type="text/css",o.id="u"+h++,o.async=!1,o.defer=!1,i=(new Date).getTime(),g.contentCssCors&&(o.crossOrigin="anonymous"),"onload"in o&&!((d=navigator.userAgent.match(/WebKit\/(\d*)/))&&parseInt(d[1],10)<536))o.onload=l,o.onerror=s;else{if(0<navigator.userAgent.indexOf("Firefox"))return(r=m.createElement("style")).textContent='@import "'+e+'"',f(),void b(r);l()}var d;b(o),o.href=e}else s();else u()},t=function(t){return Jr.nu(function(e){n(t,H(e,j(no.value(t))),H(e,j(no.error(t))))})},o=function(e){return e.fold(q,q)};return{load:n,loadAll:function(e,n,r){Zr($(e,t)).get(function(e){var t=W(e,function(e){return e.isValue()});0<t.fail.length?r(t.fail.map(o)):n(t.pass.map(o))})}}}(a,{contentCssCors:u.contentCssCors}),l=[],f=u.schema?u.schema:ni({}),d=oi({url_converter:u.url_converter,url_converter_scope:u.url_converter_scope},u.schema),m=u.ownEvents?new ke(u.proxy):ke.Event,n=f.getBlockElements(),g=pn.overrideDefaults(function(){return{context:a,element:V.getRoot()}}),p=function(e){if(e&&a&&"string"==typeof e){var t=a.getElementById(e);return t&&t.id!==e?a.getElementsByName(e)[1]:t}return e},h=function(e){return"string"==typeof e&&(e=p(e)),g(e)},v=function(e,t,n){var r,o,i=h(e);return i.length&&(o=(r=s[t])&&r.get?r.get(i,t):i.attr(t)),void 0===o&&(o=n||""),o},b=function(e){var t=p(e);return t?t.attributes:[]},y=function(e,t,n){var r,o;""===n&&(n=null);var i=h(e);r=i.attr(t),i.length&&((o=s[t])&&o.set?o.set(i,n,t):i.attr(t,n),r!==n&&u.onSetAttrib&&u.onSetAttrib({attrElm:i,attrName:t,attrValue:n}))},C=function(){return u.root_element||a.body},x=function(e,t){return Kr.getPos(a.body,p(e),t)},w=function(e,t,n){var r=h(e);return n?r.css(t):("float"===(t=t.replace(/-(\D)/g,function(e,t){return t.toUpperCase()}))&&(t=de.ie&&de.ie<12?"styleFloat":"cssFloat"),r[0]&&r[0].style?r[0].style[t]:undefined)},N=function(e){var t,n;return e=p(e),t=w(e,"width"),n=w(e,"height"),-1===t.indexOf("px")&&(t=0),-1===n.indexOf("px")&&(n=0),{w:parseInt(t,10)||e.offsetWidth||e.clientWidth,h:parseInt(n,10)||e.offsetHeight||e.clientHeight}},E=function(e,t){var n;if(!e)return!1;if(!Array.isArray(e)){if("*"===t)return 1===e.nodeType;if(ci.test(t)){var r=t.toLowerCase().split(/,/),o=e.nodeName.toLowerCase();for(n=r.length-1;0<=n;n--)if(r[n]===o)return!0;return!1}if(e.nodeType&&1!==e.nodeType)return!1}var i=Array.isArray(e)?e:[e];return 0<kt(t,i[0].ownerDocument||i[0],null,i).length},S=function(e,t,n,r){var o,i=[],a=p(e);for(r=r===undefined,n=n||("BODY"!==C().nodeName?C().parentNode:null),Yt.is(t,"string")&&(t="*"===(o=t)?function(e){return 1===e.nodeType}:function(e){return E(e,o)});a&&a!==n&&a.nodeType&&9!==a.nodeType;){if(!t||"function"==typeof t&&t(a)){if(!r)return[a];i.push(a)}a=a.parentNode}return r?i:null},k=function(e,t,n){var r=t;if(e)for("string"==typeof t&&(r=function(e){return E(e,t)}),e=e[n];e;e=e[n])if("function"==typeof r&&r(e))return e;return null},T=function(e,n,r){var o,t="string"==typeof e?p(e):e;if(!t)return!1;if(Yt.isArray(t)&&(t.length||0===t.length))return o=[],ai(t,function(e,t){e&&("string"==typeof e&&(e=p(e)),o.push(n.call(r,e,t)))}),o;var i=r||c;return n.call(i,t)},A=function(e,t){h(e).each(function(e,n){ai(t,function(e,t){y(n,t,e)})})},R=function(e,r){var t=h(e);si?t.each(function(e,t){if(!1!==t.canHaveHTML){for(;t.firstChild;)t.removeChild(t.firstChild);try{t.innerHTML="<br>"+r,t.removeChild(t.firstChild)}catch(n){pn("<div></div>").html("<br>"+r).contents().slice(1).appendTo(t)}return r}}):t.html(r)},_=function(e,n,r,o,i){return T(e,function(e){var t="string"==typeof n?a.createElement(n):n;return A(t,r),o&&("string"!=typeof o&&o.nodeType?t.appendChild(o):"string"==typeof o&&R(t,o)),i?t:e.appendChild(t)})},D=function(e,t,n){return _(a.createElement(e),e,t,n,!0)},B=$o.decode,O=$o.encodeAllRaw,P=function(e,t){var n=h(e);return t?n.each(function(){for(var e;e=this.firstChild;)3===e.nodeType&&0===e.data.length?this.removeChild(e):this.parentNode.insertBefore(e,this)}).remove():n.remove(),1<n.length?n.toArray():n[0]},L=function(e,t,n){h(e).toggleClass(t,n).each(function(){""===this.className&&pn(this).attr("class",null)})},I=function(t,e,n){return T(e,function(e){return Yt.is(e,"array")&&(t=t.cloneNode(!0)),n&&ai(ui(e.childNodes),function(e){t.appendChild(e)}),e.parentNode.replaceChild(t,e)})},M=function(){return a.createRange()},F=function(e,t,n,r){if(Yt.isArray(e)){for(var o=e.length;o--;)e[o]=F(e[o],t,n,r);return e}return!u.collect||e!==a&&e!==i||l.push([e,t,n,r]),m.bind(e,t,n,r||V)},z=function(e,t,n){var r;if(Yt.isArray(e)){for(r=e.length;r--;)e[r]=z(e[r],t,n);return e}if(l&&(e===a||e===i))for(r=l.length;r--;){var o=l[r];e!==o[0]||t&&t!==o[1]||n&&n!==o[2]||m.unbind(o[0],o[1],o[2])}return m.unbind(e,t,n)},U=function(e){if(e&&Do.isElement(e)){var t=e.getAttribute("data-mce-contenteditable");return t&&"inherit"!==t?t:"inherit"!==e.contentEditable?e.contentEditable:null}return null},V={doc:a,settings:u,win:i,files:o,stdMode:!0,boxModel:!0,styleSheetLoader:e,boundEvents:l,styles:d,schema:f,events:m,isBlock:function(e){if("string"==typeof e)return!!n[e];if(e){var t=e.nodeType;if(t)return!(1!==t||!n[e.nodeName])}return!1},$:g,$$:h,root:null,clone:function(t,e){if(!si||1!==t.nodeType||e)return t.cloneNode(e);if(!e){var n=a.createElement(t.nodeName);return ai(b(t),function(e){y(n,e.nodeName,v(t,e.nodeName))}),n}return null},getRoot:C,getViewPort:function(e){var t=e||i,n=t.document,r=n.documentElement;return{x:t.pageXOffset||r.scrollLeft,y:t.pageYOffset||r.scrollTop,w:t.innerWidth||r.clientWidth,h:t.innerHeight||r.clientHeight}},getRect:function(e){var t,n;return e=p(e),t=x(e),n=N(e),{x:t.x,y:t.y,w:n.w,h:n.h}},getSize:N,getParent:function(e,t,n){var r=S(e,t,n,!1);return r&&0<r.length?r[0]:null},getParents:S,get:p,getNext:function(e,t){return k(e,t,"nextSibling")},getPrev:function(e,t){return k(e,t,"previousSibling")},select:function(e,t){return kt(e,p(t)||u.root_element||a,[])},is:E,add:_,create:D,createHTML:function(e,t,n){var r,o="";for(r in o+="<"+e,t)t.hasOwnProperty(r)&&null!==t[r]&&"undefined"!=typeof t[r]&&(o+=" "+r+'="'+O(t[r])+'"');return void 0!==n?o+">"+n+"</"+e+">":o+" />"},createFragment:function(e){var t,n=a.createElement("div"),r=a.createDocumentFragment();for(e&&(n.innerHTML=e);t=n.firstChild;)r.appendChild(t);return r},remove:P,setStyle:function(e,t,n){var r=h(e).css(t,n);u.update_styles&&di(d,r)},getStyle:w,setStyles:function(e,t){var n=h(e).css(t);u.update_styles&&di(d,n)},removeAllAttribs:function(e){return T(e,function(e){var t,n=e.attributes;for(t=n.length-1;0<=t;t--)e.removeAttributeNode(n.item(t))})},setAttrib:y,setAttribs:A,getAttrib:v,getPos:x,parseStyle:function(e){return d.parse(e)},serializeStyle:function(e,t){return d.serialize(e,t)},addStyle:function(e){var t,n;if(V!==gi.DOM&&a===document){if(r[e])return;r[e]=!0}(n=a.getElementById("mceDefaultStyles"))||((n=a.createElement("style")).id="mceDefaultStyles",n.type="text/css",(t=a.getElementsByTagName("head")[0]).firstChild?t.insertBefore(n,t.firstChild):t.appendChild(n)),n.styleSheet?n.styleSheet.cssText+=e:n.appendChild(a.createTextNode(e))},loadCSS:function(e){var n;V===gi.DOM||a!==document?(e||(e=""),n=a.getElementsByTagName("head")[0],ai(e.split(","),function(e){var t;e=Yt._addCacheSuffix(e),o[e]||(o[e]=!0,t=D("link",{rel:"stylesheet",href:e}),n.appendChild(t))})):gi.DOM.loadCSS(e)},addClass:function(e,t){h(e).addClass(t)},removeClass:function(e,t){L(e,t,!1)},hasClass:function(e,t){return h(e).hasClass(t)},toggleClass:L,show:function(e){h(e).show()},hide:function(e){h(e).hide()},isHidden:function(e){return"none"===h(e).css("display")},uniqueId:function(e){return(e||"mce_")+t++},setHTML:R,getOuterHTML:function(e){var t="string"==typeof e?p(e):e;return Do.isElement(t)?t.outerHTML:pn("<div></div>").append(pn(t).clone()).html()},setOuterHTML:function(e,t){h(e).each(function(){try{if("outerHTML"in this)return void(this.outerHTML=t)}catch(e){}P(pn(this).html(t),!0)})},decode:B,encode:O,insertAfter:function(e,t){var r=p(t);return T(e,function(e){var t,n;return t=r.parentNode,(n=r.nextSibling)?t.insertBefore(e,n):t.appendChild(e),e})},replace:I,rename:function(t,e){var n;return t.nodeName!==e.toUpperCase()&&(n=D(e),ai(b(t),function(e){y(n,e.nodeName,v(t,e.nodeName))}),I(n,t,!0)),n||t},findCommonAncestor:function(e,t){for(var n,r=e;r;){for(n=t;n&&r!==n;)n=n.parentNode;if(r===n)break;r=r.parentNode}return!r&&e.ownerDocument?e.ownerDocument.documentElement:r},toHex:function(e){return d.toHex(Yt.trim(e))},run:T,getAttribs:b,isEmpty:function(e,t){var n,r,o,i,a,u,s=0;if(e=e.firstChild){a=new ro(e,e.parentNode),t=t||(f?f.getNonEmptyElements():null),i=f?f.getWhiteSpaceElements():{};do{if(o=e.nodeType,Do.isElement(e)){var c=e.getAttribute("data-mce-bogus");if(c){e=a.next("all"===c);continue}if(u=e.nodeName.toLowerCase(),t&&t[u]){if("br"===u){s++,e=a.next();continue}return!1}for(n=(r=b(e)).length;n--;)if("name"===(u=r[n].nodeName)||"data-mce-bookmark"===u)return!1}if(8===o)return!1;if(3===o&&!li.test(e.nodeValue))return!1;if(3===o&&e.parentNode&&i[e.parentNode.nodeName]&&li.test(e.nodeValue))return!1;e=a.next()}while(e)}return s<=1},createRng:M,nodeIndex:mi,split:function(e,t,n){var r,o,i,a=M();if(e&&t)return a.setStart(e.parentNode,mi(e)),a.setEnd(t.parentNode,mi(t)),r=a.extractContents(),(a=M()).setStart(t.parentNode,mi(t)+1),a.setEnd(e.parentNode,mi(e)+1),o=a.extractContents(),(i=e.parentNode).insertBefore(Po.trimNode(V,r),e),n?i.insertBefore(n,e):i.insertBefore(t,e),i.insertBefore(Po.trimNode(V,o),e),P(e),n||t},bind:F,unbind:z,fire:function(e,t,n){return m.fire(e,t,n)},getContentEditable:U,getContentEditableParent:function(e){for(var t=C(),n=null;e&&e!==t&&null===(n=U(e));e=e.parentNode);return n},destroy:function(){if(l)for(var e=l.length;e--;){var t=l[e];m.unbind(t[0],t[1],t[2])}kt.setDocument&&kt.setDocument()},isChildOf:function(e,t){for(;e;){if(t===e)return!0;e=e.parentNode}return!1},dumpRng:function(e){return"startContainer: "+e.startContainer.nodeName+", startOffset: "+e.startOffset+", endContainer: "+e.endContainer.nodeName+", endOffset: "+e.endOffset}};return s=fi(d,u,function(){return V}),V}(ii=gi||(gi={})).DOM=ii(document),ii.nodeIndex=mi;var pi=gi,hi=pi.DOM,vi=Yt.each,bi=Yt.grep,yi=function(e){return"function"==typeof e},Ci=function(){var l={},o=[],i={},a=[],f=0;this.isDone=function(e){return 2===l[e]},this.markDone=function(e){l[e]=2},this.add=this.load=function(e,t,n,r){l[e]===undefined&&(o.push(e),l[e]=0),t&&(i[e]||(i[e]=[]),i[e].push({success:t,failure:r,scope:n||this}))},this.remove=function(e){delete l[e],delete i[e]},this.loadQueue=function(e,t,n){this.loadScripts(o,e,t,n)},this.loadScripts=function(n,e,t,r){var u,s=[],c=function(t,e){vi(i[e],function(e){yi(e[t])&&e[t].call(e.scope)}),i[e]=undefined};a.push({success:e,failure:r,scope:t||this}),(u=function(){var e=bi(n);if(n.length=0,vi(e,function(e){var t,n,r,o,i,a;2!==l[e]?3!==l[e]?1!==l[e]&&(l[e]=1,f++,t=e,n=function(){l[e]=2,f--,c("success",e),u()},r=function(){l[e]=3,f--,s.push(e),c("failure",e),u()},i=(a=hi).uniqueId(),(o=document.createElement("script")).id=i,o.type="text/javascript",o.src=Yt._addCacheSuffix(t),o.onload=function(){a.remove(i),o&&(o.onreadystatechange=o.onload=o=null),n()},o.onerror=function(){yi(r)?r():"undefined"!=typeof console&&console.log&&console.log("Failed to load script: "+t)},(document.getElementsByTagName("head")[0]||document.body).appendChild(o)):c("failure",e):c("success",e)}),!f){var t=a.slice(0);a.length=0,vi(t,function(e){0===s.length?yi(e.success)&&e.success.call(e.scope):yi(e.failure)&&e.failure.call(e.scope,s)})}})()}};Ci.ScriptLoader=new Ci;var xi,wi=Yt.each;function Ni(){var r=this,o=[],a={},u={},i=[],s=function(e){var t;return u[e]&&(t=u[e].dependencies),t||[]},c=function(e,t){return"object"==typeof t?t:"string"==typeof e?{prefix:"",resource:t,suffix:""}:{prefix:e.prefix,resource:t,suffix:e.suffix}},l=function(e,n,t,r){var o=s(e);wi(o,function(e){var t=c(n,e);f(t.resource,t,undefined,undefined)}),t&&(r?t.call(r):t.call(Ci))},f=function(e,t,n,r,o){if(!a[e]){var i="string"==typeof t?t:t.prefix+t.resource+t.suffix;0!==i.indexOf("/")&&-1===i.indexOf("://")&&(i=Ni.baseURL+"/"+i),a[e]=i.substring(0,i.lastIndexOf("/")),u[e]?l(e,t,n,r):Ci.ScriptLoader.add(i,function(){return l(e,t,n,r)},r,o)}};return{items:o,urls:a,lookup:u,_listeners:i,get:function(e){return u[e]?u[e].instance:undefined},dependencies:s,requireLangPack:function(e,t){var n=Ni.language;if(n&&!1!==Ni.languageLoad){if(t)if(-1!==(t=","+t+",").indexOf(","+n.substr(0,2)+","))n=n.substr(0,2);else if(-1===t.indexOf(","+n+","))return;Ci.ScriptLoader.add(a[e]+"/langs/"+n+".js")}},add:function(t,e,n){o.push(e),u[t]={instance:e,dependencies:n};var r=W(i,function(e){return e.name===t});return i=r.fail,wi(r.pass,function(e){e.callback()}),e},remove:function(e){delete a[e],delete u[e]},createUrl:c,addComponents:function(e,t){var n=r.urls[e];wi(t,function(e){Ci.ScriptLoader.add(n+"/"+e)})},load:f,waitFor:function(e,t){u.hasOwnProperty(e)?t():i.push({name:e,callback:t})}}}(xi=Ni||(Ni={})).PluginManager=xi(),xi.ThemeManager=xi();var Ei=function(t,n){Ir(t).each(function(e){e.dom().insertBefore(n.dom(),t.dom())})},Si=function(e,t){Fr(e).fold(function(){Ir(e).each(function(e){Ti(e,t)})},function(e){Ei(e,t)})},ki=function(t,n){jr(t).fold(function(){Ti(t,n)},function(e){t.dom().insertBefore(n.dom(),e.dom())})},Ti=function(e,t){e.dom().appendChild(t.dom())},Ai=function(t,e){F(e,function(e){Ti(t,e)})},Ri=function(e){e.dom().textContent="",F(Vr(e),function(e){_i(e)})},_i=function(e){var t=e.dom();null!==t.parentNode&&t.parentNode.removeChild(t)},Di=function(e){var t,n=Vr(e);0<n.length&&(t=e,F(n,function(e){Ei(t,e)})),_i(e)},Bi=function(n,r){var o=null;return{cancel:function(){null!==o&&(clearTimeout(o),o=null)},throttle:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];null===o&&(o=setTimeout(function(){n.apply(null,e),o=null},r))}}},Oi=function(e){var t=e,n=function(){return t};return{get:n,set:function(e){t=e},clone:function(){return Oi(n())}}},Pi=function(e,t){var n=yr(e,t);return n===undefined||""===n?[]:n.split(" ")},Li=function(e){return e.dom().classList!==undefined},Ii=function(e,t){return o=t,i=Pi(n=e,r="class").concat([o]),vr(n,r,i.join(" ")),!0;var n,r,o,i},Mi=function(e,t){return o=t,0<(i=z(Pi(n=e,r="class"),function(e){return e!==o})).length?vr(n,r,i.join(" ")):Cr(n,r),!1;var n,r,o,i},Fi=function(e,t){Li(e)?e.dom().classList.add(t):Ii(e,t)},zi=function(e){0===(Li(e)?e.dom().classList:Pi(e,"class")).length&&Cr(e,"class")},Ui=function(e,t){return Li(e)&&e.dom().classList.contains(t)},Vi=function(e,t){var n=[];return F(Vr(e),function(e){t(e)&&(n=n.concat([e])),n=n.concat(Vi(e,t))}),n},Hi=function(e,t){return n=t,o=(r=e)===undefined?document:r.dom(),Br(o)?[]:$(o.querySelectorAll(n),rr.fromDom);var n,r,o};function ji(e,t,n,r,o){return e(n,r)?A.some(n):P(o)&&o(n)?A.none():t(n,r,o)}var qi,$i=function(e,t,n){for(var r=e.dom(),o=P(n)?n:j(!1);r.parentNode;){r=r.parentNode;var i=rr.fromDom(r);if(t(i))return A.some(i);if(o(i))break}return A.none()},Wi=function(e,t,n){return ji(function(e){return t(e)},$i,e,t,n)},Ki=function(e,t,n){return $i(e,function(e){return Dr(e,t)},n)},Xi=function(e,t){return n=t,o=(r=e)===undefined?document:r.dom(),Br(o)?A.none():A.from(o.querySelector(n)).map(rr.fromDom);var n,r,o},Yi=function(e,t,n){return ji(Dr,Ki,e,t,n)},Gi=j("mce-annotation"),Ji=j("data-mce-annotation"),Qi=j("data-mce-annotation-uid"),Zi=function(r,e){var t=r.selection.getRng(),n=rr.fromDom(t.startContainer),o=rr.fromDom(r.getBody()),i=e.fold(function(){return"."+Gi()},function(e){return"["+Ji()+'="'+e+'"]'}),a=Hr(n,t.startOffset).getOr(n),u=Yi(a,i,function(e){return Or(e,o)}),s=function(e,t){return n=t,(r=e.dom())&&r.hasAttribute&&r.hasAttribute(n)?A.some(yr(e,t)):A.none();var n,r};return u.bind(function(e){return s(e,""+Qi()).bind(function(n){return s(e,""+Ji()).map(function(e){var t=ea(r,n);return{uid:n,name:e,elements:t}})})})},ea=function(e,t){var n=rr.fromDom(e.getBody());return Hi(n,"["+Qi()+'="'+t+'"]')},ta=function(i,e){var n,r,o,a=Oi({}),c=function(e,t){u(e,function(e){return t(e),e})},u=function(e,t){var n=a.get(),r=t(n.hasOwnProperty(e)?n[e]:{listeners:[],previous:Oi(A.none())});n[e]=r,a.set(n)},t=(n=function(){var e,t,n,r=a.get(),o=(e=fr(r),(n=Q.call(e,0)).sort(t),n);F(o,function(e){u(e,function(u){var s=u.previous.get();return Zi(i,A.some(e)).fold(function(){var t;s.isSome()&&(c(t=e,function(e){F(e.listeners,function(e){return e(!1,t)})}),u.previous.set(A.none()))},function(e){var t,n,r,o=e.uid,i=e.name,a=e.elements;s.is(o)||(n=o,r=a,c(t=i,function(e){F(e.listeners,function(e){return e(!0,t,{uid:n,nodes:$(r,function(e){return e.dom()})})})}),u.previous.set(A.some(o)))}),{previous:u.previous,listeners:u.listeners}})})},r=30,o=null,{cancel:function(){null!==o&&(clearTimeout(o),o=null)},throttle:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];null!==o&&clearTimeout(o),o=setTimeout(function(){n.apply(null,e),o=null},r)}});return i.on("remove",function(){t.cancel()}),i.on("nodeChange",function(){t.throttle()}),{addListener:function(e,t){u(e,function(e){return{previous:e.previous,listeners:e.listeners.concat([t])}})}}},na=function(e,n){e.on("init",function(){e.serializer.addNodeFilter("span",function(e){F(e,function(t){var e;(e=t,A.from(e.attributes.map[Ji()]).bind(n.lookup)).each(function(e){!1===e.persistent&&t.unwrap()})})})})},ra=0,oa=function(e,t){return rr.fromDom(e.dom().cloneNode(t))},ia=function(e){return oa(e,!1)},aa=function(e){return oa(e,!0)},ua=function(e,t){var n,r,o=Lr(e).dom(),i=rr.fromDom(o.createDocumentFragment()),a=(n=t,(r=(o||document).createElement("div")).innerHTML=n,Vr(rr.fromDom(r)));Ai(i,a),Ri(e),Ti(e,i)},sa="\ufeff",ca=function(e){return e===sa},la=sa,fa=function(e){return e.replace(new RegExp(sa,"g"),"")},da=Do.isElement,ma=Do.isText,ga=function(e){return ma(e)&&(e=e.parentNode),da(e)&&e.hasAttribute("data-mce-caret")},pa=function(e){return ma(e)&&ca(e.data)},ha=function(e){return ga(e)||pa(e)},va=function(e){return e.firstChild!==e.lastChild||!Do.isBr(e.firstChild)},ba=function(e){var t=e.container();return!(!e||!Do.isText(t))&&(t.data.charAt(e.offset())===la||e.isAtStart()&&pa(t.previousSibling))},ya=function(e){var t=e.container();return!(!e||!Do.isText(t))&&(t.data.charAt(e.offset()-1)===la||e.isAtEnd()&&pa(t.nextSibling))},Ca=function(e,t,n){var r,o,i;return(r=t.ownerDocument.createElement(e)).setAttribute("data-mce-caret",n?"before":"after"),r.setAttribute("data-mce-bogus","all"),r.appendChild(((i=document.createElement("br")).setAttribute("data-mce-bogus","1"),i)),o=t.parentNode,n?o.insertBefore(r,t):t.nextSibling?o.insertBefore(r,t.nextSibling):o.appendChild(r),r},xa=function(e){return ma(e)&&e.data[0]===la},wa=function(e){return ma(e)&&e.data[e.data.length-1]===la},Na=function(e){return e&&e.hasAttribute("data-mce-caret")?(t=e.getElementsByTagName("br"),n=t[t.length-1],Do.isBogus(n)&&n.parentNode.removeChild(n),e.removeAttribute("data-mce-caret"),e.removeAttribute("data-mce-bogus"),e.removeAttribute("style"),e.removeAttribute("_moz_abspos"),e):null;var t,n},Ea=Do.isContentEditableTrue,Sa=Do.isContentEditableFalse,ka=Do.isBr,Ta=Do.isText,Aa=Do.matchNodeNames("script style textarea"),Ra=Do.matchNodeNames("img input textarea hr iframe video audio object"),_a=Do.matchNodeNames("table"),Da=ha,Ba=function(e){return!Da(e)&&(Ta(e)?!Aa(e.parentNode):Ra(e)||ka(e)||_a(e)||Oa(e))},Oa=function(e){return!1===(t=e,Do.isElement(t)&&"true"===t.getAttribute("unselectable"))&&Sa(e);var t},Pa=function(e,t){return Ba(e)&&function(e,t){for(e=e.parentNode;e&&e!==t;e=e.parentNode){if(Oa(e))return!1;if(Ea(e))return!0}return!0}(e,t)},La=Math.round,Ia=function(e){return e?{left:La(e.left),top:La(e.top),bottom:La(e.bottom),right:La(e.right),width:La(e.width),height:La(e.height)}:{left:0,top:0,bottom:0,right:0,width:0,height:0}},Ma=function(e,t){return e=Ia(e),t||(e.left=e.left+e.width),e.right=e.left,e.width=0,e},Fa=function(e,t,n){return 0<=e&&e<=Math.min(t.height,n.height)/2},za=function(e,t){return e.bottom-e.height/2<t.top||!(e.top>t.bottom)&&Fa(t.top-e.bottom,e,t)},Ua=function(e,t){return e.top>t.bottom||!(e.bottom<t.top)&&Fa(t.bottom-e.top,e,t)},Va=function(e){var t=e.startContainer,n=e.startOffset;return t.hasChildNodes()&&e.endOffset===n+1?t.childNodes[n]:null},Ha=function(e,t){return 1===e.nodeType&&e.hasChildNodes()&&(t>=e.childNodes.length&&(t=e.childNodes.length-1),e=e.childNodes[t]),e},ja=new RegExp("[\u0300-\u036f\u0483-\u0487\u0488-\u0489\u0591-\u05bd\u05bf\u05c1-\u05c2\u05c4-\u05c5\u05c7\u0610-\u061a\u064b-\u065f\u0670\u06d6-\u06dc\u06df-\u06e4\u06e7-\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0859-\u085b\u08e3-\u0902\u093a\u093c\u0941-\u0948\u094d\u0951-\u0957\u0962-\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2-\u09e3\u0a01-\u0a02\u0a3c\u0a41-\u0a42\u0a47-\u0a48\u0a4b-\u0a4d\u0a51\u0a70-\u0a71\u0a75\u0a81-\u0a82\u0abc\u0ac1-\u0ac5\u0ac7-\u0ac8\u0acd\u0ae2-\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62-\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c00\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55-\u0c56\u0c62-\u0c63\u0c81\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc-\u0ccd\u0cd5-\u0cd6\u0ce2-\u0ce3\u0d01\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62-\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb-\u0ebc\u0ec8-\u0ecd\u0f18-\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86-\u0f87\u0f8d-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039-\u103a\u103d-\u103e\u1058-\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085-\u1086\u108d\u109d\u135d-\u135f\u1712-\u1714\u1732-\u1734\u1752-\u1753\u1772-\u1773\u17b4-\u17b5\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927-\u1928\u1932\u1939-\u193b\u1a17-\u1a18\u1a1b\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1ab0-\u1abd\u1abe\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80-\u1b81\u1ba2-\u1ba5\u1ba8-\u1ba9\u1bab-\u1bad\u1be6\u1be8-\u1be9\u1bed\u1bef-\u1bf1\u1c2c-\u1c33\u1c36-\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1cf4\u1cf8-\u1cf9\u1dc0-\u1df5\u1dfc-\u1dff\u200c-\u200d\u20d0-\u20dc\u20dd-\u20e0\u20e1\u20e2-\u20e4\u20e5-\u20f0\u2cef-\u2cf1\u2d7f\u2de0-\u2dff\u302a-\u302d\u302e-\u302f\u3099-\u309a\ua66f\ua670-\ua672\ua674-\ua67d\ua69e-\ua69f\ua6f0-\ua6f1\ua802\ua806\ua80b\ua825-\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\ua9e5\uaa29-\uaa2e\uaa31-\uaa32\uaa35-\uaa36\uaa43\uaa4c\uaa7c\uaab0\uaab2-\uaab4\uaab7-\uaab8\uaabe-\uaabf\uaac1\uaaec-\uaaed\uaaf6\uabe5\uabe8\uabed\ufb1e\ufe00-\ufe0f\ufe20-\ufe2f\uff9e-\uff9f]"),qa=function(e){return"string"==typeof e&&768<=e.charCodeAt(0)&&ja.test(e)},$a=function(e,t){for(var n=[],r=0;r<e.length;r++){var o=e[r];if(!o.isSome())return A.none();n.push(o.getOrDie())}return A.some(t.apply(null,n))},Wa=[].slice,Ka=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=Wa.call(arguments);return function(e){for(var t=0;t<n.length;t++)if(!n[t](e))return!1;return!0}},Xa=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=Wa.call(arguments);return function(e){for(var t=0;t<n.length;t++)if(n[t](e))return!0;return!1}},Ya=Do.isElement,Ga=Ba,Ja=Do.matchStyleValues("display","block table"),Qa=Do.matchStyleValues("float","left right"),Za=Ka(Ya,Ga,y(Qa)),eu=y(Do.matchStyleValues("white-space","pre pre-line pre-wrap")),tu=Do.isText,nu=Do.isBr,ru=pi.nodeIndex,ou=Ha,iu=function(e){return"createRange"in e?e.createRange():pi.DOM.createRng()},au=function(e){return e&&/[\r\n\t ]/.test(e)},uu=function(e){return!!e.setStart&&!!e.setEnd},su=function(e){var t,n=e.startContainer,r=e.startOffset;return!!(au(e.toString())&&eu(n.parentNode)&&Do.isText(n)&&(t=n.data,au(t[r-1])||au(t[r+1])))},cu=function(e){return 0===e.left&&0===e.right&&0===e.top&&0===e.bottom},lu=function(e){var t,n,r,o,i,a,u,s;return t=0<(n=e.getClientRects()).length?Ia(n[0]):Ia(e.getBoundingClientRect()),!uu(e)&&nu(e)&&cu(t)?(i=(r=e).ownerDocument,a=iu(i),u=i.createTextNode("\xa0"),(s=r.parentNode).insertBefore(u,r),a.setStart(u,0),a.setEnd(u,1),o=Ia(a.getBoundingClientRect()),s.removeChild(u),o):cu(t)&&uu(e)?function(e){var t=e.startContainer,n=e.endContainer,r=e.startOffset,o=e.endOffset;if(t===n&&Do.isText(n)&&0===r&&1===o){var i=e.cloneRange();return i.setEndAfter(n),lu(i)}return null}(e):t},fu=function(e,t){var n=Ma(e,t);return n.width=1,n.right=n.left+1,n},du=function(e){var t,n,r=[],o=function(e){var t,n;0!==e.height&&(0<r.length&&(t=e,n=r[r.length-1],t.left===n.left&&t.top===n.top&&t.bottom===n.bottom&&t.right===n.right)||r.push(e))},i=function(e,t){var n=iu(e.ownerDocument);if(t<e.data.length){if(qa(e.data[t]))return r;if(qa(e.data[t-1])&&(n.setStart(e,t),n.setEnd(e,t+1),!su(n)))return o(fu(lu(n),!1)),r}0<t&&(n.setStart(e,t-1),n.setEnd(e,t),su(n)||o(fu(lu(n),!1))),t<e.data.length&&(n.setStart(e,t),n.setEnd(e,t+1),su(n)||o(fu(lu(n),!0)))};if(tu(e.container()))return i(e.container(),e.offset()),r;if(Ya(e.container()))if(e.isAtEnd())n=ou(e.container(),e.offset()),tu(n)&&i(n,n.data.length),Za(n)&&!nu(n)&&o(fu(lu(n),!1));else{if(n=ou(e.container(),e.offset()),tu(n)&&i(n,0),Za(n)&&e.isAtEnd())return o(fu(lu(n),!1)),r;t=ou(e.container(),e.offset()-1),Za(t)&&!nu(t)&&(Ja(t)||Ja(n)||!Za(n))&&o(fu(lu(t),!1)),Za(n)&&o(fu(lu(n),!0))}return r};function mu(t,n,e){var r=function(){return e||(e=du(mu(t,n))),e};return{container:j(t),offset:j(n),toRange:function(){var e;return(e=iu(t.ownerDocument)).setStart(t,n),e.setEnd(t,n),e},getClientRects:r,isVisible:function(){return 0<r().length},isAtStart:function(){return tu(t),0===n},isAtEnd:function(){return tu(t)?n>=t.data.length:n>=t.childNodes.length},isEqual:function(e){return e&&t===e.container()&&n===e.offset()},getNode:function(e){return ou(t,e?n-1:n)}}}(qi=mu||(mu={})).fromRangeStart=function(e){return qi(e.startContainer,e.startOffset)},qi.fromRangeEnd=function(e){return qi(e.endContainer,e.endOffset)},qi.after=function(e){return qi(e.parentNode,ru(e)+1)},qi.before=function(e){return qi(e.parentNode,ru(e))},qi.isAbove=function(e,t){return $a([ee(t.getClientRects()),te(e.getClientRects())],za).getOr(!1)},qi.isBelow=function(e,t){return $a([te(t.getClientRects()),ee(e.getClientRects())],Ua).getOr(!1)},qi.isAtStart=function(e){return!!e&&e.isAtStart()},qi.isAtEnd=function(e){return!!e&&e.isAtEnd()},qi.isTextPosition=function(e){return!!e&&Do.isText(e.container())},qi.isElementPosition=function(e){return!1===qi.isTextPosition(e)};var gu,pu,hu,vu=mu,bu=Do.isText,yu=Do.isBogus,Cu=pi.nodeIndex,xu=function(e){var t=e.parentNode;return yu(t)?xu(t):t},wu=function(e){return e?qt.reduce(e.childNodes,function(e,t){return yu(t)&&"BR"!==t.nodeName?e=e.concat(wu(t)):e.push(t),e},[]):[]},Nu=function(t){return function(e){return t===e}},Eu=function(e){var t,r,n,o;return(bu(e)?"text()":e.nodeName.toLowerCase())+"["+(r=wu(xu(t=e)),n=qt.findIndex(r,Nu(t),t),r=r.slice(0,n+1),o=qt.reduce(r,function(e,t,n){return bu(t)&&bu(r[n-1])&&e++,e},0),r=qt.filter(r,Do.matchNodeNames(t.nodeName)),(n=qt.findIndex(r,Nu(t),t))-o)+"]"},Su=function(e,t){var n,r,o,i,a,u=[];return n=t.container(),r=t.offset(),bu(n)?o=function(e,t){for(;(e=e.previousSibling)&&bu(e);)t+=e.data.length;return t}(n,r):(r>=(i=n.childNodes).length?(o="after",r=i.length-1):o="before",n=i[r]),u.push(Eu(n)),a=function(e,t,n){var r=[];for(t=t.parentNode;!(t===e||n&&n(t));t=t.parentNode)r.push(t);return r}(e,n),a=qt.filter(a,y(Do.isBogus)),(u=u.concat(qt.map(a,function(e){return Eu(e)}))).reverse().join("/")+","+o},ku=function(e,t){var n,r,o;return t?(t=(n=t.split(","))[0].split("/"),o=1<n.length?n[1]:"before",(r=qt.reduce(t,function(e,t){return(t=/([\w\-\(\)]+)\[([0-9]+)\]/.exec(t))?("text()"===t[1]&&(t[1]="#text"),n=e,r=t[1],o=parseInt(t[2],10),i=wu(n),i=qt.filter(i,function(e,t){return!bu(e)||!bu(i[t-1])}),(i=qt.filter(i,Do.matchNodeNames(r)))[o]):null;var n,r,o,i},e))?bu(r)?function(e,t){for(var n,r=e,o=0;bu(r);){if(n=r.data.length,o<=t&&t<=o+n){e=r,t-=o;break}if(!bu(r.nextSibling)){e=r,t=n;break}o+=n,r=r.nextSibling}return bu(e)&&t>e.data.length&&(t=e.data.length),vu(e,t)}(r,parseInt(o,10)):(o="after"===o?Cu(r)+1:Cu(r),vu(r.parentNode,o)):null):null},Tu=function(e,t){Do.isText(t)&&0===t.data.length&&e.remove(t)},Au=function(e,t,n){var r,o,i,a,u,s,c;Do.isDocumentFragment(n)?(i=e,a=t,u=n,s=A.from(u.firstChild),c=A.from(u.lastChild),a.insertNode(u),s.each(function(e){return Tu(i,e.previousSibling)}),c.each(function(e){return Tu(i,e.nextSibling)})):(r=e,o=n,t.insertNode(o),Tu(r,o.previousSibling),Tu(r,o.nextSibling))},Ru=Do.isContentEditableFalse,_u=function(e,t,n,r,o){var i,a=r[o?"startContainer":"endContainer"],u=r[o?"startOffset":"endOffset"],s=[],c=0,l=e.getRoot();for(Do.isText(a)?s.push(n?function(e,t,n){var r,o;for(o=e(t.data.slice(0,n)).length,r=t.previousSibling;r&&Do.isText(r);r=r.previousSibling)o+=e(r.data).length;return o}(t,a,u):u):(u>=(i=a.childNodes).length&&i.length&&(c=1,u=Math.max(0,i.length-1)),s.push(e.nodeIndex(i[u],n)+c));a&&a!==l;a=a.parentNode)s.push(e.nodeIndex(a,n));return s},Du=function(e,t,n){var r=0;return Yt.each(e.select(t),function(e){if("all"!==e.getAttribute("data-mce-bogus"))return e!==n&&void r++}),r},Bu=function(e,t){var n,r,o,i=t?"start":"end";n=e[i+"Container"],r=e[i+"Offset"],Do.isElement(n)&&"TR"===n.nodeName&&(n=(o=n.childNodes)[Math.min(t?r:r-1,o.length-1)])&&(r=t?0:n.childNodes.length,e["set"+(t?"Start":"End")](n,r))},Ou=function(e){return Bu(e,!0),Bu(e,!1),e},Pu=function(e,t){var n;if(Do.isElement(e)&&(e=Ha(e,t),Ru(e)))return e;if(ha(e)){if(Do.isText(e)&&ga(e)&&(e=e.parentNode),n=e.previousSibling,Ru(n))return n;if(n=e.nextSibling,Ru(n))return n}},Lu=function(e,t,n){var r=n.getNode(),o=r?r.nodeName:null,i=n.getRng();if(Ru(r)||"IMG"===o)return{name:o,index:Du(n.dom,o,r)};var a,u,s,c,l,f,d,m=Pu((a=i).startContainer,a.startOffset)||Pu(a.endContainer,a.endOffset);return m?{name:o=m.tagName,index:Du(n.dom,o,m)}:(u=e,c=t,l=i,f=(s=n).dom,(d={}).start=_u(f,u,c,l,!0),s.isCollapsed()||(d.end=_u(f,u,c,l,!1)),d)},Iu=function(e,t,n){var r={"data-mce-type":"bookmark",id:t,style:"overflow:hidden;line-height:0px"};return n?e.create("span",r,"&#xFEFF;"):e.create("span",r)},Mu=function(e,t){var n=e.dom,r=e.getRng(),o=n.uniqueId(),i=e.isCollapsed(),a=e.getNode(),u=a.nodeName;if("IMG"===u)return{name:u,index:Du(n,u,a)};var s=Ou(r.cloneRange());if(!i){s.collapse(!1);var c=Iu(n,o+"_end",t);Au(n,s,c)}(r=Ou(r)).collapse(!0);var l=Iu(n,o+"_start",t);return Au(n,r,l),e.moveToBookmark({id:o,keep:1}),{id:o}},Fu={getBookmark:function(e,t,n){return 2===t?Lu(fa,n,e):3===t?(o=(r=e).getRng(),{start:Su(r.dom.getRoot(),vu.fromRangeStart(o)),end:Su(r.dom.getRoot(),vu.fromRangeEnd(o))}):t?{rng:e.getRng()}:Mu(e,!1);var r,o},getUndoBookmark:d(Lu,q,!0),getPersistentBookmark:Mu},zu="_mce_caret",Uu=function(e){return Do.isElement(e)&&e.id===zu},Vu=function(e,t){for(;t&&t!==e;){if(t.id===zu)return t;t=t.parentNode}return null},Hu=Do.isElement,ju=Do.isText,qu=function(e){var t=e.parentNode;t&&t.removeChild(e)},$u=function(e,t){0===t.length?qu(e):e.nodeValue=t},Wu=function(e){var t=fa(e);return{count:e.length-t.length,text:t}},Ku=function(e,t){return Gu(e),t},Xu=function(e,t){var n,r,o,i=t.container(),a=(n=ne(i.childNodes),r=e,o=I(n,r),-1===o?A.none():A.some(o)).map(function(e){return e<t.offset()?vu(i,t.offset()-1):t}).getOr(t);return Gu(e),a},Yu=function(e,t){return ju(e)&&t.container()===e?(r=t,o=Wu((n=e).data.substr(0,r.offset())),i=Wu(n.data.substr(r.offset())),0<(a=o.text+i.text).length?($u(n,a),vu(n,r.offset()-o.count)):r):Ku(e,t);var n,r,o,i,a},Gu=function(e){if(Hu(e)&&ha(e)&&(va(e)?e.removeAttribute("data-mce-caret"):qu(e)),ju(e)){var t=fa(function(e){try{return e.nodeValue}catch(t){return""}}(e));$u(e,t)}},Ju={removeAndReposition:function(e,t){return vu.isTextPosition(t)?Yu(e,t):(n=e,(r=t).container()===n.parentNode?Xu(n,r):Ku(n,r));var n,r},remove:Gu},Qu=tr.detect().browser,Zu=Do.isContentEditableFalse,es=function(e,t,n){var r,o,i,a,u,s=Ma(t.getBoundingClientRect(),n);return"BODY"===e.tagName?(r=e.ownerDocument.documentElement,o=e.scrollLeft||r.scrollLeft,i=e.scrollTop||r.scrollTop):(u=e.getBoundingClientRect(),o=e.scrollLeft-u.left,i=e.scrollTop-u.top),s.left+=o,s.right+=o,s.top+=i,s.bottom+=i,s.width=1,0<(a=t.offsetWidth-t.clientWidth)&&(n&&(a*=-1),s.left+=a,s.right+=a),s},ts=function(a,u,e){var t,s,c=Oi(A.none()),l=function(){!function(e){var t,n,r,o,i;for(t=pn("*[contentEditable=false]",e),o=0;o<t.length;o++)r=(n=t[o]).previousSibling,wa(r)&&(1===(i=r.data).length?r.parentNode.removeChild(r):r.deleteData(i.length-1,1)),r=n.nextSibling,xa(r)&&(1===(i=r.data).length?r.parentNode.removeChild(r):r.deleteData(0,1))}(a),s&&(Ju.remove(s),s=null),c.get().each(function(e){pn(e.caret).remove(),c.set(A.none())}),clearInterval(t)},f=function(){t=ve.setInterval(function(){e()?pn("div.mce-visual-caret",a).toggleClass("mce-visual-caret-hidden"):pn("div.mce-visual-caret",a).addClass("mce-visual-caret-hidden")},500)};return{show:function(t,e){var n,r,o;if(l(),o=e,Do.isElement(o)&&/^(TD|TH)$/i.test(o.tagName))return null;if(!u(e))return s=function(e,t){var n,r,o;if(r=e.ownerDocument.createTextNode(la),o=e.parentNode,t){if(n=e.previousSibling,ma(n)){if(ha(n))return n;if(wa(n))return n.splitText(n.data.length-1)}o.insertBefore(r,e)}else{if(n=e.nextSibling,ma(n)){if(ha(n))return n;if(xa(n))return n.splitText(1),n}e.nextSibling?o.insertBefore(r,e.nextSibling):o.appendChild(r)}return r}(e,t),r=e.ownerDocument.createRange(),Zu(s.nextSibling)?(r.setStart(s,0),r.setEnd(s,0)):(r.setStart(s,1),r.setEnd(s,1)),r;s=Ca("p",e,t),n=es(a,e,t),pn(s).css("top",n.top);var i=pn('<div class="mce-visual-caret" data-mce-bogus="all"></div>').css(n).appendTo(a)[0];return c.set(A.some({caret:i,element:e,before:t})),c.get().each(function(e){t&&pn(e.caret).addClass("mce-visual-caret-before")}),f(),(r=e.ownerDocument.createRange()).setStart(s,0),r.setEnd(s,0),r},hide:l,getCss:function(){return".mce-visual-caret {position: absolute;background-color: black;background-color: currentcolor;}.mce-visual-caret-hidden {display: none;}*[data-mce-caret] {position: absolute;left: -1000px;right: auto;top: 0;margin: 0;padding: 0;}"},reposition:function(){c.get().each(function(e){var t=es(a,e.element,e.before);pn(e.caret).css(t)})},destroy:function(){return ve.clearInterval(t)}}},ns=function(){return Qu.isIE()||Qu.isEdge()||Qu.isFirefox()},rs=function(e){return Zu(e)||Do.isTable(e)&&ns()},os=(gu="\xa0",function(e){return gu===e}),is=function(e){return/^[\r\n\t ]$/.test(e)},as=function(e){return!is(e)&&!os(e)},us=Do.isContentEditableFalse,ss=Do.matchStyleValues("display","block table table-cell table-caption list-item"),cs=ha,ls=ga,fs=Do.isElement,ds=Ba,ms=function(e){return 0<e},gs=function(e){return e<0},ps=function(e,t){for(var n;n=e(t);)if(!ls(n))return n;return null},hs=function(e,t,n,r,o){var i=new ro(e,r);if(gs(t)){if((us(e)||ls(e))&&n(e=ps(i.prev,!0)))return e;for(;e=ps(i.prev,o);)if(n(e))return e}if(ms(t)){if((us(e)||ls(e))&&n(e=ps(i.next,!0)))return e;for(;e=ps(i.next,o);)if(n(e))return e}return null},vs=function(e,t){for(;e&&e!==t;){if(ss(e))return e;e=e.parentNode}return null},bs=function(e,t,n){return vs(e.container(),n)===vs(t.container(),n)},ys=function(e,t){var n,r;return t?(n=t.container(),r=t.offset(),fs(n)?n.childNodes[r+e]:null):null},Cs=function(e,t){var n=t.ownerDocument.createRange();return e?(n.setStartBefore(t),n.setEndBefore(t)):(n.setStartAfter(t),n.setEndAfter(t)),n},xs=function(e,t,n){var r,o,i,a;for(o=e?"previousSibling":"nextSibling";n&&n!==t;){if(r=n[o],cs(r)&&(r=r[o]),us(r)){if(a=n,vs(r,i=t)===vs(a,i))return r;break}if(ds(r))break;n=n.parentNode}return null},ws=d(Cs,!0),Ns=d(Cs,!1),Es=function(e,t,n){var r,o,i,a,u=d(xs,!0,t),s=d(xs,!1,t);if(o=n.startContainer,i=n.startOffset,ga(o)){if(fs(o)||(o=o.parentNode),"before"===(a=o.getAttribute("data-mce-caret"))&&(r=o.nextSibling,rs(r)))return ws(r);if("after"===a&&(r=o.previousSibling,rs(r)))return Ns(r)}if(!n.collapsed)return n;if(Do.isText(o)){if(cs(o)){if(1===e){if(r=s(o))return ws(r);if(r=u(o))return Ns(r)}if(-1===e){if(r=u(o))return Ns(r);if(r=s(o))return ws(r)}return n}if(wa(o)&&i>=o.data.length-1)return 1===e&&(r=s(o))?ws(r):n;if(xa(o)&&i<=1)return-1===e&&(r=u(o))?Ns(r):n;if(i===o.data.length)return(r=s(o))?ws(r):n;if(0===i)return(r=u(o))?Ns(r):n}return n},Ss=function(e,t){var n=ys(e,t);return us(n)&&!Do.isBogusAll(n)},ks=function(e,t){return Do.isTable(ys(e,t))},Ts=function(e,t){return A.from(ys(e?0:-1,t)).filter(us)},As=function(e,t,n){var r=Es(e,t,n);return-1===e?mu.fromRangeStart(r):mu.fromRangeEnd(r)},Rs=d(Ss,0),_s=d(Ss,-1),Ds=d(ks,0),Bs=d(ks,-1),Os=function(n,r,o){return A.from(o.container()).filter(Do.isText).exists(function(e){var t=n?0:-1;return r(e.data.charAt(o.offset()+t))})},Ps=d(Os,!0,is),Ls=d(Os,!1,is),Is=function(e){return A.from(e.getNode()).map(rr.fromDom)},Ms=function(e,t){for(;t=e(t);)if(t.isVisible())return t;return t},Fs=function(e,t){var n=bs(e,t);return!(n||!Do.isBr(e.getNode()))||n};(hu=pu||(pu={}))[hu.Backwards=-1]="Backwards",hu[hu.Forwards=1]="Forwards";var zs,Us,Vs,Hs,js,qs=Do.isContentEditableFalse,$s=Do.isText,Ws=Do.isElement,Ks=Do.isBr,Xs=Ba,Ys=function(e){return Ra(e)||!!Oa(t=e)&&!0!==U(ne(t.getElementsByTagName("*")),function(e,t){return e||Ea(t)},!1);var t},Gs=Pa,Js=function(e,t){return e.hasChildNodes()&&t<e.childNodes.length?e.childNodes[t]:null},Qs=function(e,t){if(ms(e)){if(Xs(t.previousSibling)&&!$s(t.previousSibling))return vu.before(t);if($s(t))return vu(t,0)}if(gs(e)){if(Xs(t.nextSibling)&&!$s(t.nextSibling))return vu.after(t);if($s(t))return vu(t,t.data.length)}return gs(e)?Ks(t)?vu.before(t):vu.after(t):vu.before(t)},Zs=function(e,t,n){var r,o,i,a,u;if(!Ws(n)||!t)return null;if(t.isEqual(vu.after(n))&&n.lastChild){if(u=vu.after(n.lastChild),gs(e)&&Xs(n.lastChild)&&Ws(n.lastChild))return Ks(n.lastChild)?vu.before(n.lastChild):u}else u=t;var s,c,l,f=u.container(),d=u.offset();if($s(f)){if(gs(e)&&0<d)return vu(f,--d);if(ms(e)&&d<f.length)return vu(f,++d);r=f}else{if(gs(e)&&0<d&&(o=Js(f,d-1),Xs(o)))return!Ys(o)&&(i=hs(o,e,Gs,o))?$s(i)?vu(i,i.data.length):vu.after(i):$s(o)?vu(o,o.data.length):vu.before(o);if(ms(e)&&d<f.childNodes.length&&(o=Js(f,d),Xs(o)))return Ks(o)?(s=n,(l=(c=o).nextSibling)&&Xs(l)?$s(l)?vu(l,0):vu.before(l):Zs(pu.Forwards,vu.after(c),s)):!Ys(o)&&(i=hs(o,e,Gs,o))?$s(i)?vu(i,0):vu.before(i):$s(o)?vu(o,0):vu.after(o);r=o||u.getNode()}return(ms(e)&&u.isAtEnd()||gs(e)&&u.isAtStart())&&(r=hs(r,e,j(!0),n,!0),Gs(r,n))?Qs(e,r):(o=hs(r,e,Gs,n),!(a=qt.last(z(function(e,t){for(var n=[];e&&e!==t;)n.push(e),e=e.parentNode;return n}(f,n),qs)))||o&&a.contains(o)?o?Qs(e,o):null:u=ms(e)?vu.after(a):vu.before(a))},ec=function(t){return{next:function(e){return Zs(pu.Forwards,e,t)},prev:function(e){return Zs(pu.Backwards,e,t)}}},tc=function(e){return vu.isTextPosition(e)?0===e.offset():Ba(e.getNode())},nc=function(e){if(vu.isTextPosition(e)){var t=e.container();return e.offset()===t.data.length}return Ba(e.getNode(!0))},rc=function(e,t){return!vu.isTextPosition(e)&&!vu.isTextPosition(t)&&e.getNode()===t.getNode(!0)},oc=function(e,t,n){return e?!rc(t,n)&&(r=t,!(!vu.isTextPosition(r)&&Do.isBr(r.getNode())))&&nc(t)&&tc(n):!rc(n,t)&&tc(t)&&nc(n);var r},ic=function(e,t,n){var r=ec(t);return A.from(e?r.next(n):r.prev(n))},ac=function(e,t){var n,r,o,i,a,u=e?t.firstChild:t.lastChild;return Do.isText(u)?A.some(vu(u,e?0:u.data.length)):u?Ba(u)?A.some(e?vu.before(u):(a=u,Do.isBr(a)?vu.before(a):vu.after(a))):(r=t,o=u,i=(n=e)?vu.before(o):vu.after(o),ic(n,r,i)):A.none()},uc=d(ic,!0),sc=d(ic,!1),cc={fromPosition:ic,nextPosition:uc,prevPosition:sc,navigate:function(t,n,r){return ic(t,n,r).bind(function(e){return bs(r,e,n)&&oc(t,r,e)?ic(t,n,e):A.some(e)})},positionIn:ac,firstPositionIn:d(ac,!0),lastPositionIn:d(ac,!1)},lc=function(e,t){return!e.isBlock(t)||t.innerHTML||de.ie||(t.innerHTML='<br data-mce-bogus="1" />'),t},fc=function(e,t){return cc.lastPositionIn(e).fold(function(){return!1},function(e){return t.setStart(e.container(),e.offset()),t.setEnd(e.container(),e.offset()),!0})},dc=function(e,t,n){return!(!1!==t.hasChildNodes()||!Vu(e,t)||(o=n,i=(r=t).ownerDocument.createTextNode(la),r.appendChild(i),o.setStart(i,0),o.setEnd(i,0),0));var r,o,i},mc=function(e,t,n,r){var o,i,a,u,s=n[t?"start":"end"],c=e.getRoot();if(s){for(a=s[0],i=c,o=s.length-1;1<=o;o--){if(u=i.childNodes,dc(c,i,r))return!0;if(s[o]>u.length-1)return!!dc(c,i,r)||fc(i,r);i=u[s[o]]}3===i.nodeType&&(a=Math.min(s[0],i.nodeValue.length)),1===i.nodeType&&(a=Math.min(s[0],i.childNodes.length)),t?r.setStart(i,a):r.setEnd(i,a)}return!0},gc=function(e){return Do.isText(e)&&0<e.data.length},pc=function(e,t,n){var r,o,i,a,u,s,c=e.get(n.id+"_"+t),l=n.keep;if(c){if(r=c.parentNode,"start"===t?l?c.hasChildNodes()?(r=c.firstChild,o=1):gc(c.nextSibling)?(r=c.nextSibling,o=0):gc(c.previousSibling)?(r=c.previousSibling,o=c.previousSibling.data.length):(r=c.parentNode,o=e.nodeIndex(c)+1):o=e.nodeIndex(c):l?c.hasChildNodes()?(r=c.firstChild,o=1):gc(c.previousSibling)?(r=c.previousSibling,o=c.previousSibling.data.length):(r=c.parentNode,o=e.nodeIndex(c)):o=e.nodeIndex(c),u=r,s=o,!l){for(a=c.previousSibling,i=c.nextSibling,Yt.each(Yt.grep(c.childNodes),function(e){Do.isText(e)&&(e.nodeValue=e.nodeValue.replace(/\uFEFF/g,""))});c=e.get(n.id+"_"+t);)e.remove(c,!0);a&&i&&a.nodeType===i.nodeType&&Do.isText(a)&&!de.opera&&(o=a.nodeValue.length,a.appendData(i.nodeValue),e.remove(i),u=a,s=o)}return A.some(vu(u,s))}return A.none()},hc=function(e,t){var n,r,o,i,a,u,s,c,l,f,d,m,g,p,h,v,b=e.dom;if(t){if(v=t,Yt.isArray(v.start))return p=t,h=(g=b).createRng(),mc(g,!0,p,h)&&mc(g,!1,p,h)?A.some(h):A.none();if("string"==typeof t.start)return A.some((f=t,d=(l=b).createRng(),m=ku(l.getRoot(),f.start),d.setStart(m.container(),m.offset()),m=ku(l.getRoot(),f.end),d.setEnd(m.container(),m.offset()),d));if(t.hasOwnProperty("id"))return s=pc(o=b,"start",i=t),c=pc(o,"end",i),$a([s,(a=c,u=s,a.isSome()?a:u)],function(e,t){var n=o.createRng();return n.setStart(lc(o,e.container()),e.offset()),n.setEnd(lc(o,t.container()),t.offset()),n});if(t.hasOwnProperty("name"))return n=b,r=t,A.from(n.select(r.name)[r.index]).map(function(e){var t=n.createRng();return t.selectNode(e),t});if(t.hasOwnProperty("rng"))return A.some(t.rng)}return A.none()},vc=function(e,t,n){return Fu.getBookmark(e,t,n)},bc=function(t,e){hc(t,e).each(function(e){t.setRng(e)})},yc=function(e){return Do.isElement(e)&&"SPAN"===e.tagName&&"bookmark"===e.getAttribute("data-mce-type")},Cc=function(e){return e&&/^(IMG)$/.test(e.nodeName)},xc=function(e){return e&&3===e.nodeType&&/^([\t \r\n]+|)$/.test(e.nodeValue)},wc=function(e,t,n){return"color"!==n&&"backgroundColor"!==n||(t=e.toHex(t)),"fontWeight"===n&&700===t&&(t="bold"),"fontFamily"===n&&(t=t.replace(/[\'\"]/g,"").replace(/,\s+/g,",")),""+t},Nc={isInlineBlock:Cc,moveStart:function(e,t,n){var r,o,i,a=n.startOffset,u=n.startContainer;if((n.startContainer!==n.endContainer||!Cc(n.startContainer.childNodes[n.startOffset]))&&1===u.nodeType)for(a<(i=u.childNodes).length?r=new ro(u=i[a],e.getParent(u,e.isBlock)):(r=new ro(u=i[i.length-1],e.getParent(u,e.isBlock))).next(!0),o=r.current();o;o=r.next())if(3===o.nodeType&&!xc(o))return n.setStart(o,0),void t.setRng(n)},getNonWhiteSpaceSibling:function(e,t,n){if(e)for(t=t?"nextSibling":"previousSibling",e=n?e:e[t];e;e=e[t])if(1===e.nodeType||!xc(e))return e},isTextBlock:function(e,t){return t.nodeType&&(t=t.nodeName),!!e.schema.getTextBlockElements()[t.toLowerCase()]},isValid:function(e,t,n){return e.schema.isValidChild(t,n)},isWhiteSpaceNode:xc,replaceVars:function(e,n){return"string"!=typeof e?e=e(n):n&&(e=e.replace(/%(\w+)/g,function(e,t){return n[t]||e})),e},isEq:function(e,t){return t=t||"",e=""+((e=e||"").nodeName||e),t=""+(t.nodeName||t),e.toLowerCase()===t.toLowerCase()},normalizeStyleValue:wc,getStyle:function(e,t,n){return wc(e,e.getStyle(t,n),n)},getTextDecoration:function(t,e){var n;return t.getParent(e,function(e){return(n=t.getStyle(e,"text-decoration"))&&"none"!==n}),n},getParents:function(e,t,n){return e.getParents(t,n,e.getRoot())}},Ec=yc,Sc=Nc.getParents,kc=Nc.isWhiteSpaceNode,Tc=Nc.isTextBlock,Ac=function(e,t){for(void 0===t&&(t=3===e.nodeType?e.length:e.childNodes.length);e&&e.hasChildNodes();)(e=e.childNodes[t])&&(t=3===e.nodeType?e.length:e.childNodes.length);return{node:e,offset:t}},Rc=function(e,t){for(var n=t;n;){if(1===n.nodeType&&e.getContentEditable(n))return"false"===e.getContentEditable(n)?n:t;n=n.parentNode}return t},_c=function(e,t,n,r){var o,i,a=n.nodeValue;return void 0===r&&(r=e?a.length:0),e?(o=a.lastIndexOf(" ",r),-1!==(o=(i=a.lastIndexOf("\xa0",r))<o?o:i)&&!t&&(o<r||!e)&&o<=a.length&&o++):(o=a.indexOf(" ",r),i=a.indexOf("\xa0",r),o=-1!==o&&(-1===i||o<i)?o:i),o},Dc=function(e,t,n,r,o,i){var a,u,s,c;if(3===n.nodeType){if(-1!==(s=_c(o,i,n,r)))return{container:n,offset:s};c=n}for(a=new ro(n,e.getParent(n,e.isBlock)||t);u=a[o?"prev":"next"]();)if(3!==u.nodeType||Ec(u.parentNode)){if(e.isBlock(u)||Nc.isEq(u,"BR"))break}else if(-1!==(s=_c(o,i,c=u)))return{container:u,offset:s};if(c)return{container:c,offset:r=o?0:c.length}},Bc=function(e,t,n,r,o){var i,a,u,s;for(3===r.nodeType&&0===r.nodeValue.length&&r[o]&&(r=r[o]),i=Sc(e,r),a=0;a<i.length;a++)for(u=0;u<t.length;u++)if(!("collapsed"in(s=t[u])&&s.collapsed!==n.collapsed)&&e.is(i[a],s.selector))return i[a];return r},Oc=function(t,e,n,r){var o,i=t.dom,a=i.getRoot();if(e[0].wrapper||(o=i.getParent(n,e[0].block,a)),!o){var u=i.getParent(n,"LI,TD,TH");o=i.getParent(3===n.nodeType?n.parentNode:n,function(e){return e!==a&&Tc(t,e)},u)}if(o&&e[0].wrapper&&(o=Sc(i,o,"ul,ol").reverse()[0]||o),!o)for(o=n;o[r]&&!i.isBlock(o[r])&&(o=o[r],!Nc.isEq(o,"br")););return o||n},Pc=function(e,t,n,r,o,i,a){var u,s,c,l,f,d;if(u=s=a?n:o,l=a?"previousSibling":"nextSibling",f=e.getRoot(),3===u.nodeType&&!kc(u)&&(a?0<r:i<u.nodeValue.length))return u;for(;;){if(!t[0].block_expand&&e.isBlock(s))return s;for(c=s[l];c;c=c[l])if(!Ec(c)&&!kc(c)&&("BR"!==(d=c).nodeName||!d.getAttribute("data-mce-bogus")||d.nextSibling))return s;if(s===f||s.parentNode===f){u=s;break}s=s.parentNode}return u},Lc=function(e,t,n,r){var o,i=t.startContainer,a=t.startOffset,u=t.endContainer,s=t.endOffset,c=e.dom;return 1===i.nodeType&&i.hasChildNodes()&&3===(i=Ha(i,a)).nodeType&&(a=0),1===u.nodeType&&u.hasChildNodes()&&3===(u=Ha(u,t.collapsed?s:s-1)).nodeType&&(s=u.nodeValue.length),i=Rc(c,i),u=Rc(c,u),(Ec(i.parentNode)||Ec(i))&&(i=Ec(i)?i:i.parentNode,3===(i=t.collapsed?i.previousSibling||i:i.nextSibling||i).nodeType&&(a=t.collapsed?i.length:0)),(Ec(u.parentNode)||Ec(u))&&(u=Ec(u)?u:u.parentNode,3===(u=t.collapsed?u.nextSibling||u:u.previousSibling||u).nodeType&&(s=t.collapsed?0:u.length)),t.collapsed&&((o=Dc(c,e.getBody(),i,a,!0,r))&&(i=o.container,a=o.offset),(o=Dc(c,e.getBody(),u,s,!1,r))&&(u=o.container,s=o.offset)),n[0].inline&&(u=r?u:function(e,t){var n=Ac(e,t);if(n.node){for(;n.node&&0===n.offset&&n.node.previousSibling;)n=Ac(n.node.previousSibling);n.node&&0<n.offset&&3===n.node.nodeType&&" "===n.node.nodeValue.charAt(n.offset-1)&&1<n.offset&&(e=n.node).splitText(n.offset-1)}return e}(u,s)),(n[0].inline||n[0].block_expand)&&(n[0].inline&&3===i.nodeType&&0!==a||(i=Pc(c,n,i,a,u,s,!0)),n[0].inline&&3===u.nodeType&&s!==u.nodeValue.length||(u=Pc(c,n,i,a,u,s,!1))),n[0].selector&&!1!==n[0].expand&&!n[0].inline&&(i=Bc(c,n,t,i,"previousSibling"),u=Bc(c,n,t,u,"nextSibling")),(n[0].block||n[0].selector)&&(i=Oc(e,n,i,"previousSibling"),u=Oc(e,n,u,"nextSibling"),n[0].block&&(c.isBlock(i)||(i=Pc(c,n,i,a,u,s,!0)),c.isBlock(u)||(u=Pc(c,n,i,a,u,s,!1)))),1===i.nodeType&&(a=c.nodeIndex(i),i=i.parentNode),1===u.nodeType&&(s=c.nodeIndex(u)+1,u=u.parentNode),{startContainer:i,startOffset:a,endContainer:u,endOffset:s}},Ic=Yt.each,Mc=function(e,t,o){var n,r,i,a,u,s,c,l=t.startContainer,f=t.startOffset,d=t.endContainer,m=t.endOffset;if(0<(c=e.select("td[data-mce-selected],th[data-mce-selected]")).length)Ic(c,function(e){o([e])});else{var g,p,h,v=function(e){var t;return 3===(t=e[0]).nodeType&&t===l&&f>=t.nodeValue.length&&e.splice(0,1),t=e[e.length-1],0===m&&0<e.length&&t===d&&3===t.nodeType&&e.splice(e.length-1,1),e},b=function(e,t,n){for(var r=[];e&&e!==n;e=e[t])r.push(e);return r},y=function(e,t){do{if(e.parentNode===t)return e;e=e.parentNode}while(e)},C=function(e,t,n){var r=n?"nextSibling":"previousSibling";for(u=(a=e).parentNode;a&&a!==t;a=u)u=a.parentNode,(s=b(a===e?a:a[r],r)).length&&(n||s.reverse(),o(v(s)))};if(1===l.nodeType&&l.hasChildNodes()&&(l=l.childNodes[f]),1===d.nodeType&&d.hasChildNodes()&&(p=m,h=(g=d).childNodes,--p>h.length-1?p=h.length-1:p<0&&(p=0),d=h[p]||g),l===d)return o(v([l]));for(n=e.findCommonAncestor(l,d),a=l;a;a=a.parentNode){if(a===d)return C(l,n,!0);if(a===n)break}for(a=d;a;a=a.parentNode){if(a===l)return C(d,n);if(a===n)break}r=y(l,n)||l,i=y(d,n)||d,C(l,r,!0),(s=b(r===l?r:r.nextSibling,"nextSibling",i===d?i.nextSibling:i)).length&&o(v(s)),C(d,i)}},Fc=(zs=lr,Us="text",Vs=function(e){return zs(e)?A.from(e.dom().nodeValue):A.none()},Hs=tr.detect().browser,{get:function(e){if(!zs(e))throw new Error("Can only get "+Us+" value of a "+Us+" node");return js(e).getOr("")},getOption:js=Hs.isIE()&&10===Hs.version.major?function(e){try{return Vs(e)}catch(VN){return A.none()}}:Vs,set:function(e,t){if(!zs(e))throw new Error("Can only set raw "+Us+" value of a "+Us+" node");e.dom().nodeValue=t}}),zc=function(e){return Fc.get(e)},Uc=function(r,o,i,a){return Ir(o).fold(function(){return"skipping"},function(e){return"br"===a||lr(n=o)&&"\ufeff"===zc(n)?"valid":cr(t=o)&&Ui(t,Gi())?"existing":Uu(o)?"caret":Nc.isValid(r,i,a)&&Nc.isValid(r,ur(e),i)?"valid":"invalid-child";var t,n})},Vc=function(e,t,n,r){var o,i,a=t.uid,u=void 0===a?(o="mce-annotation",i=(new Date).getTime(),o+"_"+Math.floor(1e9*Math.random())+ ++ra+String(i)):a,s=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&(n[r[o]]=e[r[o]])}return n}(t,["uid"]),c=rr.fromTag("span",e);Fi(c,Gi()),vr(c,""+Qi(),u),vr(c,""+Ji(),n);var l,f=r(u,s),d=f.attributes,m=void 0===d?{}:d,g=f.classes,p=void 0===g?[]:g;return br(c,m),l=c,F(p,function(e){Fi(l,e)}),c},Hc=function(i,e,t,n,r){var a=[],u=Vc(i.getDoc(),r,t,n),s=Oi(A.none()),c=function(){s.set(A.none())},l=function(e){F(e,o)},o=function(e){var t,n;switch(Uc(i,e,"span",ur(e))){case"invalid-child":c();var r=Vr(e);l(r),c();break;case"valid":var o=s.get().getOrThunk(function(){var e=ia(u);return a.push(e),s.set(A.some(e)),e});Ei(t=e,n=o),Ti(n,t)}};return Mc(i.dom,e,function(e){var t;c(),t=$(e,rr.fromDom),l(t)}),a},jc=function(s,c,l,f){s.undoManager.transact(function(){var e,t,n,r,o=s.selection.getRng();if(o.collapsed&&(r=Lc(e=s,t=o,[{inline:!0}],3===(n=t).startContainer.nodeType&&n.startContainer.nodeValue.length>=n.startOffset&&"\xa0"===n.startContainer.nodeValue[n.startOffset]),t.setStart(r.startContainer,r.startOffset),t.setEnd(r.endContainer,r.endOffset),e.selection.setRng(t)),s.selection.getRng().collapsed){var i=Vc(s.getDoc(),f,c,l.decorate);ua(i,"\xa0"),s.selection.getRng().insertNode(i.dom()),s.selection.select(i.dom())}else{var a=Fu.getPersistentBookmark(s.selection,!1),u=s.selection.getRng();Hc(s,u,c,l.decorate,f),s.selection.moveToBookmark(a)}})};function qc(s){var n,r=(n={},{register:function(e,t){n[e]={name:e,settings:t}},lookup:function(e){return n.hasOwnProperty(e)?A.from(n[e]).map(function(e){return e.settings}):A.none()}});na(s,r);var o=ta(s);return{register:function(e,t){r.register(e,t)},annotate:function(t,n){r.lookup(t).each(function(e){jc(s,t,e,n)})},annotationChanged:function(e,t){o.addListener(e,t)},remove:function(e){Zi(s,A.some(e)).each(function(e){var t=e.elements;F(t,Di)})},getAll:function(e){var t,n,r,o,i,a,u=(t=s,n=e,r=rr.fromDom(t.getBody()),o=Hi(r,"["+Ji()+'="'+n+'"]'),i={},F(o,function(e){var t=yr(e,Qi()),n=i.hasOwnProperty(t)?i[t]:[];i[t]=n.concat([e])}),i);return a=function(e){return $(e,function(e){return e.dom()})},gr(u,function(e,t,n){return{k:t,v:a(e,t,n)}})}}}var $c=function(e){return Yt.grep(e.childNodes,function(e){return"LI"===e.nodeName})},Wc=function(e){return e&&e.firstChild&&e.firstChild===e.lastChild&&("\xa0"===(t=e.firstChild).data||Do.isBr(t));var t},Kc=function(e){return 0<e.length&&(!(t=e[e.length-1]).firstChild||Wc(t))?e.slice(0,-1):e;var t},Xc=function(e,t){var n=e.getParent(t,e.isBlock);return n&&"LI"===n.nodeName?n:null},Yc=function(e,t){var n=vu.after(e),r=ec(t).prev(n);return r?r.toRange():null},Gc=function(t,e,n){var r,o,i,a,u=t.parentNode;return Yt.each(e,function(e){u.insertBefore(e,t)}),r=t,o=n,i=vu.before(r),(a=ec(o).next(i))?a.toRange():null},Jc=function(e,t){var n,r,o,i,a,u,s=t.firstChild,c=t.lastChild;return s&&"meta"===s.name&&(s=s.next),c&&"mce_marker"===c.attr("id")&&(c=c.prev),r=c,u=(n=e).getNonEmptyElements(),r&&(r.isEmpty(u)||(o=r,n.getBlockElements()[o.name]&&(a=o).firstChild&&a.firstChild===a.lastChild&&("br"===(i=o.firstChild).name||"\xa0"===i.value)))&&(c=c.prev),!(!s||s!==c||"ul"!==s.name&&"ol"!==s.name)},Qc=function(e,o,i,t){var n,r,a,u,s,c,l,f,d,m,g,p,h,v,b,y,C,x,w,N=(n=o,r=t,c=e.serialize(r),l=n.createFragment(c),u=(a=l).firstChild,s=a.lastChild,u&&"META"===u.nodeName&&u.parentNode.removeChild(u),s&&"mce_marker"===s.id&&s.parentNode.removeChild(s),a),E=Xc(o,i.startContainer),S=Kc($c(N.firstChild)),k=o.getRoot(),T=function(e){var t=vu.fromRangeStart(i),n=ec(o.getRoot()),r=1===e?n.prev(t):n.next(t);return!r||Xc(o,r.getNode())!==E};return T(1)?Gc(E,S,k):T(2)?(f=E,d=S,m=k,o.insertAfter(d.reverse(),f),Yc(d[0],m)):(p=S,h=k,v=g=E,y=(b=i).cloneRange(),C=b.cloneRange(),y.setStartBefore(v),C.setEndAfter(v),x=[y.cloneContents(),C.cloneContents()],(w=g.parentNode).insertBefore(x[0],g),Yt.each(p,function(e){w.insertBefore(e,g)}),w.insertBefore(x[1],g),w.removeChild(g),Yc(p[p.length-1],h))},Zc=function(e,t){return!!Xc(e,t)},el=Yt.each,tl=function(o){this.compare=function(e,t){if(e.nodeName!==t.nodeName)return!1;var n=function(n){var r={};return el(o.getAttribs(n),function(e){var t=e.nodeName.toLowerCase();0!==t.indexOf("_")&&"style"!==t&&0!==t.indexOf("data-")&&(r[t]=o.getAttrib(n,t))}),r},r=function(e,t){var n,r;for(r in e)if(e.hasOwnProperty(r)){if(void 0===(n=t[r]))return!1;if(e[r]!==n)return!1;delete t[r]}for(r in t)if(t.hasOwnProperty(r))return!1;return!0};return!(!r(n(e),n(t))||!r(o.parseStyle(o.getAttrib(e,"style")),o.parseStyle(o.getAttrib(t,"style")))||yc(e)||yc(t))}},nl=function(e){var t=Hi(e,"br"),n=z(function(e){for(var t=[],n=e.dom();n;)t.push(rr.fromDom(n)),n=n.lastChild;return t}(e).slice(-1),fo);t.length===n.length&&F(n,_i)},rl=function(e){Ri(e),Ti(e,rr.fromHtml('<br data-mce-bogus="1">'))},ol=function(n){qr(n).each(function(t){Mr(t).each(function(e){co(n)&&fo(t)&&co(e)&&_i(t)})})},il=Yt.makeMap;function al(e){var u,s,c,l,f,d=[];return u=(e=e||{}).indent,s=il(e.indent_before||""),c=il(e.indent_after||""),l=$o.getEncodeFunc(e.entity_encoding||"raw",e.entities),f="html"===e.element_format,{start:function(e,t,n){var r,o,i,a;if(u&&s[e]&&0<d.length&&0<(a=d[d.length-1]).length&&"\n"!==a&&d.push("\n"),d.push("<",e),t)for(r=0,o=t.length;r<o;r++)i=t[r],d.push(" ",i.name,'="',l(i.value,!0),'"');d[d.length]=!n||f?">":" />",n&&u&&c[e]&&0<d.length&&0<(a=d[d.length-1]).length&&"\n"!==a&&d.push("\n")},end:function(e){var t;d.push("</",e,">"),u&&c[e]&&0<d.length&&0<(t=d[d.length-1]).length&&"\n"!==t&&d.push("\n")},text:function(e,t){0<e.length&&(d[d.length]=t?e:l(e))},cdata:function(e){d.push("<![CDATA[",e,"]]>")},comment:function(e){d.push("\x3c!--",e,"--\x3e")},pi:function(e,t){t?d.push("<?",e," ",l(t),"?>"):d.push("<?",e,"?>"),u&&d.push("\n")},doctype:function(e){d.push("<!DOCTYPE",e,">",u?"\n":"")},reset:function(){d.length=0},getContent:function(){return d.join("").replace(/\n$/,"")}}}function ul(t,g){void 0===g&&(g=ni());var p=al(t);return(t=t||{}).validate=!("validate"in t)||t.validate,{serialize:function(e){var f,d;d=t.validate,f={3:function(e){p.text(e.value,e.raw)},8:function(e){p.comment(e.value)},7:function(e){p.pi(e.name,e.value)},10:function(e){p.doctype(e.value)},4:function(e){p.cdata(e.value)},11:function(e){if(e=e.firstChild)for(;m(e),e=e.next;);}},p.reset();var m=function(e){var t,n,r,o,i,a,u,s,c,l=f[e.type];if(l)l(e);else{if(t=e.name,n=e.shortEnded,r=e.attributes,d&&r&&1<r.length&&((a=[]).map={},c=g.getElementRule(e.name))){for(u=0,s=c.attributesOrder.length;u<s;u++)(o=c.attributesOrder[u])in r.map&&(i=r.map[o],a.map[o]=i,a.push({name:o,value:i}));for(u=0,s=r.length;u<s;u++)(o=r[u].name)in a.map||(i=r.map[o],a.map[o]=i,a.push({name:o,value:i}));r=a}if(p.start(e.name,r,n),!n){if(e=e.firstChild)for(;m(e),e=e.next;);p.end(t)}}};return 1!==e.type||t.inner?f[11](e):m(e),p.getContent()}}}var sl=function(a){var u=vu.fromRangeStart(a),s=vu.fromRangeEnd(a),c=a.commonAncestorContainer;return cc.fromPosition(!1,c,s).map(function(e){return!bs(u,s,c)&&bs(u,e,c)?(t=u.container(),n=u.offset(),r=e.container(),o=e.offset(),(i=document.createRange()).setStart(t,n),i.setEnd(r,o),i):a;var t,n,r,o,i}).getOr(a)},cl=function(e){return e.collapsed?e:sl(e)},ll=Do.matchNodeNames("td th"),fl=function(e,t){var n,r,o=e.selection.getRng(),i=o.startContainer,a=o.startOffset;o.collapsed&&(n=i,r=a,Do.isText(n)&&"\xa0"===n.nodeValue[r-1])&&Do.isText(i)&&(i.insertData(a-1," "),i.deleteData(a,1),o.setStart(i,a),o.setEnd(i,a),e.selection.setRng(o)),e.selection.setContent(t)},dl=function(e,t,n){var r,o,i,a,u,s,c,l,f,d,m,g=e.selection,p=e.dom;if(/^ | $/.test(t)&&(t=function(e,t){var n,r;n=e.startContainer,r=e.startOffset;var o=function(e){return n[e]&&3===n[e].nodeType};return 3===n.nodeType&&(0<r?t=t.replace(/^&nbsp;/," "):o("previousSibling")||(t=t.replace(/^ /,"&nbsp;")),r<n.length?t=t.replace(/&nbsp;(<br>|)$/," "):o("nextSibling")||(t=t.replace(/(&nbsp;| )(<br>|)$/,"&nbsp;"))),t}(g.getRng(),t)),r=e.parser,m=n.merge,o=ul({validate:e.settings.validate},e.schema),d='<span id="mce_marker" data-mce-type="bookmark">&#xFEFF;&#x200B;</span>',s={content:t,format:"html",selection:!0,paste:n.paste},(s=e.fire("BeforeSetContent",s)).isDefaultPrevented())e.fire("SetContent",{content:s.content,format:"html",selection:!0,paste:n.paste});else{-1===(t=s.content).indexOf("{$caret}")&&(t+="{$caret}"),t=t.replace(/\{\$caret\}/,d);var h,v,b,y,C,x,w=(l=g.getRng()).startContainer||(l.parentElement?l.parentElement():null),N=e.getBody();w===N&&g.isCollapsed()&&p.isBlock(N.firstChild)&&(h=e,(v=N.firstChild)&&!h.schema.getShortEndedElements()[v.nodeName])&&p.isEmpty(N.firstChild)&&((l=p.createRng()).setStart(N.firstChild,0),l.setEnd(N.firstChild,0),g.setRng(l)),g.isCollapsed()||(e.selection.setRng(cl(e.selection.getRng())),e.getDoc().execCommand("Delete",!1,null),b=e.selection.getRng(),y=t,C=b.startContainer,x=b.startOffset,3===C.nodeType&&b.collapsed&&("\xa0"===C.data[x]?(C.deleteData(x,1),/[\u00a0| ]$/.test(y)||(y+=" ")):"\xa0"===C.data[x-1]&&(C.deleteData(x-1,1),/[\u00a0| ]$/.test(y)||(y=" "+y))),t=y);var E,S,k,T={context:(i=g.getNode()).nodeName.toLowerCase(),data:n.data,insert:!0};if(u=r.parse(t,T),!0===n.paste&&Jc(e.schema,u)&&Zc(p,i))return l=Qc(o,p,e.selection.getRng(),u),e.selection.setRng(l),void e.fire("SetContent",s);if(function(e){for(var t=e;t=t.walk();)1===t.type&&t.attr("data-mce-fragment","1")}(u),"mce_marker"===(f=u.lastChild).attr("id"))for(f=(c=f).prev;f;f=f.walk(!0))if(3===f.type||!p.isBlock(f.name)){e.schema.isValidChild(f.parent.name,"span")&&f.parent.insert(c,f,"br"===f.name);break}if(e._selectionOverrides.showBlockCaretContainer(i),T.invalid){for(fl(e,d),i=g.getNode(),a=e.getBody(),9===i.nodeType?i=f=a:f=i;f!==a;)f=(i=f).parentNode;t=i===a?a.innerHTML:p.getOuterHTML(i),t=o.serialize(r.parse(t.replace(/<span (id="mce_marker"|id=mce_marker).+?<\/span>/i,function(){return o.serialize(u)}))),i===a?p.setHTML(a,t):p.setOuterHTML(i,t)}else!function(e,t,n){if("all"===n.getAttribute("data-mce-bogus"))n.parentNode.insertBefore(e.dom.createFragment(t),n);else{var r=n.firstChild,o=n.lastChild;!r||r===o&&"BR"===r.nodeName?e.dom.setHTML(n,t):fl(e,t)}}(e,t=o.serialize(u),i);!function(e,t){var n=e.schema.getTextInlineElements(),r=e.dom;if(t){var o=e.getBody(),i=new tl(r);Yt.each(r.select("*[data-mce-fragment]"),function(e){for(var t=e.parentNode;t&&t!==o;t=t.parentNode)n[e.nodeName.toLowerCase()]&&i.compare(t,e)&&r.remove(e,!0)})}}(e,m),function(n,e){var t,r,o,i,a,u=n.dom,s=n.selection;if(e){if(n.selection.scrollIntoView(e),t=function(e){for(var t=n.getBody();e&&e!==t;e=e.parentNode)if("false"===n.dom.getContentEditable(e))return e;return null}(e))return u.remove(e),s.select(t);var c=u.createRng();(i=e.previousSibling)&&3===i.nodeType?(c.setStart(i,i.nodeValue.length),de.ie||(a=e.nextSibling)&&3===a.nodeType&&(i.appendData(a.data),a.parentNode.removeChild(a))):(c.setStartBefore(e),c.setEndBefore(e)),r=u.getParent(e,u.isBlock),u.remove(e),r&&u.isEmpty(r)&&(n.$(r).empty(),c.setStart(r,0),c.setEnd(r,0),ll(r)||r.getAttribute("data-mce-fragment")||!(o=function(e){var t=vu.fromRangeStart(e);if(t=ec(n.getBody()).next(t))return t.toRange()}(c))?u.add(r,u.create("br",{"data-mce-bogus":"1"})):(c=o,u.remove(r))),s.setRng(c)}}(e,p.get("mce_marker")),E=e.getBody(),Yt.each(E.getElementsByTagName("*"),function(e){e.removeAttribute("data-mce-fragment")}),S=e.dom,k=e.selection.getStart(),A.from(S.getParent(k,"td,th")).map(rr.fromDom).each(ol),e.fire("SetContent",s),e.addVisual()}},ml=function(e,t){var n,r,o="string"!=typeof(n=t)?(r=Yt.extend({paste:n.paste,data:{paste:n.paste}},n),{content:n.content,details:r}):{content:n,details:{}};dl(e,o.content,o.details)},gl=Er("sections","settings"),pl=tr.detect().deviceType.isTouch(),hl=["lists","autolink","autosave"],vl={theme:"mobile"},bl=function(e){var t=D(e)?e.join(" "):e,n=$(R(t)?t.split(" "):[],Kn);return z(n,function(e){return 0<e.length})},yl=function(n,e){var r,o,i,t=(r=function(e,t){return M(n,t)},o={},i={},mr(e,function(e,t){(r(e,t)?o:i)[t]=e}),{t:o,f:i});return gl(t.t,t.f)},Cl=function(e,t){return e.sections().hasOwnProperty(t)},xl=function(e,t,n,r){var o,i=bl(n.forced_plugins),a=bl(r.plugins),u=e&&Cl(t,"mobile")?z(a,d(M,hl)):a,s=(o=u,[].concat(bl(i)).concat(bl(o)));return Yt.extend(r,{plugins:s.join(" ")})},wl=function(e,t,n,r){var o,i,a,u,s,c,l,f,d,m,g=yl(["mobile"],r),p=Yt.extend(t,n,g.settings(),(f=e,m=(d=g).settings().inline,f&&Cl(d,"mobile")&&!m?(u="mobile",s=vl,c=g.sections(),l=c.hasOwnProperty(u)?c[u]:{},Yt.extend({},s,l)):{}),{validate:!0,content_editable:g.settings().inline,external_plugins:(o=n,i=g.settings(),a=i.external_plugins?i.external_plugins:{},o&&o.external_plugins?Yt.extend({},o.external_plugins,a):a)});return xl(e,g,n,p)},Nl=function(e,t,n){return A.from(t.settings[n]).filter(e)},El=d(Nl,R),Sl=function(e,t,n,r){var o,i,a,u=t in e.settings?e.settings[t]:n;return"hash"===r?(a={},"string"==typeof(i=u)?F(0<i.indexOf("=")?i.split(/[;,](?![^=;,]*(?:[;,]|$))/):i.split(","),function(e){var t=e.split("=");1<t.length?a[Yt.trim(t[0])]=Yt.trim(t[1]):a[Yt.trim(t[0])]=Yt.trim(t)}):a=i,a):"string"===r?Nl(R,e,t).getOr(n):"number"===r?Nl(L,e,t).getOr(n):"boolean"===r?Nl(O,e,t).getOr(n):"object"===r?Nl(_,e,t).getOr(n):"array"===r?Nl(D,e,t).getOr(n):"string[]"===r?Nl((o=R,function(e){return D(e)&&J(e,o)}),e,t).getOr(n):"function"===r?Nl(P,e,t).getOr(n):u},kl=/[\u0591-\u07FF\uFB1D-\uFDFF\uFE70-\uFEFC]/,Tl=function(e,t){if(!t)return t;var n=t.container(),r=t.offset();return e?pa(n)?Do.isText(n.nextSibling)?vu(n.nextSibling,0):vu.after(n):ba(t)?vu(n,r+1):t:pa(n)?Do.isText(n.previousSibling)?vu(n.previousSibling,n.previousSibling.data.length):vu.before(n):ya(t)?vu(n,r-1):t},Al={isInlineTarget:function(e,t){var n=El(e,"inline_boundaries_selector").getOr("a[href],code");return Dr(rr.fromDom(t),n)},findRootInline:function(e,t,n){var r,o,i,a=(r=e,o=t,i=n,z(pi.DOM.getParents(i.container(),"*",o),r));return A.from(a[a.length-1])},isRtl:function(e){return"rtl"===pi.DOM.getStyle(e,"direction",!0)||(t=e.textContent,kl.test(t));var t},isAtZwsp:function(e){return ba(e)||ya(e)},normalizePosition:Tl,normalizeForwards:d(Tl,!0),normalizeBackwards:d(Tl,!1),hasSameParentBlock:function(e,t,n){var r=vs(t,e),o=vs(n,e);return r&&r===o}},Rl=function(e,t){return Pr(e,t)?Wi(t,function(e){return mo(e)||po(e)},(n=e,function(e){return Or(n,rr.fromDom(e.dom().parentNode))})):A.none();var n},_l=function(e){var t,n,r;e.dom.isEmpty(e.getBody())&&(e.setContent(""),n=(t=e).getBody(),r=n.firstChild&&t.dom.isBlock(n.firstChild)?n.firstChild:n,t.selection.setCursorLocation(r,0))},Dl=function(i,a,u){return $a([cc.firstPositionIn(u),cc.lastPositionIn(u)],function(e,t){var n=Al.normalizePosition(!0,e),r=Al.normalizePosition(!1,t),o=Al.normalizePosition(!1,a);return i?cc.nextPosition(u,o).map(function(e){return e.isEqual(r)&&a.isEqual(n)}).getOr(!1):cc.prevPosition(u,o).map(function(e){return e.isEqual(n)&&a.isEqual(r)}).getOr(!1)}).getOr(!0)},Bl=function(e,t){var n,r,o,i=rr.fromDom(e),a=rr.fromDom(t);return n=a,r="pre,code",o=d(Or,i),Ki(n,r,o).isSome()},Ol=function(e,t){return Ba(t)&&!1===(r=e,o=t,Do.isText(o)&&/^[ \t\r\n]*$/.test(o.data)&&!1===Bl(r,o))||(n=t,Do.isElement(n)&&"A"===n.nodeName&&n.hasAttribute("name"))||Pl(t);var n,r,o},Pl=Do.hasAttribute("data-mce-bookmark"),Ll=Do.hasAttribute("data-mce-bogus"),Il=Do.hasAttributeValue("data-mce-bogus","all"),Ml=function(e){return function(e){var t,n,r=0;if(Ol(e,e))return!1;if(!(n=e.firstChild))return!0;t=new ro(n,e);do{if(Il(n))n=t.next(!0);else if(Ll(n))n=t.next();else if(Do.isBr(n))r++,n=t.next();else{if(Ol(e,n))return!1;n=t.next()}}while(n);return r<=1}(e.dom())},Fl=Er("block","position"),zl=Er("from","to"),Ul=function(e,t){var n=rr.fromDom(e),r=rr.fromDom(t.container());return Rl(n,r).map(function(e){return Fl(e,t)})},Vl=function(o,i,e){var t=Ul(o,vu.fromRangeStart(e)),n=t.bind(function(e){return cc.fromPosition(i,o,e.position()).bind(function(e){return Ul(o,e).map(function(e){return t=o,n=i,r=e,Do.isBr(r.position().getNode())&&!1===Ml(r.block())?cc.positionIn(!1,r.block().dom()).bind(function(e){return e.isEqual(r.position())?cc.fromPosition(n,t,e).bind(function(e){return Ul(t,e)}):A.some(r)}).getOr(r):r;var t,n,r})})});return $a([t,n],zl).filter(function(e){return!1===Or((r=e).from().block(),r.to().block())&&Ir((n=e).from().block()).bind(function(t){return Ir(n.to().block()).filter(function(e){return Or(t,e)})}).isSome()&&(t=e,!1===Do.isContentEditableFalse(t.from().block())&&!1===Do.isContentEditableFalse(t.to().block()));var t,n,r})},Hl=function(e,t,n){return n.collapsed?Vl(e,t,n):A.none()},jl=function(e,t,n){return Pr(t,e)?function(e,t){for(var n=P(t)?t:j(!1),r=e.dom(),o=[];null!==r.parentNode&&r.parentNode!==undefined;){var i=r.parentNode,a=rr.fromDom(i);if(o.push(a),!0===n(a))break;r=i}return o}(e,function(e){return n(e)||Or(e,t)}).slice(0,-1):[]},ql=function(e,t){return jl(e,t,j(!1))},$l=ql,Wl=function(e,t){return[e].concat(ql(e,t))},Kl=function(e){var t,n=(t=Vr(e),K(t,co).fold(function(){return t},function(e){return t.slice(0,e)}));return F(n,_i),n},Xl=function(e,t){var n=Wl(t,e);return V(n.reverse(),Ml).each(_i)},Yl=function(e,t,n,r){if(Ml(n))return rl(n),cc.firstPositionIn(n.dom());0===z(zr(r),function(e){return!Ml(e)}).length&&Ml(t)&&Ei(r,rr.fromTag("br"));var o=cc.prevPosition(n.dom(),vu.before(r.dom()));return F(Kl(t),function(e){Ei(r,e)}),Xl(e,t),o},Gl=function(e,t,n){if(Ml(n))return _i(n),Ml(t)&&rl(t),cc.firstPositionIn(t.dom());var r=cc.lastPositionIn(n.dom());return F(Kl(t),function(e){Ti(n,e)}),Xl(e,t),r},Jl=function(e,t){return Pr(t,e)?(n=Wl(e,t),A.from(n[n.length-1])):A.none();var n},Ql=function(e,t){cc.positionIn(e,t.dom()).map(function(e){return e.getNode()}).map(rr.fromDom).filter(fo).each(_i)},Zl=function(e,t,n){return Ql(!0,t),Ql(!1,n),Jl(t,n).fold(d(Gl,e,t,n),d(Yl,e,t,n))},ef=function(e,t,n,r){return t?Zl(e,r,n):Zl(e,n,r)},tf=function(t,n){var e,r=rr.fromDom(t.getBody());return(e=Hl(r.dom(),n,t.selection.getRng()).bind(function(e){return ef(r,n,e.from().block(),e.to().block())})).each(function(e){t.selection.setRng(e.toRange())}),e.isSome()},nf=function(e,t){var n=rr.fromDom(t),r=d(Or,e);return $i(n,bo,r).isSome()},rf=function(e,t){var n,r,o=cc.prevPosition(e.dom(),vu.fromRangeStart(t)).isNone(),i=cc.nextPosition(e.dom(),vu.fromRangeEnd(t)).isNone();return!(nf(n=e,(r=t).startContainer)||nf(n,r.endContainer))&&o&&i},of=function(e){var n,r,o,t,i=rr.fromDom(e.getBody()),a=e.selection.getRng();return rf(i,a)?((t=e).setContent(""),t.selection.setCursorLocation(),!0):(n=i,r=e.selection,o=r.getRng(),$a([Rl(n,rr.fromDom(o.startContainer)),Rl(n,rr.fromDom(o.endContainer))],function(e,t){return!1===Or(e,t)&&(o.deleteContents(),ef(n,!0,e,t).each(function(e){r.setRng(e.toRange())}),!0)}).getOr(!1))},af=function(e,t){return!e.selection.isCollapsed()&&of(e)},uf=function(a){if(!D(a))throw new Error("cases must be an array");if(0===a.length)throw new Error("there must be at least one case");var u=[],n={};return F(a,function(e,r){var t=fr(e);if(1!==t.length)throw new Error("one and only one name per case");var o=t[0],i=e[o];if(n[o]!==undefined)throw new Error("duplicate key detected:"+o);if("cata"===o)throw new Error("cannot have a case named cata (sorry)");if(!D(i))throw new Error("case arguments must be an array");u.push(o),n[o]=function(){var e=arguments.length;if(e!==i.length)throw new Error("Wrong number of arguments to case "+o+". Expected "+i.length+" ("+i+"), got "+e);for(var n=new Array(e),t=0;t<n.length;t++)n[t]=arguments[t];return{fold:function(){if(arguments.length!==a.length)throw new Error("Wrong number of arguments to fold. Expected "+a.length+", got "+arguments.length);return arguments[r].apply(null,n)},match:function(e){var t=fr(e);if(u.length!==t.length)throw new Error("Wrong number of arguments to match. Expected: "+u.join(",")+"\nActual: "+t.join(","));if(!J(u,function(e){return M(t,e)}))throw new Error("Not all branches were specified when using match. Specified: "+t.join(", ")+"\nRequired: "+u.join(", "));return e[o].apply(null,n)},log:function(e){console.log(e,{constructors:u,constructor:o,params:n})}}}}),n},sf=function(e){return Is(e).exists(fo)},cf=function(e,t,n){var r=z(Wl(rr.fromDom(n.container()),t),co),o=ee(r).getOr(t);return cc.fromPosition(e,o.dom(),n).filter(sf)},lf=function(e,t){return Is(t).exists(fo)||cf(!0,e,t).isSome()},ff=function(e,t){return(n=t,A.from(n.getNode(!0)).map(rr.fromDom)).exists(fo)||cf(!1,e,t).isSome();var n},df=d(cf,!1),mf=d(cf,!0),gf=uf([{remove:["element"]},{moveToElement:["element"]},{moveToPosition:["position"]}]),pf=function(e,t,n,r){var o=r.getNode(!1===t);return Rl(rr.fromDom(e),rr.fromDom(n.getNode())).map(function(e){return Ml(e)?gf.remove(e.dom()):gf.moveToElement(o)}).orThunk(function(){return A.some(gf.moveToElement(o))})},hf=function(u,s,c){return cc.fromPosition(s,u,c).bind(function(e){return a=e.getNode(),bo(rr.fromDom(a))||po(rr.fromDom(a))?A.none():(t=u,o=e,i=function(e){return lo(rr.fromDom(e))&&!bs(r,o,t)},Ts(!(n=s),r=c).fold(function(){return Ts(n,o).fold(j(!1),i)},i)?A.none():s&&Do.isContentEditableFalse(e.getNode())?pf(u,s,c,e):!1===s&&Do.isContentEditableFalse(e.getNode(!0))?pf(u,s,c,e):s&&_s(c)?A.some(gf.moveToPosition(e)):!1===s&&Rs(c)?A.some(gf.moveToPosition(e)):A.none());var t,n,r,o,i,a})},vf=function(r,e,o){return i=e,a=o.getNode(!1===i),u=i?"after":"before",Do.isElement(a)&&a.getAttribute("data-mce-caret")===u?(t=e,n=o.getNode(!1===e),t&&Do.isContentEditableFalse(n.nextSibling)?A.some(gf.moveToElement(n.nextSibling)):!1===t&&Do.isContentEditableFalse(n.previousSibling)?A.some(gf.moveToElement(n.previousSibling)):A.none()).fold(function(){return hf(r,e,o)},A.some):hf(r,e,o).bind(function(e){return t=r,n=o,e.fold(function(e){return A.some(gf.remove(e))},function(e){return A.some(gf.moveToElement(e))},function(e){return bs(n,e,t)?A.none():A.some(gf.moveToPosition(e))});var t,n});var t,n,i,a,u},bf=function(e,t,n){if(0!==n){var r,o,i,a=e.data.slice(t,t+n),u=t+n>=e.data.length,s=0===t;e.replaceData(t,n,(o=s,i=u,U((r=a).split(""),function(e,t){return-1!==" \f\n\r\t\x0B".indexOf(t)||"\xa0"===t?e.previousCharIsSpace||""===e.str&&o||e.str.length===r.length-1&&i?{previousCharIsSpace:!1,str:e.str+"\xa0"}:{previousCharIsSpace:!0,str:e.str+" "}:{previousCharIsSpace:!1,str:e.str+t}},{previousCharIsSpace:!1,str:""}).str))}},yf=function(e,t){var n,r=e.data.slice(t),o=r.length-(n=r,n.replace(/^\s+/g,"")).length;return bf(e,t,o)},Cf=function(e,t){return r=e,o=(n=t).container(),i=n.offset(),!1===vu.isTextPosition(n)&&o===r.parentNode&&i>vu.before(r).offset()?vu(t.container(),t.offset()-1):t;var n,r,o,i},xf=function(e){return Ba(e.previousSibling)?A.some((t=e.previousSibling,Do.isText(t)?vu(t,t.data.length):vu.after(t))):e.previousSibling?cc.lastPositionIn(e.previousSibling):A.none();var t},wf=function(e){return Ba(e.nextSibling)?A.some((t=e.nextSibling,Do.isText(t)?vu(t,0):vu.before(t))):e.nextSibling?cc.firstPositionIn(e.nextSibling):A.none();var t},Nf=function(r,o){return xf(o).orThunk(function(){return wf(o)}).orThunk(function(){return e=r,t=o,n=vu.before(t.previousSibling?t.previousSibling:t.parentNode),cc.prevPosition(e,n).fold(function(){return cc.nextPosition(e,vu.after(t))},A.some);var e,t,n})},Ef=function(n,r){return wf(r).orThunk(function(){return xf(r)}).orThunk(function(){return e=n,t=r,cc.nextPosition(e,vu.after(t)).fold(function(){return cc.prevPosition(e,vu.before(t))},A.some);var e,t})},Sf=function(e,t,n){return(r=e,o=t,i=n,r?Ef(o,i):Nf(o,i)).map(d(Cf,n));var r,o,i},kf=function(t,n,e){e.fold(function(){t.focus()},function(e){t.selection.setRng(e.toRange(),n)})},Tf=function(e,t){return t&&e.schema.getBlockElements().hasOwnProperty(ur(t))},Af=function(e){if(Ml(e)){var t=rr.fromHtml('<br data-mce-bogus="1">');return Ri(e),Ti(e,t),A.some(vu.before(t.dom()))}return A.none()},Rf=function(e,t,l){var n=Mr(e).filter(function(e){return Do.isText(e.dom())}),r=Fr(e).filter(function(e){return Do.isText(e.dom())});return _i(e),$a([n,r,t],function(e,t,n){var r,o,i,a,u=e.dom(),s=t.dom(),c=u.data.length;return o=s,i=l,a=Xn((r=u).data).length,r.appendData(o.data),_i(rr.fromDom(o)),i&&yf(r,a),n.container()===s?vu(u,c):n}).orThunk(function(){return l&&(n.each(function(e){return t=e.dom(),n=e.dom().length,r=t.data.slice(0,n),o=r.length-Xn(r).length,bf(t,n-o,o);var t,n,r,o}),r.each(function(e){return yf(e.dom(),0)})),t})},_f=function(e,t){return n=e.schema.getTextInlineElements(),r=ur(t),dr.call(n,r);var n,r},Df=function(t,n,e,r){void 0===r&&(r=!0);var o,i=Sf(n,t.getBody(),e.dom()),a=$i(e,d(Tf,t),(o=t.getBody(),function(e){return e.dom()===o})),u=Rf(e,i,_f(t,e));t.dom.isEmpty(t.getBody())?(t.setContent(""),t.selection.setCursorLocation()):a.bind(Af).fold(function(){r&&kf(t,n,u)},function(e){r&&kf(t,n,A.some(e))})},Bf=function(a,u){var e,t,n,r,o,i;return(e=a.getBody(),t=u,n=a.selection.getRng(),r=Es(t?1:-1,e,n),o=vu.fromRangeStart(r),i=rr.fromDom(e),!1===t&&_s(o)?A.some(gf.remove(o.getNode(!0))):t&&Rs(o)?A.some(gf.remove(o.getNode())):!1===t&&Rs(o)&&ff(i,o)?df(i,o).map(function(e){return gf.remove(e.getNode())}):t&&_s(o)&&lf(i,o)?mf(i,o).map(function(e){return gf.remove(e.getNode())}):vf(e,t,o)).map(function(e){return e.fold((o=a,i=u,function(e){return o._selectionOverrides.hideFakeCaret(),Df(o,i,rr.fromDom(e)),!0}),(n=a,r=u,function(e){var t=r?vu.before(e):vu.after(e);return n.selection.setRng(t.toRange()),!0}),(t=a,function(e){return t.selection.setRng(e.toRange()),!0}));var t,n,r,o,i}).getOr(!1)},Of=function(e,t){var n,r=e.selection.getNode();return!!Do.isContentEditableFalse(r)&&(n=rr.fromDom(e.getBody()),F(Hi(n,".mce-offscreen-selection"),_i),Df(e,t,rr.fromDom(e.selection.getNode())),_l(e),!0)},Pf=function(e,t){return e.selection.isCollapsed()?Bf(e,t):Of(e,t)},Lf=function(e){var t,n=function(e,t){for(;t&&t!==e;){if(Do.isContentEditableTrue(t)||Do.isContentEditableFalse(t))return t;t=t.parentNode}return null}(e.getBody(),e.selection.getNode());return Do.isContentEditableTrue(n)&&e.dom.isBlock(n)&&e.dom.isEmpty(n)&&(t=e.dom.create("br",{"data-mce-bogus":"1"}),e.dom.setHTML(n,""),n.appendChild(t),e.selection.setRng(vu.before(t).toRange())),!0},If=Do.isText,Mf=function(e){return If(e)&&e.data[0]===la},Ff=function(e){return If(e)&&e.data[e.data.length-1]===la},zf=function(e){return e.ownerDocument.createTextNode(la)},Uf=function(e,t){return e?function(e){if(If(e.previousSibling))return Ff(e.previousSibling)||e.previousSibling.appendData(la),e.previousSibling;if(If(e))return Mf(e)||e.insertData(0,la),e;var t=zf(e);return e.parentNode.insertBefore(t,e),t}(t):function(e){if(If(e.nextSibling))return Mf(e.nextSibling)||e.nextSibling.insertData(0,la),e.nextSibling;if(If(e))return Ff(e)||e.appendData(la),e;var t=zf(e);return e.nextSibling?e.parentNode.insertBefore(t,e.nextSibling):e.parentNode.appendChild(t),t}(t)},Vf=d(Uf,!0),Hf=d(Uf,!1),jf=function(e,t){return Do.isText(e.container())?Uf(t,e.container()):Uf(t,e.getNode())},qf=function(e,t){var n=t.get();return n&&e.container()===n&&pa(n)},$f=function(n,e){return e.fold(function(e){Ju.remove(n.get());var t=Vf(e);return n.set(t),A.some(vu(t,t.length-1))},function(e){return cc.firstPositionIn(e).map(function(e){if(qf(e,n))return vu(n.get(),1);Ju.remove(n.get());var t=jf(e,!0);return n.set(t),vu(t,1)})},function(e){return cc.lastPositionIn(e).map(function(e){if(qf(e,n))return vu(n.get(),n.get().length-1);Ju.remove(n.get());var t=jf(e,!1);return n.set(t),vu(t,t.length-1)})},function(e){Ju.remove(n.get());var t=Hf(e);return n.set(t),A.some(vu(t,1))})},Wf=function(e,t){for(var n=0;n<e.length;n++){var r=e[n].apply(null,t);if(r.isSome())return r}return A.none()},Kf=uf([{before:["element"]},{start:["element"]},{end:["element"]},{after:["element"]}]),Xf=function(e,t){var n=vs(t,e);return n||e},Yf=function(e,t,n){var r=Al.normalizeForwards(n),o=Xf(t,r.container());return Al.findRootInline(e,o,r).fold(function(){return cc.nextPosition(o,r).bind(d(Al.findRootInline,e,o)).map(function(e){return Kf.before(e)})},A.none)},Gf=function(e,t){return null===Vu(e,t)},Jf=function(e,t,n){return Al.findRootInline(e,t,n).filter(d(Gf,t))},Qf=function(e,t,n){var r=Al.normalizeBackwards(n);return Jf(e,t,r).bind(function(e){return cc.prevPosition(e,r).isNone()?A.some(Kf.start(e)):A.none()})},Zf=function(e,t,n){var r=Al.normalizeForwards(n);return Jf(e,t,r).bind(function(e){return cc.nextPosition(e,r).isNone()?A.some(Kf.end(e)):A.none()})},ed=function(e,t,n){var r=Al.normalizeBackwards(n),o=Xf(t,r.container());return Al.findRootInline(e,o,r).fold(function(){return cc.prevPosition(o,r).bind(d(Al.findRootInline,e,o)).map(function(e){return Kf.after(e)})},A.none)},td=function(e){return!1===Al.isRtl(rd(e))},nd=function(e,t,n){return Wf([Yf,Qf,Zf,ed],[e,t,n]).filter(td)},rd=function(e){return e.fold(q,q,q,q)},od=function(e){return e.fold(j("before"),j("start"),j("end"),j("after"))},id=function(e){return e.fold(Kf.before,Kf.before,Kf.after,Kf.after)},ad=function(n,e,r,t,o,i){return $a([Al.findRootInline(e,r,t),Al.findRootInline(e,r,o)],function(e,t){return e!==t&&Al.hasSameParentBlock(r,e,t)?Kf.after(n?e:t):i}).getOr(i)},ud=function(e,r){return e.fold(j(!0),function(e){return n=r,!(od(t=e)===od(n)&&rd(t)===rd(n));var t,n})},sd=function(e,t){return e?t.fold(H(A.some,Kf.start),A.none,H(A.some,Kf.after),A.none):t.fold(A.none,H(A.some,Kf.before),A.none,H(A.some,Kf.end))},cd=function(a,u,s,c){var e=Al.normalizePosition(a,c),l=nd(u,s,e);return nd(u,s,e).bind(d(sd,a)).orThunk(function(){return t=a,n=u,r=s,o=l,e=c,i=Al.normalizePosition(t,e),cc.fromPosition(t,r,i).map(d(Al.normalizePosition,t)).fold(function(){return o.map(id)},function(e){return nd(n,r,e).map(d(ad,t,n,r,i,e)).filter(d(ud,o))}).filter(td);var t,n,r,o,e,i})},ld=nd,fd=cd,dd=(d(cd,!1),d(cd,!0),id),md=function(e){return e.fold(Kf.start,Kf.start,Kf.end,Kf.end)},gd=function(e){return P(e.selection.getSel().modify)},pd=function(e,t,n){var r=e?1:-1;return t.setRng(vu(n.container(),n.offset()+r).toRange()),t.getSel().modify("move",e?"forward":"backward","word"),!0},hd=function(e,t){var n=t.selection.getRng(),r=e?vu.fromRangeEnd(n):vu.fromRangeStart(n);return!!gd(t)&&(e&&ba(r)?pd(!0,t.selection,r):!(e||!ya(r))&&pd(!1,t.selection,r))},vd=function(e,t){var n=e.dom.createRng();n.setStart(t.container(),t.offset()),n.setEnd(t.container(),t.offset()),e.selection.setRng(n)},bd=function(e){return!1!==e.settings.inline_boundaries},yd=function(e,t){e?t.setAttribute("data-mce-selected","inline-boundary"):t.removeAttribute("data-mce-selected")},Cd=function(t,e,n){return $f(e,n).map(function(e){return vd(t,e),n})},xd=function(e,t,n){return function(){return!!bd(t)&&hd(e,t)}},wd={move:function(a,u,s){return function(){return!!bd(a)&&(t=a,n=u,e=s,r=t.getBody(),o=vu.fromRangeStart(t.selection.getRng()),i=d(Al.isInlineTarget,t),fd(e,i,r,o).bind(function(e){return Cd(t,n,e)})).isSome();var t,n,e,r,o,i}},moveNextWord:d(xd,!0),movePrevWord:d(xd,!1),setupSelectedState:function(a){var u=Oi(null),s=d(Al.isInlineTarget,a);return a.on("NodeChange",function(e){var t,n,r,o,i;bd(a)&&(t=s,n=a.dom,r=e.parents,o=z(n.select('*[data-mce-selected="inline-boundary"]'),t),i=z(r,t),F(Z(o,i),d(yd,!1)),F(Z(i,o),d(yd,!0)),function(e,t){if(e.selection.isCollapsed()&&!0!==e.composing&&t.get()){var n=vu.fromRangeStart(e.selection.getRng());vu.isTextPosition(n)&&!1===Al.isAtZwsp(n)&&(vd(e,Ju.removeAndReposition(t.get(),n)),t.set(null))}}(a,u),function(n,r,o,e){if(r.selection.isCollapsed()){var t=z(e,n);F(t,function(e){var t=vu.fromRangeStart(r.selection.getRng());ld(n,r.getBody(),t).bind(function(e){return Cd(r,o,e)})})}}(s,a,u,e.parents))}),u},setCaretPosition:vd},Nd=function(t,n){return function(e){return $f(n,e).map(function(e){return wd.setCaretPosition(t,e),!0}).getOr(!1)}},Ed=function(r,o,i,a){var u=r.getBody(),s=d(Al.isInlineTarget,r);r.undoManager.ignore(function(){var e,t,n;r.selection.setRng((e=i,t=a,(n=document.createRange()).setStart(e.container(),e.offset()),n.setEnd(t.container(),t.offset()),n)),r.execCommand("Delete"),ld(s,u,vu.fromRangeStart(r.selection.getRng())).map(md).map(Nd(r,o))}),r.nodeChanged()},Sd=function(n,r,i,o){var e,t,a=(e=n.getBody(),t=o.container(),vs(t,e)||e),u=d(Al.isInlineTarget,n),s=ld(u,a,o);return s.bind(function(e){return i?e.fold(j(A.some(md(e))),A.none,j(A.some(dd(e))),A.none):e.fold(A.none,j(A.some(dd(e))),A.none,j(A.some(md(e))))}).map(Nd(n,r)).getOrThunk(function(){var t=cc.navigate(i,a,o),e=t.bind(function(e){return ld(u,a,e)});return s.isSome()&&e.isSome()?Al.findRootInline(u,a,o).map(function(e){return o=e,!!$a([cc.firstPositionIn(o),cc.lastPositionIn(o)],function(e,t){var n=Al.normalizePosition(!0,e),r=Al.normalizePosition(!1,t);return cc.nextPosition(o,n).map(function(e){return e.isEqual(r)}).getOr(!0)}).getOr(!0)&&(Df(n,i,rr.fromDom(e)),!0);var o}).getOr(!1):e.bind(function(e){return t.map(function(e){return i?Ed(n,r,o,e):Ed(n,r,e,o),!0})}).getOr(!1)})},kd=function(e,t,n){if(e.selection.isCollapsed()&&!1!==e.settings.inline_boundaries){var r=vu.fromRangeStart(e.selection.getRng());return Sd(e,t,n,r)}return!1},Td=Er("start","end"),Ad=Er("rng","table","cells"),Rd=uf([{removeTable:["element"]},{emptyCells:["cells"]}]),_d=function(e,t){return Yi(rr.fromDom(e),"td,th",t)},Dd=function(e,t){return Ki(e,"table",t)},Bd=function(e){return!1===Or(e.start(),e.end())},Od=function(e,n){return Dd(e.start(),n).bind(function(t){return Dd(e.end(),n).bind(function(e){return Or(t,e)?A.some(t):A.none()})})},Pd=function(e){return Hi(e,"td,th")},Ld=function(r,e){var t=_d(e.startContainer,r),n=_d(e.endContainer,r);return e.collapsed?A.none():$a([t,n],Td).fold(function(){return t.fold(function(){return n.bind(function(t){return Dd(t,r).bind(function(e){return ee(Pd(e)).map(function(e){return Td(e,t)})})})},function(t){return Dd(t,r).bind(function(e){return te(Pd(e)).map(function(e){return Td(t,e)})})})},function(e){return Id(r,e)?A.none():(n=r,Dd((t=e).start(),n).bind(function(e){return te(Pd(e)).map(function(e){return Td(t.start(),e)})}));var t,n})},Id=function(e,t){return Od(t,e).isSome()},Md=function(e,t){var n,r,o,i,a=d(Or,e);return(n=t,r=a,o=_d(n.startContainer,r),i=_d(n.endContainer,r),$a([o,i],Td).filter(Bd).filter(function(e){return Id(r,e)}).orThunk(function(){return Ld(r,n)})).bind(function(e){return Od(t=e,a).map(function(e){return Ad(t,e,Pd(e))});var t})},Fd=function(e,t){return K(e,function(e){return Or(e,t)})},zd=function(n){return(r=n,$a([Fd(r.cells(),r.rng().start()),Fd(r.cells(),r.rng().end())],function(e,t){return r.cells().slice(e,t+1)})).map(function(e){var t=n.cells();return e.length===t.length?Rd.removeTable(n.table()):Rd.emptyCells(e)});var r},Ud=function(e,t){return Md(e,t).bind(zd)},Vd=function(e){var t=[];if(e)for(var n=0;n<e.rangeCount;n++)t.push(e.getRangeAt(n));return t},Hd=Vd,jd=function(e){return G(e,function(e){var t=Va(e);return t?[rr.fromDom(t)]:[]})},qd=function(e){return 1<Vd(e).length},$d=function(e){return z(jd(e),bo)},Wd=function(e){return Hi(e,"td[data-mce-selected],th[data-mce-selected]")},Kd=function(e,t){var n=Wd(t),r=$d(e);return 0<n.length?n:r},Xd=Kd,Yd=function(e){return Kd(Hd(e.selection.getSel()),rr.fromDom(e.getBody()))},Gd=function(e,t){return F(t,rl),e.selection.setCursorLocation(t[0].dom(),0),!0},Jd=function(e,t){return Df(e,!1,t),!0},Qd=function(n,e,r,t){return em(e,t).fold(function(){return t=n,Ud(e,r).map(function(e){return e.fold(d(Jd,t),d(Gd,t))});var t},function(e){return tm(n,e)}).getOr(!1)},Zd=function(e,t){return V(Wl(t,e),bo)},em=function(e,t){return V(Wl(t,e),function(e){return"caption"===ur(e)})},tm=function(e,t){return rl(t),e.selection.setCursorLocation(t.dom(),0),A.some(!0)},nm=function(u,s,c,l,f){return cc.navigate(c,u.getBody(),f).bind(function(e){return r=l,o=c,i=f,a=e,cc.firstPositionIn(r.dom()).bind(function(t){return cc.lastPositionIn(r.dom()).map(function(e){return o?i.isEqual(t)&&a.isEqual(e):i.isEqual(e)&&a.isEqual(t)})}).getOr(!0)?tm(u,l):(t=l,n=e,em(s,rr.fromDom(n.getNode())).map(function(e){return!1===Or(e,t)}));var t,n,r,o,i,a}).or(A.some(!0))},rm=function(a,u,s,e){var c=vu.fromRangeStart(a.selection.getRng());return Zd(s,e).bind(function(e){return Ml(e)?tm(a,e):(t=a,n=s,r=u,o=e,i=c,cc.navigate(r,t.getBody(),i).bind(function(e){return Zd(n,rr.fromDom(e.getNode())).map(function(e){return!1===Or(e,o)})}));var t,n,r,o,i})},om=function(a,u,e){var s=rr.fromDom(a.getBody());return em(s,e).fold(function(){return rm(a,u,s,e)},function(e){return t=a,n=u,r=s,o=e,i=vu.fromRangeStart(t.selection.getRng()),Ml(o)?tm(t,o):nm(t,r,n,o,i);var t,n,r,o,i}).getOr(!1)},im=function(e,t){var n,r,o,i,a,u=rr.fromDom(e.selection.getStart(!0)),s=Yd(e);return e.selection.isCollapsed()&&0===s.length?om(e,t,u):(n=e,r=u,o=rr.fromDom(n.getBody()),i=n.selection.getRng(),0!==(a=Yd(n)).length?Gd(n,a):Qd(n,o,i,r))},am=Nc.isEq,um=function(e,t,n){var r=e.formatter.get(n);if(r)for(var o=0;o<r.length;o++)if(!1===r[o].inherit&&e.dom.is(t,r[o].selector))return!0;return!1},sm=function(t,e,n,r){var o=t.dom.getRoot();return e!==o&&(e=t.dom.getParent(e,function(e){return!!um(t,e,n)||e.parentNode===o||!!fm(t,e,n,r,!0)}),fm(t,e,n,r))},cm=function(e,t,n){return!!am(t,n.inline)||!!am(t,n.block)||(n.selector?1===t.nodeType&&e.is(t,n.selector):void 0)},lm=function(e,t,n,r,o,i){var a,u,s,c=n[r];if(n.onmatch)return n.onmatch(t,n,r);if(c)if("undefined"==typeof c.length){for(a in c)if(c.hasOwnProperty(a)){if(u="attributes"===r?e.getAttrib(t,a):Nc.getStyle(e,t,a),o&&!u&&!n.exact)return;if((!o||n.exact)&&!am(u,Nc.normalizeStyleValue(e,Nc.replaceVars(c[a],i),a)))return}}else for(s=0;s<c.length;s++)if("attributes"===r?e.getAttrib(t,c[s]):Nc.getStyle(e,t,c[s]))return n;return n},fm=function(e,t,n,r,o){var i,a,u,s,c=e.formatter.get(n),l=e.dom;if(c&&t)for(a=0;a<c.length;a++)if(i=c[a],cm(e.dom,t,i)&&lm(l,t,i,"attributes",o,r)&&lm(l,t,i,"styles",o,r)){if(s=i.classes)for(u=0;u<s.length;u++)if(!e.dom.hasClass(t,s[u]))return;return i}},dm={matchNode:fm,matchName:cm,match:function(e,t,n,r){var o;return r?sm(e,r,t,n):(r=e.selection.getNode(),!!sm(e,r,t,n)||!((o=e.selection.getStart())===r||!sm(e,o,t,n)))},matchAll:function(r,o,i){var e,a=[],u={};return e=r.selection.getStart(),r.dom.getParent(e,function(e){var t,n;for(t=0;t<o.length;t++)n=o[t],!u[n]&&fm(r,e,n,i)&&(u[n]=!0,a.push(n))},r.dom.getRoot()),a},canApply:function(e,t){var n,r,o,i,a,u=e.formatter.get(t),s=e.dom;if(u)for(n=e.selection.getStart(),r=Nc.getParents(s,n),i=u.length-1;0<=i;i--){if(!(a=u[i].selector)||u[i].defaultBlock)return!0;for(o=r.length-1;0<=o;o--)if(s.is(r[o],a))return!0}return!1},matchesUnInheritedFormatSelector:um},mm=function(e,t){return e.splitText(t)},gm=function(e){var t=e.startContainer,n=e.startOffset,r=e.endContainer,o=e.endOffset;return t===r&&Do.isText(t)?0<n&&n<t.nodeValue.length&&(t=(r=mm(t,n)).previousSibling,n<o?(t=r=mm(r,o-=n).previousSibling,o=r.nodeValue.length,n=0):o=0):(Do.isText(t)&&0<n&&n<t.nodeValue.length&&(t=mm(t,n),n=0),Do.isText(r)&&0<o&&o<r.nodeValue.length&&(o=(r=mm(r,o).previousSibling).nodeValue.length)),{startContainer:t,startOffset:n,endContainer:r,endOffset:o}},pm=la,hm="_mce_caret",vm=function(e){return 0<function(e){for(var t=[];e;){if(3===e.nodeType&&e.nodeValue!==pm||1<e.childNodes.length)return[];1===e.nodeType&&t.push(e),e=e.firstChild}return t}(e).length},bm=function(e){var t;if(e)for(e=(t=new ro(e,e)).current();e;e=t.next())if(3===e.nodeType)return e;return null},ym=function(e){var t=rr.fromTag("span");return br(t,{id:hm,"data-mce-bogus":"1","data-mce-type":"format-caret"}),e&&Ti(t,rr.fromText(pm)),t},Cm=function(e,t,n){void 0===n&&(n=!0);var r,o=e.dom,i=e.selection;if(vm(t))Df(e,!1,rr.fromDom(t),n);else{var a=i.getRng(),u=o.getParent(t,o.isBlock),s=((r=bm(t))&&r.nodeValue.charAt(0)===pm&&r.deleteData(0,1),r);a.startContainer===s&&0<a.startOffset&&a.setStart(s,a.startOffset-1),a.endContainer===s&&0<a.endOffset&&a.setEnd(s,a.endOffset-1),o.remove(t,!0),u&&o.isEmpty(u)&&rl(rr.fromDom(u)),i.setRng(a)}},xm=function(e,t,n){void 0===n&&(n=!0);var r=e.dom,o=e.selection;if(t)Cm(e,t,n);else if(!(t=Vu(e.getBody(),o.getStart())))for(;t=r.get(hm);)Cm(e,t,!1)},wm=function(e,t,n){var r=e.dom,o=r.getParent(n,d(Nc.isTextBlock,e));o&&r.isEmpty(o)?n.parentNode.replaceChild(t,n):(nl(rr.fromDom(n)),r.isEmpty(n)?n.parentNode.replaceChild(t,n):r.insertAfter(t,n))},Nm=function(e,t){return e.appendChild(t),t},Em=function(e,t){var n,r,o=(n=function(e,t){return Nm(e,t.cloneNode(!1))},r=t,function(e,t){for(var n=e.length-1;0<=n;n--)t(e[n],n,e)}(e,function(e){r=n(r,e)}),r);return Nm(o,o.ownerDocument.createTextNode(pm))},Sm=function(i){i.on("mouseup keydown",function(e){var t,n,r,o;t=i,n=e.keyCode,r=t.selection,o=t.getBody(),xm(t,null,!1),8!==n&&46!==n||!r.isCollapsed()||r.getStart().innerHTML!==pm||xm(t,Vu(o,r.getStart())),37!==n&&39!==n||xm(t,Vu(o,r.getStart()))})},km=function(e,t){return e.schema.getTextInlineElements().hasOwnProperty(ur(t))&&!Uu(t.dom())&&!Do.isBogus(t.dom())},Tm=function(e){return 1===Vr(e).length},Am=function(e,t,n,r){var o,i,a,u,s=d(km,t),c=$(z(r,s),function(e){return e.dom()});if(0===c.length)Df(t,e,n);else{var l=(o=n.dom(),i=c,a=ym(!1),u=Em(i,a.dom()),Ei(rr.fromDom(o),a),_i(rr.fromDom(o)),vu(u,0));t.selection.setRng(l.toRange())}},Rm=function(r,o){var t,e=rr.fromDom(r.getBody()),n=rr.fromDom(r.selection.getStart()),i=z((t=Wl(n,e),K(t,co).fold(j(t),function(e){return t.slice(0,e)})),Tm);return te(i).map(function(e){var t,n=vu.fromRangeStart(r.selection.getRng());return!(!Dl(o,n,e.dom())||Uu((t=e).dom())&&vm(t.dom())||(Am(o,r,e,i),0))}).getOr(!1)},_m=function(e,t){return!!e.selection.isCollapsed()&&Rm(e,t)},Dm=Do.isContentEditableTrue,Bm=Do.isContentEditableFalse,Om=function(e,t,n,r,o){return t._selectionOverrides.showCaret(e,n,r,o)},Pm=function(e,t){var n,r;return e.fire("BeforeObjectSelected",{target:t}).isDefaultPrevented()?null:((r=(n=t).ownerDocument.createRange()).selectNode(n),r)},Lm=function(e,t,n){var r=Es(1,e.getBody(),t),o=vu.fromRangeStart(r),i=o.getNode();if(Bm(i))return Om(1,e,i,!o.isAtEnd(),!1);var a=o.getNode(!0);if(Bm(a))return Om(1,e,a,!1,!1);var u=e.dom.getParent(o.getNode(),function(e){return Bm(e)||Dm(e)});return Bm(u)?Om(1,e,u,!1,n):null},Im=function(e,t,n){if(!t||!t.collapsed)return t;var r=Lm(e,t,n);return r||t},Mm=_s,Fm=Rs,zm=function(e,t,n,r,o,i){var a,u,s=Om(r,e,i.getNode(!o),o,!0);if(t.collapsed){var c=t.cloneRange();o?c.setEnd(s.startContainer,s.startOffset):c.setStart(s.endContainer,s.endOffset),c.deleteContents()}else t.deleteContents();return e.selection.setRng(s),a=e.dom,u=n,Do.isText(u)&&0===u.data.length&&a.remove(u),!0},Um=function(e,t){return function(e,t){var n=e.selection.getRng();if(!Do.isText(n.commonAncestorContainer))return!1;var r=t?pu.Forwards:pu.Backwards,o=ec(e.getBody()),i=d(Ms,o.next),a=d(Ms,o.prev),u=t?i:a,s=t?Fm:Mm,c=As(r,e.getBody(),n),l=Al.normalizePosition(t,u(c));if(!l)return!1;if(s(l))return zm(e,n,c.getNode(),r,t,l);var f=u(l);return!!(f&&s(f)&&Fs(l,f))&&zm(e,n,c.getNode(),r,t,f)}(e,t)},Vm=function(e,t){e.getDoc().execCommand(t,!1,null)},Hm=function(e){Pf(e,!1)||Um(e,!1)||kd(e,!1)||tf(e,!1)||im(e)||af(e,!1)||_m(e,!1)||(Vm(e,"Delete"),_l(e))},jm=function(e){Pf(e,!0)||Um(e,!0)||kd(e,!0)||tf(e,!0)||im(e)||af(e,!0)||_m(e,!0)||Vm(e,"ForwardDelete")},qm=function(e,t,n){var r=e.getParam(t,n);if(-1!==r.indexOf("=")){var o=e.getParam(t,"","hash");return o.hasOwnProperty(e.id)?o[e.id]:n}return r},$m=function(e){return e.getParam("iframe_attrs",{})},Wm=function(e){return e.getParam("doctype","<!DOCTYPE html>")},Km=function(e){return e.getParam("document_base_url","")},Xm=function(e){return qm(e,"body_id","tinymce")},Ym=function(e){return qm(e,"body_class","")},Gm=function(e){return e.getParam("content_security_policy","")},Jm=function(e){return e.getParam("br_in_pre",!0)},Qm=function(e){if(e.getParam("force_p_newlines",!1))return"p";var t=e.getParam("forced_root_block","p");return!1===t?"":t},Zm=function(e){return e.getParam("forced_root_block_attrs",{})},eg=function(e){return e.getParam("br_newline_selector",".mce-toc h2,figcaption,caption")},tg=function(e){return e.getParam("no_newline_selector","")},ng=function(e){return e.getParam("keep_styles",!0)},rg=function(e){return e.getParam("end_container_on_empty_block",!1)},og=function(e){return Yt.explode(e.getParam("font_size_style_values",""))},ig=function(e){return Yt.explode(e.getParam("font_size_classes",""))},ag=function(e){return e.getParam("images_dataimg_filter",j(!0),"function")},ug=function(e){return e.getParam("automatic_uploads",!0,"boolean")},sg=function(e){return e.getParam("images_reuse_filename",!1,"boolean")},cg=function(e){return e.getParam("images_replace_blob_uris",!0,"boolean")},lg=function(e){return e.getParam("images_upload_url","","string")},fg=function(e){return e.getParam("images_upload_base_path","","string")},dg=function(e){return e.getParam("images_upload_credentials",!1,"boolean")},mg=function(e){return e.getParam("images_upload_handler",null,"function")},gg=function(e){return e.getParam("content_css_cors",!1,"boolean")},pg=function(o,t,e){var n=function(e){return t=o,n=e.dom(),r=Nr(n,t),A.from(r).filter(function(e){return 0<e.length});var t,n,r};return Wi(rr.fromDom(e),function(e){return n(e).isSome()},function(e){return Or(rr.fromDom(t),e)}).bind(n)},hg=function(o){return function(r,e){return A.from(e).map(rr.fromDom).filter(cr).bind(function(e){return pg(o,r,e.dom()).or((t=o,n=e.dom(),A.from(pi.DOM.getStyle(n,t,!0))));var t,n}).getOr("")}},vg={getFontSize:hg("font-size"),getFontFamily:H(function(e){return e.replace(/[\'\"\\]/g,"").replace(/,\s+/g,",")},hg("font-family")),toPt:function(e,t){return/[0-9.]+px$/.test(e)?(n=72*parseInt(e,10)/96,r=t||0,o=Math.pow(10,r),Math.round(n*o)/o+"pt"):e;var n,r,o}},bg=function(e){return cc.firstPositionIn(e.getBody()).map(function(e){var t=e.container();return Do.isText(t)?t.parentNode:t})},yg=function(o){return A.from(o.selection.getRng()).bind(function(e){var t,n,r=o.getBody();return n=r,(t=e).startContainer===n&&0===t.startOffset?A.none():A.from(o.selection.getStart(!0))})},Cg=function(e,t){if(/^[0-9\.]+$/.test(t)){var n=parseInt(t,10);if(1<=n&&n<=7){var r=og(e),o=ig(e);return o?o[n-1]||t:r[n-1]||t}return t}return t},xg=function(e,t){return e&&t&&e.startContainer===t.startContainer&&e.startOffset===t.startOffset&&e.endContainer===t.endContainer&&e.endOffset===t.endOffset},wg=function(e,t,n){return null!==function(e,t,n){for(;e&&e!==t;){if(n(e))return e;e=e.parentNode}return null}(e,t,n)},Ng=function(e,t,n){return wg(e,t,function(e){return e.nodeName===n})},Eg=function(e){return e&&"TABLE"===e.nodeName},Sg=function(e,t,n){for(var r=new ro(t,e.getParent(t.parentNode,e.isBlock)||e.getRoot());t=r[n?"prev":"next"]();)if(Do.isBr(t))return!0},kg=function(e,t,n,r,o){var i,a,u,s,c,l,f=e.getRoot(),d=e.schema.getNonEmptyElements();if(u=e.getParent(o.parentNode,e.isBlock)||f,r&&Do.isBr(o)&&t&&e.isEmpty(u))return A.some(mu(o.parentNode,e.nodeIndex(o)));for(i=new ro(o,u);s=i[r?"prev":"next"]();){if("false"===e.getContentEditableParent(s)||(l=f,ha(c=s)&&!1===wg(c,l,Uu)))return A.none();if(Do.isText(s)&&0<s.nodeValue.length)return!1===Ng(s,f,"A")?A.some(mu(s,r?s.nodeValue.length:0)):A.none();if(e.isBlock(s)||d[s.nodeName.toLowerCase()])return A.none();a=s}return n&&a?A.some(mu(a,0)):A.none()},Tg=function(e,t,n,r){var o,i,a,u,s,c,l,f,d,m,g=e.getRoot(),p=!1;if(o=r[(n?"start":"end")+"Container"],i=r[(n?"start":"end")+"Offset"],l=Do.isElement(o)&&i===o.childNodes.length,s=e.schema.getNonEmptyElements(),c=n,ha(o))return A.none();if(Do.isElement(o)&&i>o.childNodes.length-1&&(c=!1),Do.isDocument(o)&&(o=g,i=0),o===g){if(c&&(u=o.childNodes[0<i?i-1:0])){if(ha(u))return A.none();if(s[u.nodeName]||Eg(u))return A.none()}if(o.hasChildNodes()){if(i=Math.min(!c&&0<i?i-1:i,o.childNodes.length-1),o=o.childNodes[i],i=Do.isText(o)&&l?o.data.length:0,!t&&o===g.lastChild&&Eg(o))return A.none();if(function(e,t){for(;t&&t!==e;){if(Do.isContentEditableFalse(t))return!0;t=t.parentNode}return!1}(g,o)||ha(o))return A.none();if(o.hasChildNodes()&&!1===Eg(o)){a=new ro(u=o,g);do{if(Do.isContentEditableFalse(u)||ha(u)){p=!1;break}if(Do.isText(u)&&0<u.nodeValue.length){i=c?0:u.nodeValue.length,o=u,p=!0;break}if(s[u.nodeName.toLowerCase()]&&(!(f=u)||!/^(TD|TH|CAPTION)$/.test(f.nodeName))){i=e.nodeIndex(u),o=u.parentNode,c||i++,p=!0;break}}while(u=c?a.next():a.prev())}}}return t&&(Do.isText(o)&&0===i&&kg(e,l,t,!0,o).each(function(e){o=e.container(),i=e.offset(),p=!0}),Do.isElement(o)&&((u=o.childNodes[i])||(u=o.childNodes[i-1]),!u||!Do.isBr(u)||(m="A",(d=u).previousSibling&&d.previousSibling.nodeName===m)||Sg(e,u,!1)||Sg(e,u,!0)||kg(e,l,t,!0,u).each(function(e){o=e.container(),i=e.offset(),p=!0}))),c&&!t&&Do.isText(o)&&i===o.nodeValue.length&&kg(e,l,t,!1,o).each(function(e){o=e.container(),i=e.offset(),p=!0}),p?A.some(mu(o,i)):A.none()},Ag=function(e,t){var n=t.collapsed,r=t.cloneRange(),o=mu.fromRangeStart(t);return Tg(e,n,!0,r).each(function(e){n&&mu.isAbove(o,e)||r.setStart(e.container(),e.offset())}),n||Tg(e,n,!1,r).each(function(e){r.setEnd(e.container(),e.offset())}),n&&r.collapse(!0),xg(t,r)?A.none():A.some(r)},Rg=function(e,t,n){var r=e.create("span",{},"&nbsp;");n.parentNode.insertBefore(r,n),t.scrollIntoView(r),e.remove(r)},_g=function(e,t,n,r){var o=e.createRng();r?(o.setStartBefore(n),o.setEndBefore(n)):(o.setStartAfter(n),o.setEndAfter(n)),t.setRng(o)},Dg=function(e,t){var n,r,o=e.selection,i=e.dom,a=o.getRng();Ag(i,a).each(function(e){a.setStart(e.startContainer,e.startOffset),a.setEnd(e.endContainer,e.endOffset)});var u=a.startOffset,s=a.startContainer;if(1===s.nodeType&&s.hasChildNodes()){var c=u>s.childNodes.length-1;s=s.childNodes[Math.min(u,s.childNodes.length-1)]||s,u=c&&3===s.nodeType?s.nodeValue.length:0}var l=i.getParent(s,i.isBlock),f=l?i.getParent(l.parentNode,i.isBlock):null,d=f?f.nodeName.toUpperCase():"",m=t&&t.ctrlKey;"LI"!==d||m||(l=f),s&&3===s.nodeType&&u>=s.nodeValue.length&&(function(e,t,n){for(var r,o=new ro(t,n),i=e.getNonEmptyElements();r=o.next();)if(i[r.nodeName.toLowerCase()]||0<r.length)return!0}(e.schema,s,l)||(n=i.create("br"),a.insertNode(n),a.setStartAfter(n),a.setEndAfter(n),r=!0)),n=i.create("br"),Au(i,a,n),Rg(i,o,n),_g(i,o,n,r),e.undoManager.add()},Bg=function(e,t){var n=rr.fromTag("br");Ei(rr.fromDom(t),n),e.undoManager.add()},Og=function(e,t){Pg(e.getBody(),t)||Si(rr.fromDom(t),rr.fromTag("br"));var n=rr.fromTag("br");Si(rr.fromDom(t),n),Rg(e.dom,e.selection,n.dom()),_g(e.dom,e.selection,n.dom(),!1),e.undoManager.add()},Pg=function(e,t){return n=vu.after(t),!!Do.isBr(n.getNode())||cc.nextPosition(e,vu.after(t)).map(function(e){return Do.isBr(e.getNode())}).getOr(!1);var n},Lg=function(e){return e&&"A"===e.nodeName&&"href"in e},Ig=function(e){return e.fold(j(!1),Lg,Lg,j(!1))},Mg=function(e,t){t.fold(o,d(Bg,e),d(Og,e),o)},Fg=function(e,t){var n,r,o,i=(n=e,r=d(Al.isInlineTarget,n),o=vu.fromRangeStart(n.selection.getRng()),ld(r,n.getBody(),o).filter(Ig));i.isSome()?i.each(d(Mg,e)):Dg(e,t)},zg=(uf([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),uf([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}])),Ug=Er("start","soffset","finish","foffset"),Vg=(zg.domRange,zg.relative,zg.exact,tr.detect().browser),Hg=function(e,t){var n=lr(t)?zc(t).length:Vr(t).length+1;return n<e?n:e<0?0:e},jg=function(e){return Ug(e.start(),Hg(e.soffset(),e.start()),e.finish(),Hg(e.foffset(),e.finish()))},qg=function(e,t){return Pr(e,t)||Or(e,t)},$g=function(t){return function(e){return qg(t,e.start())&&qg(t,e.finish())}},Wg=function(e){return!0===e.inline||Vg.isIE()},Kg=function(e){return Ug(rr.fromDom(e.startContainer),e.startOffset,rr.fromDom(e.endContainer),e.endOffset)},Xg=function(e){var t=e.getSelection();return(t&&0!==t.rangeCount?A.from(t.getRangeAt(0)):A.none()).map(Kg)},Yg=function(e){var t,n=(t=e.dom().ownerDocument.defaultView,rr.fromDom(t));return Xg(n.dom()).filter($g(e))},Gg=function(e,t){return A.from(t).filter($g(e)).map(jg)},Jg=function(e){var t=document.createRange();try{return t.setStart(e.start().dom(),e.soffset()),t.setEnd(e.finish().dom(),e.foffset()),A.some(t)}catch(n){return A.none()}},Qg=function(e){return(e.bookmark?e.bookmark:A.none()).bind(d(Gg,rr.fromDom(e.getBody()))).bind(Jg)},Zg=function(e){var t=Wg(e)?Yg(rr.fromDom(e.getBody())):A.none();e.bookmark=t.isSome()?t:e.bookmark},ep=function(t){Qg(t).each(function(e){t.selection.setRng(e)})},tp=Qg,np=function(e){return go(e)||po(e)},rp=function(e){return z($(e.selection.getSelectedBlocks(),rr.fromDom),function(e){return!np(e)&&!Ir(e).map(np).getOr(!1)})},op=function(e,t){var n=e.settings,r=e.dom,o=e.selection,i=e.formatter,a=/[a-z%]+$/i.exec(n.indentation)[0],u=parseInt(n.indentation,10),s=e.getParam("indent_use_margin",!1);e.queryCommandState("InsertUnorderedList")||e.queryCommandState("InsertOrderedList")||n.forced_root_block||r.getParent(o.getNode(),r.isBlock)||i.apply("div"),F(rp(e),function(e){!function(e,t,n,r,o,i){if("false"!==e.getContentEditable(i)){var a=n?"margin":"padding";if(a="TABLE"===i.nodeName?"margin":a,a+="rtl"===e.getStyle(i,"direction",!0)?"Right":"Left","outdent"===t){var u=Math.max(0,parseInt(i.style[a]||0,10)-r);e.setStyle(i,a,u?u+o:"")}else u=parseInt(i.style[a]||0,10)+r+o,e.setStyle(i,a,u)}}(r,t,s,u,a,e.dom())})},ip=Yt.each,ap=Yt.extend,up=Yt.map,sp=Yt.inArray;function cp(s){var o,i,a,t,c={state:{},exec:{},value:{}},n=s.settings;s.on("PreInit",function(){o=s.dom,i=s.selection,n=s.settings,a=s.formatter});var r=function(e){var t;if(!s.quirks.isHidden()&&!s.removed){if(e=e.toLowerCase(),t=c.state[e])return t(e);try{return s.getDoc().queryCommandState(e)}catch(n){}return!1}},e=function(e,n){n=n||"exec",ip(e,function(t,e){ip(e.toLowerCase().split(","),function(e){c[n][e]=t})})},u=function(e,t,n){e=e.toLowerCase(),c.value[e]=function(){return t.call(n||s)}};ap(this,{execCommand:function(t,n,r,e){var o,i,a=!1;if(!s.removed){if(/^(mceAddUndoLevel|mceEndUndoLevel|mceBeginUndoLevel|mceRepaint)$/.test(t)||e&&e.skip_focus?ep(s):s.focus(),(e=s.fire("BeforeExecCommand",{command:t,ui:n,value:r})).isDefaultPrevented())return!1;if(i=t.toLowerCase(),o=c.exec[i])return o(i,n,r),s.fire("ExecCommand",{command:t,ui:n,value:r}),!0;if(ip(s.plugins,function(e){if(e.execCommand&&e.execCommand(t,n,r))return s.fire("ExecCommand",{command:t,ui:n,value:r}),!(a=!0)}),a)return a;if(s.theme&&s.theme.execCommand&&s.theme.execCommand(t,n,r))return s.fire("ExecCommand",{command:t,ui:n,value:r}),!0;try{a=s.getDoc().execCommand(t,n,r)}catch(u){}return!!a&&(s.fire("ExecCommand",{command:t,ui:n,value:r}),!0)}},queryCommandState:r,queryCommandValue:function(e){var t;if(!s.quirks.isHidden()&&!s.removed){if(e=e.toLowerCase(),t=c.value[e])return t(e);try{return s.getDoc().queryCommandValue(e)}catch(n){}}},queryCommandSupported:function(e){if(e=e.toLowerCase(),c.exec[e])return!0;try{return s.getDoc().queryCommandSupported(e)}catch(t){}return!1},addCommands:e,addCommand:function(e,o,i){e=e.toLowerCase(),c.exec[e]=function(e,t,n,r){return o.call(i||s,t,n,r)}},addQueryStateHandler:function(e,t,n){e=e.toLowerCase(),c.state[e]=function(){return t.call(n||s)}},addQueryValueHandler:u,hasCustomCommand:function(e){return e=e.toLowerCase(),!!c.exec[e]}});var l=function(e,t,n){return t===undefined&&(t=!1),n===undefined&&(n=null),s.getDoc().execCommand(e,t,n)},f=function(e){return a.match(e)},d=function(e,t){a.toggle(e,t?{value:t}:undefined),s.nodeChanged()},m=function(e){t=i.getBookmark(e)},g=function(){i.moveToBookmark(t)};e({"mceResetDesignMode,mceBeginUndoLevel":function(){},"mceEndUndoLevel,mceAddUndoLevel":function(){s.undoManager.add()},"Cut,Copy,Paste":function(e){var t,n=s.getDoc();try{l(e)}catch(o){t=!0}if("paste"!==e||n.queryCommandEnabled(e)||(t=!0),t||!n.queryCommandSupported(e)){var r=s.translate("Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X/C/V keyboard shortcuts instead.");de.mac&&(r=r.replace(/Ctrl\+/g,"\u2318+")),s.notificationManager.open({text:r,type:"error"})}},unlink:function(){if(i.isCollapsed()){var e=s.dom.getParent(s.selection.getStart(),"a");e&&s.dom.remove(e,!0)}else a.remove("link")},"JustifyLeft,JustifyCenter,JustifyRight,JustifyFull,JustifyNone":function(e){var t=e.substring(7);"full"===t&&(t="justify"),ip("left,center,right,justify".split(","),function(e){t!==e&&a.remove("align"+e)}),"none"!==t&&d("align"+t)},"InsertUnorderedList,InsertOrderedList":function(e){var t,n;l(e),(t=o.getParent(i.getNode(),"ol,ul"))&&(n=t.parentNode,/^(H[1-6]|P|ADDRESS|PRE)$/.test(n.nodeName)&&(m(),o.split(n,t),g()))},"Bold,Italic,Underline,Strikethrough,Superscript,Subscript":function(e){d(e)},"ForeColor,HiliteColor":function(e,t,n){d(e,n)},FontName:function(e,t,n){var r,o;o=n,(r=s).formatter.toggle("fontname",{value:Cg(r,o)}),r.nodeChanged()},FontSize:function(e,t,n){var r,o;o=n,(r=s).formatter.toggle("fontsize",{value:Cg(r,o)}),r.nodeChanged()},RemoveFormat:function(e){a.remove(e)},mceBlockQuote:function(){d("blockquote")},FormatBlock:function(e,t,n){return d(n||"p")},mceCleanup:function(){var e=i.getBookmark();s.setContent(s.getContent()),i.moveToBookmark(e)},mceRemoveNode:function(e,t,n){var r=n||i.getNode();r!==s.getBody()&&(m(),s.dom.remove(r,!0),g())},mceSelectNodeDepth:function(e,t,n){var r=0;o.getParent(i.getNode(),function(e){if(1===e.nodeType&&r++===n)return i.select(e),!1},s.getBody())},mceSelectNode:function(e,t,n){i.select(n)},mceInsertContent:function(e,t,n){ml(s,n)},mceInsertRawHTML:function(e,t,n){i.setContent("tiny_mce_marker");var r=s.getContent();s.setContent(r.replace(/tiny_mce_marker/g,function(){return n}))},mceToggleFormat:function(e,t,n){d(n)},mceSetContent:function(e,t,n){s.setContent(n)},"Indent,Outdent":function(e){op(s,e)},mceRepaint:function(){},InsertHorizontalRule:function(){s.execCommand("mceInsertContent",!1,"<hr />")},mceToggleVisualAid:function(){s.hasVisual=!s.hasVisual,s.addVisual()},mceReplaceContent:function(e,t,n){s.execCommand("mceInsertContent",!1,n.replace(/\{\$selection\}/g,i.getContent({format:"text"})))},mceInsertLink:function(e,t,n){var r;"string"==typeof n&&(n={href:n}),r=o.getParent(i.getNode(),"a"),n.href=n.href.replace(" ","%20"),r&&n.href||a.remove("link"),n.href&&a.apply("link",n,r)},selectAll:function(){var e=o.getParent(i.getStart(),Do.isContentEditableTrue);if(e){var t=o.createRng();t.selectNodeContents(e),i.setRng(t)}},"delete":function(){Hm(s)},forwardDelete:function(){jm(s)},mceNewDocument:function(){s.setContent("")},InsertLineBreak:function(e,t,n){return Fg(s,n),!0}});var p=function(n){return function(){var e=i.isCollapsed()?[o.getParent(i.getNode(),o.isBlock)]:i.getSelectedBlocks(),t=up(e,function(e){return!!a.matchNode(e,n)});return-1!==sp(t,!0)}};e({JustifyLeft:p("alignleft"),JustifyCenter:p("aligncenter"),JustifyRight:p("alignright"),JustifyFull:p("alignjustify"),"Bold,Italic,Underline,Strikethrough,Superscript,Subscript":function(e){return f(e)},mceBlockQuote:function(){return f("blockquote")},Outdent:function(){var e;if(n.inline_styles){if((e=o.getParent(i.getStart(),o.isBlock))&&0<parseInt(e.style.paddingLeft,10))return!0;if((e=o.getParent(i.getEnd(),o.isBlock))&&0<parseInt(e.style.paddingLeft,10))return!0}return r("InsertUnorderedList")||r("InsertOrderedList")||!n.inline_styles&&!!o.getParent(i.getNode(),"BLOCKQUOTE")},"InsertUnorderedList,InsertOrderedList":function(e){var t=o.getParent(i.getNode(),"ul,ol");return t&&("insertunorderedlist"===e&&"UL"===t.tagName||"insertorderedlist"===e&&"OL"===t.tagName)}},"state"),e({Undo:function(){s.undoManager.undo()},Redo:function(){s.undoManager.redo()}}),u("FontName",function(){return yg(t=s).fold(function(){return bg(t).map(function(e){return vg.getFontFamily(t.getBody(),e)}).getOr("")},function(e){return vg.getFontFamily(t.getBody(),e)});var t},this),u("FontSize",function(){return yg(t=s).fold(function(){return bg(t).map(function(e){return vg.getFontSize(t.getBody(),e)}).getOr("")},function(e){return vg.getFontSize(t.getBody(),e)});var t},this)}var lp=Yt.makeMap("focus blur focusin focusout click dblclick mousedown mouseup mousemove mouseover beforepaste paste cut copy selectionchange mouseout mouseenter mouseleave wheel keydown keypress keyup input contextmenu dragstart dragend dragover draggesture dragdrop drop drag submit compositionstart compositionend compositionupdate touchstart touchmove touchend"," "),fp=function(a){var u,s,c=this,l={},f=function(){return!1},d=function(){return!0};u=(a=a||{}).scope||c,s=a.toggleEvent||f;var r=function(e,t,n,r){var o,i,a;if(!1===t&&(t=f),t)for(t={func:t},r&&Yt.extend(t,r),a=(i=e.toLowerCase().split(" ")).length;a--;)e=i[a],(o=l[e])||(o=l[e]=[],s(e,!0)),n?o.unshift(t):o.push(t);return c},m=function(e,t){var n,r,o,i,a;if(e)for(n=(i=e.toLowerCase().split(" ")).length;n--;){if(e=i[n],r=l[e],!e){for(o in l)s(o,!1),delete l[o];return c}if(r){if(t)for(a=r.length;a--;)r[a].func===t&&(r=r.slice(0,a).concat(r.slice(a+1)),l[e]=r);else r.length=0;r.length||(s(e,!1),delete l[e])}}else{for(e in l)s(e,!1);l={}}return c};c.fire=function(e,t){var n,r,o,i;if(e=e.toLowerCase(),(t=t||{}).type=e,t.target||(t.target=u),t.preventDefault||(t.preventDefault=function(){t.isDefaultPrevented=d},t.stopPropagation=function(){t.isPropagationStopped=d},t.stopImmediatePropagation=function(){t.isImmediatePropagationStopped=d},t.isDefaultPrevented=f,t.isPropagationStopped=f,t.isImmediatePropagationStopped=f),a.beforeFire&&a.beforeFire(t),n=l[e])for(r=0,o=n.length;r<o;r++){if((i=n[r]).once&&m(e,i.func),t.isImmediatePropagationStopped())return t.stopPropagation(),t;if(!1===i.func.call(u,t))return t.preventDefault(),t}return t},c.on=r,c.off=m,c.once=function(e,t,n){return r(e,t,n,{once:!0})},c.has=function(e){return e=e.toLowerCase(),!(!l[e]||0===l[e].length)}};fp.isNative=function(e){return!!lp[e.toLowerCase()]};var dp,mp=function(n){return n._eventDispatcher||(n._eventDispatcher=new fp({scope:n,toggleEvent:function(e,t){fp.isNative(e)&&n.toggleNativeEvent&&n.toggleNativeEvent(e,t)}})),n._eventDispatcher},gp={fire:function(e,t,n){if(this.removed&&"remove"!==e)return t;if(t=mp(this).fire(e,t,n),!1!==n&&this.parent)for(var r=this.parent();r&&!t.isPropagationStopped();)r.fire(e,t,!1),r=r.parent();return t},on:function(e,t,n){return mp(this).on(e,t,n)},off:function(e,t){return mp(this).off(e,t)},once:function(e,t){return mp(this).once(e,t)},hasEventListeners:function(e){return mp(this).has(e)}},pp=function(e,t){return e.fire("PreProcess",t)},hp=function(e,t){return e.fire("PostProcess",t)},vp=function(e){return e.fire("remove")},bp=function(e,t){return e.fire("SwitchMode",{mode:t})},yp=function(e,t,n,r){e.fire("ObjectResizeStart",{target:t,width:n,height:r})},Cp=function(e,t,n,r){e.fire("ObjectResized",{target:t,width:n,height:r})},xp=function(e,t,n){try{e.getDoc().execCommand(t,!1,n)}catch(r){}},wp=function(e,t,n){var r,o;Ui(e,t)&&!1===n?(o=t,Li(r=e)?r.dom().classList.remove(o):Mi(r,o),zi(r)):n&&Fi(e,t)},Np=function(e,t){wp(rr.fromDom(e.getBody()),"mce-content-readonly",t),t?(e.selection.controlSelection.hideResizeRect(),e.readonly=!0,e.getBody().contentEditable="false"):(e.readonly=!1,e.getBody().contentEditable="true",xp(e,"StyleWithCSS",!1),xp(e,"enableInlineTableEditing",!1),xp(e,"enableObjectResizing",!1),e.focus(),e.nodeChanged())},Ep=function(e){return e.readonly?"readonly":"design"},Sp=pi.DOM,kp=function(e,t){return"selectionchange"===t?e.getDoc():!e.inline&&/^mouse|touch|click|contextmenu|drop|dragover|dragend/.test(t)?e.getDoc().documentElement:e.settings.event_root?(e.eventRoot||(e.eventRoot=Sp.select(e.settings.event_root)[0]),e.eventRoot):e.getBody()},Tp=function(e,t,n){var r;(r=e).hidden||r.readonly?!0===e.readonly&&n.preventDefault():e.fire(t,n)},Ap=function(i,a){var e,t;if(i.delegates||(i.delegates={}),!i.delegates[a]&&!i.removed)if(e=kp(i,a),i.settings.event_root){if(dp||(dp={},i.editorManager.on("removeEditor",function(){var e;if(!i.editorManager.activeEditor&&dp){for(e in dp)i.dom.unbind(kp(i,e));dp=null}})),dp[a])return;t=function(e){for(var t=e.target,n=i.editorManager.get(),r=n.length;r--;){var o=n[r].getBody();(o===t||Sp.isChildOf(t,o))&&Tp(n[r],a,e)}},dp[a]=t,Sp.bind(e,a,t)}else t=function(e){Tp(i,a,e)},Sp.bind(e,a,t),i.delegates[a]=t},Rp={bindPendingEventDelegates:function(){var t=this;Yt.each(t._pendingNativeEvents,function(e){Ap(t,e)})},toggleNativeEvent:function(e,t){var n=this;"focus"!==e&&"blur"!==e&&(t?n.initialized?Ap(n,e):n._pendingNativeEvents?n._pendingNativeEvents.push(e):n._pendingNativeEvents=[e]:n.initialized&&(n.dom.unbind(kp(n,e),e,n.delegates[e]),delete n.delegates[e]))},unbindAllNativeEvents:function(){var e,t=this,n=t.getBody(),r=t.dom;if(t.delegates){for(e in t.delegates)t.dom.unbind(kp(t,e),e,t.delegates[e]);delete t.delegates}!t.inline&&n&&r&&(n.onload=null,r.unbind(t.getWin()),r.unbind(t.getDoc())),r&&(r.unbind(n),r.unbind(t.getContainer()))}},_p=Rp=Yt.extend({},gp,Rp),Dp=Yt.each,Bp=Yt.explode,Op={f1:112,f2:113,f3:114,f4:115,f5:116,f6:117,f7:118,f8:119,f9:120,f10:121,f11:122,f12:123},Pp=Yt.makeMap("alt,ctrl,shift,meta,access");function Lp(i){var a={},r=[],u=function(e){var t,n,r={};for(n in Dp(Bp(e,"+"),function(e){e in Pp?r[e]=!0:/^[0-9]{2,}$/.test(e)?r.keyCode=parseInt(e,10):(r.charCode=e.charCodeAt(0),r.keyCode=Op[e]||e.toUpperCase().charCodeAt(0))}),t=[r.keyCode],Pp)r[n]?t.push(n):r[n]=!1;return r.id=t.join(","),r.access&&(r.alt=!0,de.mac?r.ctrl=!0:r.shift=!0),r.meta&&(de.mac?r.meta=!0:(r.ctrl=!0,r.meta=!1)),r},s=function(e,t,n,r){var o;return(o=Yt.map(Bp(e,">"),u))[o.length-1]=Yt.extend(o[o.length-1],{func:n,scope:r||i}),Yt.extend(o[0],{desc:i.translate(t),subpatterns:o.slice(1)})},o=function(e,t){return!!t&&t.ctrl===e.ctrlKey&&t.meta===e.metaKey&&t.alt===e.altKey&&t.shift===e.shiftKey&&!!(e.keyCode===t.keyCode||e.charCode&&e.charCode===t.charCode)&&(e.preventDefault(),!0)},c=function(e){return e.func?e.func.call(e.scope):null};i.on("keyup keypress keydown",function(t){var e,n;((n=t).altKey||n.ctrlKey||n.metaKey||"keydown"===(e=t).type&&112<=e.keyCode&&e.keyCode<=123)&&!t.isDefaultPrevented()&&(Dp(a,function(e){if(o(t,e))return r=e.subpatterns.slice(0),"keydown"===t.type&&c(e),!0}),o(t,r[0])&&(1===r.length&&"keydown"===t.type&&c(r[0]),r.shift()))}),this.add=function(e,n,r,o){var t;return"string"==typeof(t=r)?r=function(){i.execCommand(t,!1,null)}:Yt.isArray(t)&&(r=function(){i.execCommand(t[0],t[1],t[2])}),Dp(Bp(Yt.trim(e.toLowerCase())),function(e){var t=s(e,n,r,o);a[t.id]=t}),!0},this.remove=function(e){var t=s(e);return!!a[t.id]&&(delete a[t.id],!0)}}var Ip=function(e){var t=Lr(e).dom();return e.dom()===t.activeElement},Mp=function(t){return(e=Lr(t),n=e!==undefined?e.dom():document,A.from(n.activeElement).map(rr.fromDom)).filter(function(e){return t.dom().contains(e.dom())});var e,n},Fp=function(t,e){return(n=e,n.collapsed?A.from(Ha(n.startContainer,n.startOffset)).map(rr.fromDom):A.none()).bind(function(e){return vo(e)?A.some(e):!1===Pr(t,e)?A.some(t):A.none()});var n},zp=function(t,e){Fp(rr.fromDom(t.getBody()),e).bind(function(e){return cc.firstPositionIn(e.dom())}).fold(function(){t.selection.normalize()},function(e){return t.selection.setRng(e.toRange())})},Up=function(e){if(e.setActive)try{e.setActive()}catch(t){e.focus()}else e.focus()},Vp=function(e){var t,n=e.getBody();return n&&(t=rr.fromDom(n),Ip(t)||Mp(t).isSome())},Hp=function(e){return e.inline?Vp(e):(t=e).iframeElement&&Ip(rr.fromDom(t.iframeElement));var t},jp=function(e){return e.editorManager.setActive(e)},qp=function(e,t){e.removed||(t?jp(e):function(t){var e=t.selection,n=t.settings.content_editable,r=t.getBody(),o=e.getRng();t.quirks.refreshContentEditable();var i,a,u=(i=t,a=e.getNode(),i.dom.getParent(a,function(e){return"true"===i.dom.getContentEditable(e)}));if(t.$.contains(r,u))return Up(u),zp(t,o),jp(t);t.bookmark!==undefined&&!1===Hp(t)&&tp(t).each(function(e){t.selection.setRng(e),o=e}),n||(de.opera||Up(r),t.getWin().focus()),(de.gecko||n)&&(Up(r),zp(t,o)),jp(t)}(e))},$p=Hp,Wp=function(e,t){return t.dom()[e]},Kp=function(e,t){return parseInt(wr(t,e),10)},Xp=d(Wp,"clientWidth"),Yp=d(Wp,"clientHeight"),Gp=d(Kp,"margin-top"),Jp=d(Kp,"margin-left"),Qp=function(e,t,n){var r,o,i,a,u,s,c,l,f,d,m,g=rr.fromDom(e.getBody()),p=e.inline?g:(r=g,rr.fromDom(r.dom().ownerDocument.documentElement)),h=(o=e.inline,a=t,u=n,s=(i=p).dom().getBoundingClientRect(),{x:a-(o?s.left+i.dom().clientLeft+Jp(i):0),y:u-(o?s.top+i.dom().clientTop+Gp(i):0)});return l=h.x,f=h.y,d=Xp(c=p),m=Yp(c),0<=l&&0<=f&&l<=d&&f<=m},Zp=function(e){var t,n=e.inline?e.getBody():e.getContentAreaContainer();return(t=n,A.from(t).map(rr.fromDom)).map(function(e){return Pr(Lr(e),e)}).getOr(!1)};function eh(n){var t,o=[],i=function(){var e,t=n.theme;return t&&t.getNotificationManagerImpl?t.getNotificationManagerImpl():{open:e=function(){throw new Error("Theme did not provide a NotificationManager implementation.")},close:e,reposition:e,getArgs:e}},a=function(){0<o.length&&i().reposition(o)},u=function(t){K(o,function(e){return e===t}).each(function(e){o.splice(e,1)})},r=function(r){if(!n.removed&&Zp(n))return V(o,function(e){return t=i().getArgs(e),n=r,!(t.type!==n.type||t.text!==n.text||t.progressBar||t.timeout||n.progressBar||n.timeout);var t,n}).getOrThunk(function(){n.editorManager.setActive(n);var e,t=i().open(r,function(){u(t),a()});return e=t,o.push(e),a(),t})};return(t=n).on("SkinLoaded",function(){var e=t.settings.service_message;e&&r({text:e,type:"warning",timeout:0,icon:""})}),t.on("ResizeEditor ResizeWindow",function(){ve.requestAnimationFrame(a)}),t.on("remove",function(){F(o.slice(),function(e){i().close(e)})}),{open:r,close:function(){A.from(o[0]).each(function(e){i().close(e),u(e),a()})},getNotifications:function(){return o}}}function th(r){var o=[],i=function(){var e,t=r.theme;return t&&t.getWindowManagerImpl?t.getWindowManagerImpl():{open:e=function(){throw new Error("Theme did not provide a WindowManager implementation.")},alert:e,confirm:e,close:e,getParams:e,setParams:e}},a=function(e,t){return function(){return t?t.apply(e,arguments):undefined}},u=function(e){var t;o.push(e),t=e,r.fire("OpenWindow",{win:t})},s=function(n){K(o,function(e){return e===n}).each(function(e){var t;o.splice(e,1),t=n,r.fire("CloseWindow",{win:t}),0===o.length&&r.focus()})},e=function(){return A.from(o[o.length-1])};return r.on("remove",function(){F(o.slice(0),function(e){i().close(e)})}),{windows:o,open:function(e,t){r.editorManager.setActive(r),Zg(r);var n=i().open(e,t,s);return u(n),n},alert:function(e,t,n){var r=i().alert(e,a(n||this,t),s);u(r)},confirm:function(e,t,n){var r=i().confirm(e,a(n||this,t),s);u(r)},close:function(){e().each(function(e){i().close(e),s(e)})},getParams:function(){return e().map(i().getParams).getOr(null)},setParams:function(t){e().each(function(e){i().setParams(e,t)})},getWindows:function(){return o}}}var nh={},rh="en",oh={setCode:function(e){e&&(rh=e,this.rtl=!!this.data[e]&&"rtl"===this.data[e]._dir)},getCode:function(){return rh},rtl:!1,add:function(e,t){var n=nh[e];for(var r in n||(nh[e]=n={}),t)n[r]=t[r];this.setCode(e)},translate:function(e){var t=nh[rh]||{},n=function(e){return Yt.is(e,"function")?Object.prototype.toString.call(e):r(e)?"":""+e},r=function(e){return""===e||null===e||Yt.is(e,"undefined")},o=function(e){return e=n(e),Yt.hasOwn(t,e)?n(t[e]):e};if(r(e))return"";if(Yt.is(e,"object")&&Yt.hasOwn(e,"raw"))return n(e.raw);if(Yt.is(e,"array")){var i=e.slice(1);e=o(e[0]).replace(/\{([0-9]+)\}/g,function(e,t){return Yt.hasOwn(i,t)?n(i[t]):e})}return o(e).replace(/{context:\w+}$/,"")},data:nh},ih=Ni.PluginManager,ah=function(e,t){var n=function(e,t){for(var n in ih.urls)if(ih.urls[n]+"/plugin"+t+".js"===e)return n;return null}(t,e.suffix);return n?oh.translate(["Failed to load plugin: {0} from url {1}",n,t]):oh.translate(["Failed to load plugin url: {0}",t])},uh=function(e,t){e.notificationManager.open({type:"error",text:t})},sh=function(e,t){e._skinLoaded?uh(e,t):e.on("SkinLoaded",function(){uh(e,t)})},ch=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var r=window.console;r&&(r.error?r.error.apply(r,arguments):r.log.apply(r,arguments))},lh={pluginLoadError:function(e,t){sh(e,ah(e,t))},pluginInitError:function(e,t,n){var r=oh.translate(["Failed to initialize plugin: {0}",t]);ch(r,n),sh(e,r)},uploadError:function(e,t){sh(e,oh.translate(["Failed to upload image: {0}",t]))},displayError:sh,initError:ch},fh=Ni.PluginManager,dh=Ni.ThemeManager;function mh(){return new(ie.getOrDie("XMLHttpRequest"))}function gh(u,s){var r={},n=function(e,r,o,t){var i,n;(i=mh()).open("POST",s.url),i.withCredentials=s.credentials,i.upload.onprogress=function(e){t(e.loaded/e.total*100)},i.onerror=function(){o("Image upload failed due to a XHR Transport error. Code: "+i.status)},i.onload=function(){var e,t,n;i.status<200||300<=i.status?o("HTTP Error: "+i.status):(e=JSON.parse(i.responseText))&&"string"==typeof e.location?r((t=s.basePath,n=e.location,t?t.replace(/\/$/,"")+"/"+n.replace(/^\//,""):n)):o("Invalid JSON: "+i.responseText)},(n=new FormData).append("file",e.blob(),e.filename()),i.send(n)},c=function(e,t){return{url:t,blobInfo:e,status:!0}},l=function(e,t){return{url:"",blobInfo:e,status:!1,error:t}},f=function(e,t){Yt.each(r[e],function(e){e(t)}),delete r[e]},o=function(e,n){return e=Yt.grep(e,function(e){return!u.isUploaded(e.blobUri())}),me.all(Yt.map(e,function(e){return u.isPending(e.blobUri())?(t=e.blobUri(),new me(function(e){r[t]=r[t]||[],r[t].push(e)})):(o=e,i=s.handler,a=n,u.markPending(o.blobUri()),new me(function(t){var n;try{var r=function(){n&&n.close()};i(o,function(e){r(),u.markUploaded(o.blobUri(),e),f(o.blobUri(),c(o,e)),t(c(o,e))},function(e){r(),u.removeFailed(o.blobUri()),f(o.blobUri(),l(o,e)),t(l(o,e))},function(e){e<0||100<e||(n||(n=a()),n.progressBar.value(e))})}catch(e){t(l(o,e.message))}}));var o,i,a,t}))};return!1===P(s.handler)&&(s.handler=n),{upload:function(e,t){return s.url||s.handler!==n?o(e,t):new me(function(e){e([])})}}}var ph=function(e){return ie.getOrDie("atob")(e)},hh=function(e){var t,n,r=decodeURIComponent(e).split(",");return(n=/data:([^;]+)/.exec(r[0]))&&(t=n[1]),{type:t,data:r[1]}},vh=function(a){return new me(function(e){var t,n,r,o,i=hh(a);try{t=ph(i.data)}catch(VN){return void e(new Blob([]))}for(o=t.length,n=new(ie.getOrDie("Uint8Array"))(o),r=0;r<n.length;r++)n[r]=t.charCodeAt(r);e(new Blob([n],{type:i.type}))})},bh=function(e){return 0===e.indexOf("blob:")?(i=e,new me(function(e,t){var n=function(){t("Cannot convert "+i+" to Blob. Resource might not exist or is inaccessible.")};try{var r=mh();r.open("GET",i,!0),r.responseType="blob",r.onload=function(){200===this.status?e(this.response):n()},r.onerror=n,r.send()}catch(o){n()}})):0===e.indexOf("data:")?vh(e):null;var i},yh=function(n){return new me(function(e){var t=new(ie.getOrDie("FileReader"));t.onloadend=function(){e(t.result)},t.readAsDataURL(n)})},Ch=hh,xh=0,wh=function(e){return(e||"blobid")+xh++},Nh=function(n,r,o,t){var i,a;0!==r.src.indexOf("blob:")?(i=Ch(r.src).data,(a=n.findFirst(function(e){return e.base64()===i}))?o({image:r,blobInfo:a}):bh(r.src).then(function(e){a=n.create(wh(),e,i),n.add(a),o({image:r,blobInfo:a})},function(e){t(e)})):(a=n.getByUri(r.src))?o({image:r,blobInfo:a}):bh(r.src).then(function(t){yh(t).then(function(e){i=Ch(e).data,a=n.create(wh(),t,i),n.add(a),o({image:r,blobInfo:a})})},function(e){t(e)})},Eh=function(e){return e?ne(e.getElementsByTagName("img")):[]},Sh=0,kh={uuid:function(e){return e+Sh+++(t=function(){return Math.round(4294967295*Math.random()).toString(36)},"s"+(new Date).getTime().toString(36)+t()+t()+t());var t}};function Th(u){var n,o,t,e,i,r,a,s,c,l=(n=[],o=function(e){var t,n,r;if(!e.blob||!e.base64)throw new Error("blob and base64 representations of the image are required for BlobInfo to be created");return t=e.id||kh.uuid("blobid"),n=e.name||t,{id:j(t),name:j(n),filename:j(n+"."+(r=e.blob.type,{"image/jpeg":"jpg","image/jpg":"jpg","image/gif":"gif","image/png":"png"}[r.toLowerCase()]||"dat")),blob:j(e.blob),base64:j(e.base64),blobUri:j(e.blobUri||ue.createObjectURL(e.blob)),uri:j(e.uri)}},{create:function(e,t,n,r){if(R(e))return o({id:e,name:r,blob:t,base64:n});if(_(e))return o(e);throw new Error("Unknown input type")},add:function(e){t(e.id())||n.push(e)},get:t=function(t){return e(function(e){return e.id()===t})},getByUri:function(t){return e(function(e){return e.blobUri()===t})},findFirst:e=function(e){return z(n,e)[0]},removeByUri:function(t){n=z(n,function(e){return e.blobUri()!==t||(ue.revokeObjectURL(e.blobUri()),!1)})},destroy:function(){F(n,function(e){ue.revokeObjectURL(e.blobUri())}),n=[]}}),f=(a={},s=function(e,t){return{status:e,resultUri:t}},{hasBlobUri:c=function(e){return e in a},getResultUri:function(e){var t=a[e];return t?t.resultUri:null},isPending:function(e){return!!c(e)&&1===a[e].status},isUploaded:function(e){return!!c(e)&&2===a[e].status},markPending:function(e){a[e]=s(1,null)},markUploaded:function(e,t){a[e]=s(2,t)},removeFailed:function(e){delete a[e]},destroy:function(){a={}}}),d=[],m=function(t){return function(e){return u.selection?t(e):[]}},g=function(e,t,n){for(var r=0;-1!==(r=e.indexOf(t,r))&&(e=e.substring(0,r)+n+e.substr(r+t.length),r+=n.length-t.length+1),-1!==r;);return e},p=function(e,t,n){return e=g(e,'src="'+t+'"','src="'+n+'"'),e=g(e,'data-mce-src="'+t+'"','data-mce-src="'+n+'"')},h=function(t,n){F(u.undoManager.data,function(e){"fragmented"===e.type?e.fragments=$(e.fragments,function(e){return p(e,t,n)}):e.content=p(e.content,t,n)})},v=function(){return u.notificationManager.open({text:u.translate("Image uploading..."),type:"info",timeout:-1,progressBar:!0})},b=function(e,t){l.removeByUri(e.src),h(e.src,t),u.$(e).attr({src:sg(u)?t+"?"+(new Date).getTime():t,"data-mce-src":u.convertURL(t,"src")})},y=function(n){return i||(i=gh(f,{url:lg(u),basePath:fg(u),credentials:dg(u),handler:mg(u)})),w().then(m(function(r){var e;return e=$(r,function(e){return e.blobInfo}),i.upload(e,v).then(m(function(e){var t=$(e,function(e,t){var n=r[t].image;return e.status&&cg(u)?b(n,e.url):e.error&&lh.uploadError(u,e.error),{element:n,status:e.status}});return n&&n(t),t}))}))},C=function(e){if(ug(u))return y(e)},x=function(t){return!1!==J(d,function(e){return e(t)})&&(0!==t.getAttribute("src").indexOf("data:")||ag(u)(t))},w=function(){var o,i,a;return r||(o=f,i=l,a={},r={findAll:function(e,n){var t;n||(n=j(!0)),t=z(Eh(e),function(e){var t=e.src;return!!de.fileApi&&!e.hasAttribute("data-mce-bogus")&&!e.hasAttribute("data-mce-placeholder")&&!(!t||t===de.transparentSrc)&&(0===t.indexOf("blob:")?!o.isUploaded(t)&&n(e):0===t.indexOf("data:")&&n(e))});var r=$(t,function(n){if(a[n.src])return new me(function(t){a[n.src].then(function(e){if("string"==typeof e)return e;t({image:n,blobInfo:e.blobInfo})})});var e=new me(function(e,t){Nh(i,n,e,t)}).then(function(e){return delete a[e.image.src],e})["catch"](function(e){return delete a[n.src],e});return a[n.src]=e});return me.all(r)}}),r.findAll(u.getBody(),x).then(m(function(e){return e=z(e,function(e){return"string"!=typeof e||(lh.displayError(u,e),!1)}),F(e,function(e){h(e.image.src,e.blobInfo.blobUri()),e.image.src=e.blobInfo.blobUri(),e.image.removeAttribute("data-mce-src")}),e}))},N=function(e){return e.replace(/src="(blob:[^"]+)"/g,function(e,n){var t=f.getResultUri(n);if(t)return'src="'+t+'"';var r=l.getByUri(n);return r||(r=U(u.editorManager.get(),function(e,t){return e||t.editorUpload&&t.editorUpload.blobCache.getByUri(n)},null)),r?'src="data:'+r.blob().type+";base64,"+r.base64()+'"':e})};return u.on("setContent",function(){ug(u)?C():w()}),u.on("RawSaveContent",function(e){e.content=N(e.content)}),u.on("getContent",function(e){e.source_view||"raw"===e.format||(e.content=N(e.content))}),u.on("PostRender",function(){u.parser.addNodeFilter("img",function(e){F(e,function(e){var t=e.attr("src");if(!l.getByUri(t)){var n=f.getResultUri(t);n&&e.attr("src",n)}})})}),{blobCache:l,addFilter:function(e){d.push(e)},uploadImages:y,uploadImagesAuto:C,scanForImages:w,destroy:function(){l.destroy(),f.destroy(),r=i=null}}}var Ah=function(e,t){return e.hasOwnProperty(t.nodeName)},Rh=function(t,e,n){return r=$l(rr.fromDom(n),rr.fromDom(e)),K(r,function(e){return Ah(t,e.dom())}).isSome();var r},_h=function(e,t){if(Do.isText(t)){if(0===t.nodeValue.length)return!0;if(/^\s+$/.test(t.nodeValue)&&(!t.nextSibling||Ah(e,t.nextSibling)))return!0}return!1},Dh=function(e){var t,n,r,o,i,a,u,s,c,l,f,d=e.settings,m=e.dom,g=e.selection,p=e.schema,h=p.getBlockElements(),v=g.getStart(),b=e.getBody();if(f=d.forced_root_block,v&&Do.isElement(v)&&f&&(l=b.nodeName.toLowerCase(),p.isValidChild(l,f.toLowerCase())&&!Rh(h,b,v))){for(n=(t=g.getRng()).startContainer,r=t.startOffset,o=t.endContainer,i=t.endOffset,c=$p(e),v=b.firstChild;v;)if(y=h,C=v,Do.isText(C)||Do.isElement(C)&&!Ah(y,C)&&!yc(C)){if(_h(h,v)){v=(u=v).nextSibling,m.remove(u);continue}a||(a=m.create(f,e.settings.forced_root_block_attrs),v.parentNode.insertBefore(a,v),s=!0),v=(u=v).nextSibling,a.appendChild(u)}else a=null,v=v.nextSibling;var y,C;s&&c&&(t.setStart(n,r),t.setEnd(o,i),g.setRng(t),e.nodeChanged())}},Bh=function(e){e.settings.forced_root_block&&e.on("NodeChange",d(Dh,e))},Oh=function(t){return jr(t).fold(j([t]),function(e){return[t].concat(Oh(e))})},Ph=function(t){return qr(t).fold(j([t]),function(e){return"br"===ur(e)?Mr(e).map(function(e){return[t].concat(Ph(e))}).getOr([]):[t].concat(Ph(e))})},Lh=function(o,e){return $a([(i=e,a=i.startContainer,u=i.startOffset,Do.isText(a)?0===u?A.some(rr.fromDom(a)):A.none():A.from(a.childNodes[u]).map(rr.fromDom)),(t=e,n=t.endContainer,r=t.endOffset,Do.isText(n)?r===n.data.length?A.some(rr.fromDom(n)):A.none():A.from(n.childNodes[r-1]).map(rr.fromDom))],function(e,t){var n=V(Oh(o),d(Or,e)),r=V(Ph(o),d(Or,t));return n.isSome()&&r.isSome()}).getOr(!1);var t,n,r,i,a,u},Ih=function(e,t,n,r){var o=n,i=new ro(n,o),a=e.schema.getNonEmptyElements();do{if(3===n.nodeType&&0!==Yt.trim(n.nodeValue).length)return void(r?t.setStart(n,0):t.setEnd(n,n.nodeValue.length));if(a[n.nodeName]&&!/^(TD|TH)$/.test(n.nodeName))return void(r?t.setStartBefore(n):"BR"===n.nodeName?t.setEndBefore(n):t.setEndAfter(n));if(de.ie&&de.ie<11&&e.isBlock(n)&&e.isEmpty(n))return void(r?t.setStart(n,0):t.setEnd(n,0))}while(n=r?i.next():i.prev());"BODY"===o.nodeName&&(r?t.setStart(o,0):t.setEnd(o,o.childNodes.length))},Mh=function(e){var t=e.selection.getSel();return t&&0<t.rangeCount};function Fh(i){var r,o=[];"onselectionchange"in i.getDoc()||i.on("NodeChange Click MouseUp KeyUp Focus",function(e){var t,n;n={startContainer:(t=i.selection.getRng()).startContainer,startOffset:t.startOffset,endContainer:t.endContainer,endOffset:t.endOffset},"nodechange"!==e.type&&xg(n,r)||i.fire("SelectionChange"),r=n}),i.on("contextmenu",function(){i.fire("SelectionChange")}),i.on("SelectionChange",function(){var e=i.selection.getStart(!0);!e||!de.range&&i.selection.isCollapsed()||Mh(i)&&!function(e){var t,n;if((n=i.$(e).parentsUntil(i.getBody()).add(e)).length===o.length){for(t=n.length;0<=t&&n[t]===o[t];t--);if(-1===t)return o=n,!0}return o=n,!1}(e)&&i.dom.isChildOf(e,i.getBody())&&i.nodeChanged({selectionChange:!0})}),i.on("MouseUp",function(e){!e.isDefaultPrevented()&&Mh(i)&&("IMG"===i.selection.getNode().nodeName?ve.setEditorTimeout(i,function(){i.nodeChanged()}):i.nodeChanged())}),this.nodeChanged=function(e){var t,n,r,o=i.selection;i.initialized&&o&&!i.settings.disable_nodechange&&!i.readonly&&(r=i.getBody(),(t=o.getStart(!0)||r).ownerDocument===i.getDoc()&&i.dom.isChildOf(t,r)||(t=r),n=[],i.dom.getParent(t,function(e){if(e===r)return!0;n.push(e)}),(e=e||{}).element=t,e.parents=n,i.fire("NodeChange",e))}}var zh,Uh,Vh=function(e){var t,n,r,o;return o=e.getBoundingClientRect(),n=(t=e.ownerDocument).documentElement,r=t.defaultView,{top:o.top+r.pageYOffset-n.clientTop,left:o.left+r.pageXOffset-n.clientLeft}},Hh=function(e,t){return n=(u=e).inline?Vh(u.getBody()):{left:0,top:0},a=(i=e).getBody(),r=i.inline?{left:a.scrollLeft,top:a.scrollTop}:{left:0,top:0},{pageX:(o=function(e,t){if(t.target.ownerDocument!==e.getDoc()){var n=Vh(e.getContentAreaContainer()),r=(i=(o=e).getBody(),a=o.getDoc().documentElement,u={left:i.scrollLeft,top:i.scrollTop},s={left:i.scrollLeft||a.scrollLeft,top:i.scrollTop||a.scrollTop},o.inline?u:s);return{left:t.pageX-n.left+r.left,top:t.pageY-n.top+r.top}}var o,i,a,u,s;return{left:t.pageX,top:t.pageY}}(e,t)).left-n.left+r.left,pageY:o.top-n.top+r.top};var n,r,o,i,a,u},jh=Do.isContentEditableFalse,qh=Do.isContentEditableTrue,$h=function(e){e&&e.parentNode&&e.parentNode.removeChild(e)},Wh=function(u,s){return function(e){if(0===e.button){var t=V(s.dom.getParents(e.target),Xa(jh,qh)).getOr(null);if(i=s.getBody(),jh(a=t)&&a!==i){var n=s.dom.getPos(t),r=s.getBody(),o=s.getDoc().documentElement;u.element=t,u.screenX=e.screenX,u.screenY=e.screenY,u.maxX=(s.inline?r.scrollWidth:o.offsetWidth)-2,u.maxY=(s.inline?r.scrollHeight:o.offsetHeight)-2,u.relX=e.pageX-n.x,u.relY=e.pageY-n.y,u.width=t.offsetWidth,u.height=t.offsetHeight,u.ghost=function(e,t,n,r){var o=t.cloneNode(!0);e.dom.setStyles(o,{width:n,height:r}),e.dom.setAttrib(o,"data-mce-selected",null);var i=e.dom.create("div",{"class":"mce-drag-container","data-mce-bogus":"all",unselectable:"on",contenteditable:"false"});return e.dom.setStyles(i,{position:"absolute",opacity:.5,overflow:"hidden",border:0,padding:0,margin:0,width:n,height:r}),e.dom.setStyles(o,{margin:0,boxSizing:"border-box"}),i.appendChild(o),i}(s,t,u.width,u.height)}}var i,a}},Kh=function(l,f){return function(e){if(l.dragging&&(s=(i=f).selection,c=s.getSel().getRangeAt(0).startContainer,a=3===c.nodeType?c.parentNode:c,u=l.element,a!==u&&!i.dom.isChildOf(a,u)&&!jh(a))){var t=(r=l.element,(o=r.cloneNode(!0)).removeAttribute("data-mce-selected"),o),n=f.fire("drop",{targetClone:t,clientX:e.clientX,clientY:e.clientY});n.isDefaultPrevented()||(t=n.targetClone,f.undoManager.transact(function(){$h(l.element),f.insertContent(f.dom.getOuterHTML(t)),f._selectionOverrides.hideFakeCaret()}))}var r,o,i,a,u,s,c;Xh(l)}},Xh=function(e){e.dragging=!1,e.element=null,$h(e.ghost)},Yh=function(e){var t,n,r,o,i,a,p,h,v,u,s,c={};t=pi.DOM,a=document,n=Wh(c,e),p=c,h=e,v=ve.throttle(function(e,t){h._selectionOverrides.hideFakeCaret(),h.selection.placeCaretAt(e,t)},0),r=function(e){var t,n,r,o,i,a,u,s,c,l,f,d,m=Math.max(Math.abs(e.screenX-p.screenX),Math.abs(e.screenY-p.screenY));if(p.element&&!p.dragging&&10<m){if(h.fire("dragstart",{target:p.element}).isDefaultPrevented())return;p.dragging=!0,h.focus()}if(p.dragging){var g=(f=p,{pageX:(d=Hh(h,e)).pageX-f.relX,pageY:d.pageY+5});c=p.ghost,l=h.getBody(),c.parentNode!==l&&l.appendChild(c),t=p.ghost,n=g,r=p.width,o=p.height,i=p.maxX,a=p.maxY,s=u=0,t.style.left=n.pageX+"px",t.style.top=n.pageY+"px",n.pageX+r>i&&(u=n.pageX+r-i),n.pageY+o>a&&(s=n.pageY+o-a),t.style.width=r-u+"px",t.style.height=o-s+"px",v(e.clientX,e.clientY)}},o=Kh(c,e),u=c,i=function(){u.dragging&&s.fire("dragend"),Xh(u)},(s=e).on("mousedown",n),e.on("mousemove",r),e.on("mouseup",o),t.bind(a,"mousemove",r),t.bind(a,"mouseup",i),e.on("remove",function(){t.unbind(a,"mousemove",r),t.unbind(a,"mouseup",i)})},Gh=function(e){var n;Yh(e),(n=e).on("drop",function(e){var t="undefined"!=typeof e.clientX?n.getDoc().elementFromPoint(e.clientX,e.clientY):null;(jh(t)||jh(n.dom.getContentEditableParent(t)))&&e.preventDefault()})},Jh=function(e){return U(e,function(e,t){return e.concat(function(t){var e=function(e){return $(e,function(e){return(e=Ia(e)).node=t,e})};if(Do.isElement(t))return e(t.getClientRects());if(Do.isText(t)){var n=t.ownerDocument.createRange();return n.setStart(t,0),n.setEnd(t,t.data.length),e(n.getClientRects())}}(t))},[])};(Uh=zh||(zh={}))[Uh.Up=-1]="Up",Uh[Uh.Down=1]="Down";var Qh=function(o,i,a,e,u,t){var n,s,c=0,l=[],r=function(e){var t,n,r;for(r=Jh([e]),-1===o&&(r=r.reverse()),t=0;t<r.length;t++)if(n=r[t],!a(n,s)){if(0<l.length&&i(n,qt.last(l))&&c++,n.line=c,u(n))return!0;l.push(n)}};return(s=qt.last(t.getClientRects()))&&(r(n=t.getNode()),function(e,t,n,r){for(;r=hs(r,e,Pa,t);)if(n(r))return}(o,e,r,n)),l},Zh=d(Qh,zh.Up,za,Ua),ev=d(Qh,zh.Down,Ua,za),tv=function(n){return function(e){return t=n,e.line>t;var t}},nv=function(n){return function(e){return t=n,e.line===t;var t}},rv=Do.isContentEditableFalse,ov=hs,iv=function(e,t){return Math.abs(e.left-t)},av=function(e,t){return Math.abs(e.right-t)},uv=function(e,t){return e>=t.left&&e<=t.right},sv=function(e,o){return qt.reduce(e,function(e,t){var n,r;return n=Math.min(iv(e,o),av(e,o)),r=Math.min(iv(t,o),av(t,o)),uv(o,t)?t:uv(o,e)?e:r===n&&rv(t.node)?t:r<n?t:e})},cv=function(e,t,n,r){for(;r=ov(r,e,Pa,t);)if(n(r))return},lv=function(e,t,n){var r,o,i,a,u,s,c,l=Jh(z(ne(e.getElementsByTagName("*")),rs)),f=z(l,function(e){return n>=e.top&&n<=e.bottom});return(r=sv(f,t))&&(r=sv((a=e,c=function(t,e){var n;return n=z(Jh([e]),function(e){return!t(e,u)}),s=s.concat(n),0===n.length},(s=[]).push(u=r),cv(zh.Up,a,d(c,za),u.node),cv(zh.Down,a,d(c,Ua),u.node),s),t))&&rs(r.node)?(i=t,{node:(o=r).node,before:iv(o,i)<av(o,i)}):null},fv=function(i,a,e){return!e.collapsed&&U(e.getClientRects(),function(e,t){return e||(o=a,(r=i)>=(n=t).left&&r<=n.right&&o>=n.top&&o<=n.bottom);var n,r,o},!1)},dv=function(t){var e=Bi(function(){if(!t.removed&&t.selection.getRng().collapsed){var e=Im(t,t.selection.getRng(),!1);t.selection.setRng(e)}},0);t.on("focus",function(){e.throttle()}),t.on("blur",function(){e.cancel()})},mv={BACKSPACE:8,DELETE:46,DOWN:40,ENTER:13,LEFT:37,RIGHT:39,SPACEBAR:32,TAB:9,UP:38,modifierPressed:function(e){return e.shiftKey||e.ctrlKey||e.altKey||this.metaKeyPressed(e)},metaKeyPressed:function(e){return de.mac?e.metaKey:e.ctrlKey&&!e.altKey}},gv=Do.isContentEditableTrue,pv=Do.isContentEditableFalse,hv=_s,vv=Rs,bv=function(e,t){for(var n=e.getBody();t&&t!==n;){if(gv(t)||pv(t))return t;t=t.parentNode}return null},yv=function(g){var p,e,t,a=g.getBody(),o=ts(g.getBody(),function(e){return g.dom.isBlock(e)},function(){return $p(g)}),h="sel-"+g.dom.uniqueId(),u=function(e){e&&g.selection.setRng(e)},s=function(){return g.selection.getRng()},v=function(e,t,n,r){return void 0===r&&(r=!0),g.fire("ShowCaret",{target:t,direction:e,before:n}).isDefaultPrevented()?null:(r&&g.selection.scrollIntoView(t,-1===e),o.show(n,t))},b=function(e,t){return t=Es(e,a,t),-1===e?vu.fromRangeStart(t):vu.fromRangeEnd(t)},n=function(e){return ha(e)||xa(e)||wa(e)},y=function(e){return n(e.startContainer)||n(e.endContainer)},c=function(e,t){var n,r,o,i,a,u,s,c,l,f,d=g.$,m=g.dom;if(!e)return null;if(e.collapsed){if(!y(e))if(!1===t){if(c=b(-1,e),rs(c.getNode(!0)))return v(-1,c.getNode(!0),!1,!1);if(rs(c.getNode()))return v(-1,c.getNode(),!c.isAtEnd(),!1)}else{if(c=b(1,e),rs(c.getNode()))return v(1,c.getNode(),!c.isAtEnd(),!1);if(rs(c.getNode(!0)))return v(1,c.getNode(!0),!1,!1)}return null}return i=e.startContainer,a=e.startOffset,u=e.endOffset,3===i.nodeType&&0===a&&pv(i.parentNode)&&(i=i.parentNode,a=m.nodeIndex(i),i=i.parentNode),1!==i.nodeType?null:(u===a+1&&(n=i.childNodes[a]),pv(n)?(l=f=n.cloneNode(!0),(s=g.fire("ObjectSelected",{target:n,targetClone:l})).isDefaultPrevented()?null:(r=Xi(rr.fromDom(g.getBody()),"#"+h).fold(function(){return d([])},function(e){return d([e.dom()])}),l=s.targetClone,0===r.length&&(r=d('<div data-mce-bogus="all" class="mce-offscreen-selection"></div>').attr("id",h)).appendTo(g.getBody()),e=g.dom.createRng(),l===f&&de.ie?(r.empty().append('<p style="font-size: 0" data-mce-bogus="all">\xa0</p>').append(l),e.setStartAfter(r[0].firstChild.firstChild),e.setEndAfter(l)):(r.empty().append("\xa0").append(l).append("\xa0"),e.setStart(r[0].firstChild,1),e.setEnd(r[0].lastChild,0)),r.css({top:m.getPos(n,g.getBody()).y}),r[0].focus(),(o=g.selection.getSel()).removeAllRanges(),o.addRange(e),F(Hi(rr.fromDom(g.getBody()),"*[data-mce-selected]"),function(e){Cr(e,"data-mce-selected")}),n.setAttribute("data-mce-selected","1"),p=n,C(),e)):null)},l=function(){p&&(p.removeAttribute("data-mce-selected"),Xi(rr.fromDom(g.getBody()),"#"+h).each(_i),p=null),Xi(rr.fromDom(g.getBody()),"#"+h).each(_i),p=null},C=function(){o.hide()};return de.ceFalse&&(function(){g.on("mouseup",function(e){var t=s();t.collapsed&&Qp(g,e.clientX,e.clientY)&&u(Lm(g,t,!1))}),g.on("click",function(e){var t;(t=bv(g,e.target))&&(pv(t)&&(e.preventDefault(),g.focus()),gv(t)&&g.dom.isChildOf(t,g.selection.getNode())&&l())}),g.on("blur NewBlock",function(){l()}),g.on("ResizeWindow FullscreenStateChanged",function(){return o.reposition()});var n,r,i=function(e,t){var n,r,o=g.dom.getParent(e,g.dom.isBlock),i=g.dom.getParent(t,g.dom.isBlock);return!(!o||!g.dom.isChildOf(o,i)||!1!==pv(bv(g,o)))||o&&(n=o,r=i,!(g.dom.getParent(n,g.dom.isBlock)===g.dom.getParent(r,g.dom.isBlock)))&&function(e){var t=ec(e);if(!e.firstChild)return!1;var n=vu.before(e.firstChild),r=t.next(n);return r&&!vv(r)&&!hv(r)}(o)};r=!1,(n=g).on("touchstart",function(){r=!1}),n.on("touchmove",function(){r=!0}),n.on("touchend",function(e){var t=bv(n,e.target);pv(t)&&(r||(e.preventDefault(),c(Pm(n,t))))}),g.on("mousedown",function(e){var t,n=e.target;if((n===a||"HTML"===n.nodeName||g.dom.isChildOf(n,a))&&!1!==Qp(g,e.clientX,e.clientY))if(t=bv(g,n))pv(t)?(e.preventDefault(),c(Pm(g,t))):(l(),gv(t)&&e.shiftKey||fv(e.clientX,e.clientY,g.selection.getRng())||(C(),g.selection.placeCaretAt(e.clientX,e.clientY)));else if(!1===rs(n)){l(),C();var r=lv(a,e.clientX,e.clientY);if(r&&!i(e.target,r.node)){e.preventDefault();var o=v(1,r.node,r.before,!1);g.getBody().focus(),u(o)}}}),g.on("keypress",function(e){mv.modifierPressed(e)||(e.keyCode,pv(g.selection.getNode())&&e.preventDefault())}),g.on("getSelectionRange",function(e){var t=e.range;if(p){if(!p.parentNode)return void(p=null);(t=t.cloneRange()).selectNode(p),e.range=t}}),g.on("setSelectionRange",function(e){var t;(t=c(e.range,e.forward))&&(e.range=t)}),g.on("AfterSetSelectionRange",function(e){var t,n=e.range;y(n)||"mcepastebin"===n.startContainer.parentNode.id||C(),t=n.startContainer.parentNode,g.dom.hasClass(t,"mce-offscreen-selection")||l()}),g.on("copy",function(e){var t,n=e.clipboardData;if(!e.isDefaultPrevented()&&e.clipboardData&&!de.ie){var r=(t=g.dom.get(h))?t.getElementsByTagName("*")[0]:t;r&&(e.preventDefault(),n.clearData(),n.setData("text/html",r.outerHTML),n.setData("text/plain",r.outerText))}}),Gh(g),dv(g)}(),e=g.contentStyles,t=".mce-content-body",e.push(o.getCss()),e.push(t+" .mce-offscreen-selection {position: absolute;left: -9999999999px;max-width: 1000000px;}"+t+" *[contentEditable=false] {cursor: default;}"+t+" *[contentEditable=true] {cursor: text;}")),{showCaret:v,showBlockCaretContainer:function(e){e.hasAttribute("data-mce-caret")&&(Na(e),u(s()),g.selection.scrollIntoView(e[0]))},hideFakeCaret:C,destroy:function(){o.destroy(),p=null}}},Cv=function(e,t,n){var r,o,i,a,u=1;for(a=e.getShortEndedElements(),(i=/<([!?\/])?([A-Za-z0-9\-_\:\.]+)((?:\s+[^"\'>]+(?:(?:"[^"]*")|(?:\'[^\']*\')|[^>]*))*|\/|\s+)>/g).lastIndex=r=n;o=i.exec(t);){if(r=i.lastIndex,"/"===o[1])u--;else if(!o[1]){if(o[2]in a)continue;u++}if(0===u)break}return r},xv=function(e,t){var n=e.exec(t);if(n){var r=n[1],o=n[2];return"string"==typeof r&&"data-mce-bogus"===r.toLowerCase()?o:null}return null};function wv(z,U){void 0===U&&(U=ni());var e=function(){};!1!==(z=z||{}).fix_self_closing&&(z.fix_self_closing=!0);var V=z.comment?z.comment:e,H=z.cdata?z.cdata:e,j=z.text?z.text:e,q=z.start?z.start:e,$=z.end?z.end:e,W=z.pi?z.pi:e,K=z.doctype?z.doctype:e;return{parse:function(e){var t,n,r,d,o,i,a,m,u,s,g,c,p,l,f,h,v,b,y,C,x,w,N,E,S,k,T,A,R,_=0,D=[],B=0,O=$o.decode,P=Yt.makeMap("src,href,data,background,formaction,poster,xlink:href"),L=/((java|vb)script|mhtml):/i,I=function(e){var t,n;for(t=D.length;t--&&D[t].name!==e;);if(0<=t){for(n=D.length-1;t<=n;n--)(e=D[n]).valid&&$(e.name);D.length=t}},M=function(e,t,n,r,o){var i,a,u,s,c;if(n=(t=t.toLowerCase())in g?t:O(n||r||o||""),p&&!m&&0==(0===(u=t).indexOf("data-")||0===u.indexOf("aria-"))){if(!(i=b[t])&&y){for(a=y.length;a--&&!(i=y[a]).pattern.test(t););-1===a&&(i=null)}if(!i)return;if(i.validValues&&!(n in i.validValues))return}if(P[t]&&!z.allow_script_urls){var l=n.replace(/[\s\u0000-\u001F]+/g,"");try{l=decodeURIComponent(l)}catch(f){l=unescape(l)}if(L.test(l))return;if(c=l,!(s=z).allow_html_data_urls&&(/^data:image\//i.test(c)?!1===s.allow_svg_data_urls&&/^data:image\/svg\+xml/i.test(c):/^data:/i.test(c)))return}m&&(t in P||0===t.indexOf("on"))||(d.map[t]=n,d.push({name:t,value:n}))};for(S=new RegExp("<(?:(?:!--([\\w\\W]*?)--\x3e)|(?:!\\[CDATA\\[([\\w\\W]*?)\\]\\]>)|(?:!DOCTYPE([\\w\\W]*?)>)|(?:\\?([^\\s\\/<>]+) ?([\\w\\W]*?)[?/]>)|(?:\\/([A-Za-z][A-Za-z0-9\\-_\\:\\.]*)>)|(?:([A-Za-z][A-Za-z0-9\\-_\\:\\.]*)((?:\\s+[^\"'>]+(?:(?:\"[^\"]*\")|(?:'[^']*')|[^>]*))*|\\/|\\s+)>))","g"),k=/([\w:\-]+)(?:\s*=\s*(?:(?:\"((?:[^\"])*)\")|(?:\'((?:[^\'])*)\')|([^>\s]+)))?/g,s=U.getShortEndedElements(),E=z.self_closing_elements||U.getSelfClosingElements(),g=U.getBoolAttrs(),p=z.validate,u=z.remove_internals,R=z.fix_self_closing,T=U.getSpecialElements(),N=e+">";t=S.exec(N);){if(_<t.index&&j(O(e.substr(_,t.index-_))),n=t[6])":"===(n=n.toLowerCase()).charAt(0)&&(n=n.substr(1)),I(n);else if(n=t[7]){if(t.index+t[0].length>e.length){j(O(e.substr(t.index))),_=t.index+t[0].length;continue}":"===(n=n.toLowerCase()).charAt(0)&&(n=n.substr(1)),c=n in s,R&&E[n]&&0<D.length&&D[D.length-1].name===n&&I(n);var F=xv(k,t[8]);if(null!==F){if("all"===F){_=Cv(U,e,S.lastIndex),S.lastIndex=_;continue}f=!1}if(!p||(l=U.getElementRule(n))){if(f=!0,p&&(b=l.attributes,y=l.attributePatterns),(v=t[8])?((m=-1!==v.indexOf("data-mce-type"))&&u&&(f=!1),(d=[]).map={},v.replace(k,M)):(d=[]).map={},p&&!m){if(C=l.attributesRequired,x=l.attributesDefault,w=l.attributesForced,l.removeEmptyAttrs&&!d.length&&(f=!1),w)for(o=w.length;o--;)a=(h=w[o]).name,"{$uid}"===(A=h.value)&&(A="mce_"+B++),d.map[a]=A,d.push({name:a,value:A});if(x)for(o=x.length;o--;)(a=(h=x[o]).name)in d.map||("{$uid}"===(A=h.value)&&(A="mce_"+B++),d.map[a]=A,d.push({name:a,value:A}));if(C){for(o=C.length;o--&&!(C[o]in d.map););-1===o&&(f=!1)}if(h=d.map["data-mce-bogus"]){if("all"===h){_=Cv(U,e,S.lastIndex),S.lastIndex=_;continue}f=!1}}f&&q(n,d,c)}else f=!1;if(r=T[n]){r.lastIndex=_=t.index+t[0].length,(t=r.exec(e))?(f&&(i=e.substr(_,t.index-_)),_=t.index+t[0].length):(i=e.substr(_),_=e.length),f&&(0<i.length&&j(i,!0),$(n)),S.lastIndex=_;continue}c||(v&&v.indexOf("/")===v.length-1?f&&$(n):D.push({name:n,valid:f}))}else(n=t[1])?(">"===n.charAt(0)&&(n=" "+n),z.allow_conditional_comments||"[if"!==n.substr(0,3).toLowerCase()||(n=" "+n),V(n)):(n=t[2])?H(n.replace(/<!--|-->/g,"")):(n=t[3])?K(n):(n=t[4])&&W(n,t[5]);_=t.index+t[0].length}for(_<e.length&&j(O(e.substr(_))),o=D.length-1;0<=o;o--)(n=D[o]).valid&&$(n.name)}}}(wv||(wv={})).findEndTag=Cv;var Nv=wv,Ev=function(e,t){var n,r,o,i,a,u,s,c,l=t,f=/<(\w+) [^>]*data-mce-bogus="all"[^>]*>/g,d=e.schema;for(u=e.getTempAttrs(),s=l,c=new RegExp(["\\s?("+u.join("|")+')="[^"]+"'].join("|"),"gi"),l=s.replace(c,""),a=d.getShortEndedElements();i=f.exec(l);)r=f.lastIndex,o=i[0].length,n=a[i[1]]?r:Nv.findEndTag(d,l,r),l=l.substring(0,r-o)+l.substring(n),f.lastIndex=r-o;return fa(l)},Sv={trimExternal:Ev,trimInternal:Ev},kv=0,Tv=2,Av=1,Rv=function(g,p){var e=g.length+p.length+2,h=new Array(e),v=new Array(e),c=function(e,t,n,r,o){var i=l(e,t,n,r);if(null===i||i.start===t&&i.diag===t-r||i.end===e&&i.diag===e-n)for(var a=e,u=n;a<t||u<r;)a<t&&u<r&&g[a]===p[u]?(o.push([0,g[a]]),++a,++u):r-n<t-e?(o.push([2,g[a]]),++a):(o.push([1,p[u]]),++u);else{c(e,i.start,n,i.start-i.diag,o);for(var s=i.start;s<i.end;++s)o.push([0,g[s]]);c(i.end,t,i.end-i.diag,r,o)}},b=function(e,t,n,r){for(var o=e;o-t<r&&o<n&&g[o]===p[o-t];)++o;return{start:e,end:o,diag:t}},l=function(e,t,n,r){var o=t-e,i=r-n;if(0===o||0===i)return null;var a,u,s,c,l,f=o-i,d=i+o,m=(d%2==0?d:d+1)/2;for(h[1+m]=e,v[1+m]=t+1,a=0;a<=m;++a){for(u=-a;u<=a;u+=2){for(s=u+m,u===-a||u!==a&&h[s-1]<h[s+1]?h[s]=h[s+1]:h[s]=h[s-1]+1,l=(c=h[s])-e+n-u;c<t&&l<r&&g[c]===p[l];)h[s]=++c,++l;if(f%2!=0&&f-a<=u&&u<=f+a&&v[s-f]<=h[s])return b(v[s-f],u+e-n,t,r)}for(u=f-a;u<=f+a;u+=2){for(s=u+m-f,u===f-a||u!==f+a&&v[s+1]<=v[s-1]?v[s]=v[s+1]-1:v[s]=v[s-1],l=(c=v[s]-1)-e+n-u;e<=c&&n<=l&&g[c]===p[l];)v[s]=c--,l--;if(f%2==0&&-a<=u&&u<=a&&v[s]<=h[s+f])return b(v[s],u+e-n,t,r)}}},t=[];return c(0,g.length,0,p.length,t),t},_v=function(e){return Do.isElement(e)?e.outerHTML:Do.isText(e)?$o.encodeRaw(e.data,!1):Do.isComment(e)?"\x3c!--"+e.data+"--\x3e":""},Dv=function(e,t,n){var r=function(e){var t,n,r;for(r=document.createElement("div"),t=document.createDocumentFragment(),e&&(r.innerHTML=e);n=r.firstChild;)t.appendChild(n);return t}(t);if(e.hasChildNodes()&&n<e.childNodes.length){var o=e.childNodes[n];o.parentNode.insertBefore(r,o)}else e.appendChild(r)},Bv=function(e){return z($(ne(e.childNodes),_v),function(e){return 0<e.length})},Ov=function(e,t){var n,r,o,i=$(ne(t.childNodes),_v);return n=Rv(i,e),r=t,o=0,F(n,function(e){e[0]===kv?o++:e[0]===Av?(Dv(r,e[1],o),o++):e[0]===Tv&&function(e,t){if(e.hasChildNodes()&&t<e.childNodes.length){var n=e.childNodes[t];n.parentNode.removeChild(n)}}(r,o)}),t},Pv=Oi(A.none()),Lv=function(e){return{type:"fragmented",fragments:e,content:"",bookmark:null,beforeBookmark:null}},Iv=function(e){return{type:"complete",fragments:null,content:e,bookmark:null,beforeBookmark:null}},Mv=function(e){return"fragmented"===e.type?e.fragments.join(""):e.content},Fv=function(e){var t=rr.fromTag("body",Pv.get().getOrThunk(function(){var e=document.implementation.createHTMLDocument("undo");return Pv.set(A.some(e)),e}));return ua(t,Mv(e)),F(Hi(t,"*[data-mce-bogus]"),Di),t.dom().innerHTML},zv=function(n){var e,t,r;return e=Bv(n.getBody()),-1!==(t=(r=G(e,function(e){var t=Sv.trimInternal(n.serializer,e);return 0<t.length?[t]:[]})).join("")).indexOf("</iframe>")?Lv(r):Iv(t)},Uv=function(e,t,n){"fragmented"===t.type?Ov(t.fragments,e.getBody()):e.setContent(t.content,{format:"raw"}),e.selection.moveToBookmark(n?t.beforeBookmark:t.bookmark)},Vv=function(e,t){return!(!e||!t)&&(r=t,Mv(e)===Mv(r)||(n=t,Fv(e)===Fv(n)));var n,r};function Hv(u){var s,r,o=this,c=0,l=[],t=0,f=function(){return 0===t},i=function(e){f()&&(o.typing=e)},d=function(e){u.setDirty(e)},a=function(e){i(!1),o.add({},e)},n=function(){o.typing&&(i(!1),o.add())};return u.on("init",function(){o.add()}),u.on("BeforeExecCommand",function(e){var t=e.command;"Undo"!==t&&"Redo"!==t&&"mceRepaint"!==t&&(n(),o.beforeChange())}),u.on("ExecCommand",function(e){var t=e.command;"Undo"!==t&&"Redo"!==t&&"mceRepaint"!==t&&a(e)}),u.on("ObjectResizeStart Cut",function(){o.beforeChange()}),u.on("SaveContent ObjectResized blur",a),u.on("DragEnd",a),u.on("KeyUp",function(e){var t=e.keyCode;e.isDefaultPrevented()||((33<=t&&t<=36||37<=t&&t<=40||45===t||e.ctrlKey)&&(a(),u.nodeChanged()),46!==t&&8!==t||u.nodeChanged(),r&&o.typing&&!1===Vv(zv(u),l[0])&&(!1===u.isDirty()&&(d(!0),u.fire("change",{level:l[0],lastLevel:null})),u.fire("TypingUndo"),r=!1,u.nodeChanged()))}),u.on("KeyDown",function(e){var t=e.keyCode;if(!e.isDefaultPrevented())if(33<=t&&t<=36||37<=t&&t<=40||45===t)o.typing&&a(e);else{var n=e.ctrlKey&&!e.altKey||e.metaKey;!(t<16||20<t)||224===t||91===t||o.typing||n||(o.beforeChange(),i(!0),o.add({},e),r=!0)}}),u.on("MouseDown",function(e){o.typing&&a(e)}),u.on("input",function(e){var t;e.inputType&&("insertReplacementText"===e.inputType||"insertText"===(t=e).inputType&&null===t.data)&&a(e)}),u.addShortcut("meta+z","","Undo"),u.addShortcut("meta+y,meta+shift+z","","Redo"),u.on("AddUndo Undo Redo ClearUndos",function(e){e.isDefaultPrevented()||u.nodeChanged()}),o={data:l,typing:!1,beforeChange:function(){f()&&(s=Fu.getUndoBookmark(u.selection))},add:function(e,t){var n,r,o,i=u.settings;if(o=zv(u),e=e||{},e=Yt.extend(e,o),!1===f()||u.removed)return null;if(r=l[c],u.fire("BeforeAddUndo",{level:e,lastLevel:r,originalEvent:t}).isDefaultPrevented())return null;if(r&&Vv(r,e))return null;if(l[c]&&(l[c].beforeBookmark=s),i.custom_undo_redo_levels&&l.length>i.custom_undo_redo_levels){for(n=0;n<l.length-1;n++)l[n]=l[n+1];l.length--,c=l.length}e.bookmark=Fu.getUndoBookmark(u.selection),c<l.length-1&&(l.length=c+1),l.push(e),c=l.length-1;var a={level:e,lastLevel:r,originalEvent:t};return u.fire("AddUndo",a),0<c&&(d(!0),u.fire("change",a)),e},undo:function(){var e;return o.typing&&(o.add(),o.typing=!1,i(!1)),0<c&&(e=l[--c],Uv(u,e,!0),d(!0),u.fire("undo",{level:e})),e},redo:function(){var e;return c<l.length-1&&(e=l[++c],Uv(u,e,!1),d(!0),u.fire("redo",{level:e})),e},clear:function(){l=[],c=0,o.typing=!1,o.data=l,u.fire("ClearUndos")},hasUndo:function(){return 0<c||o.typing&&l[0]&&!Vv(zv(u),l[0])},hasRedo:function(){return c<l.length-1&&!o.typing},transact:function(e){return n(),o.beforeChange(),o.ignore(e),o.add()},ignore:function(e){try{t++,e()}finally{t--}},extra:function(e,t){var n,r;o.transact(e)&&(r=l[c].bookmark,n=l[c-1],Uv(u,n,!0),o.transact(t)&&(l[c-1].beforeBookmark=r))}}}var jv,qv,$v={},Wv=qt.filter,Kv=qt.each;qv=function(e){var t,n,r=e.selection.getRng();t=Do.matchNodeNames("pre"),r.collapsed||(n=e.selection.getSelectedBlocks(),Kv(Wv(Wv(n,t),function(e){return t(e.previousSibling)&&-1!==qt.indexOf(n,e.previousSibling)}),function(e){var t,n;t=e.previousSibling,pn(n=e).remove(),pn(t).append("<br><br>").append(n.childNodes)}))},$v[jv="pre"]||($v[jv]=[]),$v[jv].push(qv);var Xv=function(e,t){Kv($v[e],function(e){e(t)})},Yv=/^(src|href|style)$/,Gv=Yt.each,Jv=Nc.isEq,Qv=function(e,t,n){return e.isChildOf(t,n)&&t!==n&&!e.isBlock(n)},Zv=function(e,t,n){var r,o,i;return r=t[n?"startContainer":"endContainer"],o=t[n?"startOffset":"endOffset"],Do.isElement(r)&&(i=r.childNodes.length-1,!n&&o&&o--,r=r.childNodes[i<o?i:o]),Do.isText(r)&&n&&o>=r.nodeValue.length&&(r=new ro(r,e.getBody()).next()||r),Do.isText(r)&&!n&&0===o&&(r=new ro(r,e.getBody()).prev()||r),r},eb=function(e,t,n,r){var o=e.create(n,r);return t.parentNode.insertBefore(o,t),o.appendChild(t),o},tb=function(e,t,n,r,o){var i=rr.fromDom(t),a=rr.fromDom(e.create(r,o)),u=n?Ur(i):zr(i);return Ai(a,u),n?(Ei(i,a),ki(a,i)):(Si(i,a),Ti(a,i)),a.dom()},nb=function(e,t,n,r){return!(t=Nc.getNonWhiteSpaceSibling(t,n,r))||"BR"===t.nodeName||e.isBlock(t)},rb=function(e,n,r,o,i){var t,a,u,s,c,l,f,d,m,g,p,h,v,b,y=e.dom;if(c=y,!(Jv(l=o,(f=n).inline)||Jv(l,f.block)||(f.selector?Do.isElement(l)&&c.is(l,f.selector):void 0)||(s=o,n.links&&"A"===s.tagName)))return!1;if("all"!==n.remove)for(Gv(n.styles,function(e,t){e=Nc.normalizeStyleValue(y,Nc.replaceVars(e,r),t),"number"==typeof t&&(t=e,i=0),(n.remove_similar||!i||Jv(Nc.getStyle(y,i,t),e))&&y.setStyle(o,t,""),u=1}),u&&""===y.getAttrib(o,"style")&&(o.removeAttribute("style"),o.removeAttribute("data-mce-style")),Gv(n.attributes,function(e,t){var n;if(e=Nc.replaceVars(e,r),"number"==typeof t&&(t=e,i=0),!i||Jv(y.getAttrib(i,t),e)){if("class"===t&&(e=y.getAttrib(o,t))&&(n="",Gv(e.split(/\s+/),function(e){/mce\-\w+/.test(e)&&(n+=(n?" ":"")+e)}),n))return void y.setAttrib(o,t,n);"class"===t&&o.removeAttribute("className"),Yv.test(t)&&o.removeAttribute("data-mce-"+t),o.removeAttribute(t)}}),Gv(n.classes,function(e){e=Nc.replaceVars(e,r),i&&!y.hasClass(i,e)||y.removeClass(o,e)}),a=y.getAttribs(o),t=0;t<a.length;t++){var C=a[t].nodeName;if(0!==C.indexOf("_")&&0!==C.indexOf("data-"))return!1}return"none"!==n.remove?(d=e,g=n,h=(m=o).parentNode,v=d.dom,b=d.settings.forced_root_block,g.block&&(b?h===v.getRoot()&&(g.list_block&&Jv(m,g.list_block)||Gv(Yt.grep(m.childNodes),function(e){Nc.isValid(d,b,e.nodeName.toLowerCase())?p?p.appendChild(e):(p=eb(v,e,b),v.setAttribs(p,d.settings.forced_root_block_attrs)):p=0})):v.isBlock(m)&&!v.isBlock(h)&&(nb(v,m,!1)||nb(v,m.firstChild,!0,1)||m.insertBefore(v.create("br"),m.firstChild),nb(v,m,!0)||nb(v,m.lastChild,!1,1)||m.appendChild(v.create("br")))),g.selector&&g.inline&&!Jv(g.inline,m)||v.remove(m,1),!0):void 0},ob=rb,ib=function(s,c,l,e,f){var t,n,d=s.formatter.get(c),m=d[0],a=!0,u=s.dom,r=s.selection,i=function(e){var n,t,r,o,i,a,u=(n=s,t=e,r=c,o=l,i=f,Gv(Nc.getParents(n.dom,t.parentNode).reverse(),function(e){var t;a||"_start"===e.id||"_end"===e.id||(t=dm.matchNode(n,e,r,o,i))&&!1!==t.split&&(a=e)}),a);return function(e,t,n,r,o,i,a,u){var s,c,l,f,d,m,g=e.dom;if(n){for(m=n.parentNode,s=r.parentNode;s&&s!==m;s=s.parentNode){for(c=g.clone(s,!1),d=0;d<t.length;d++)if(rb(e,t[d],u,c,c)){c=0;break}c&&(l&&c.appendChild(l),f||(f=c),l=c)}!i||a.mixed&&g.isBlock(n)||(r=g.split(n,r)),l&&(o.parentNode.insertBefore(l,o),f.appendChild(o))}return r}(s,d,u,e,e,!0,m,l)},g=function(e){var t,n,r,o,i;if(Do.isElement(e)&&u.getContentEditable(e)&&(o=a,a="true"===u.getContentEditable(e),i=!0),t=Yt.grep(e.childNodes),a&&!i)for(n=0,r=d.length;n<r&&!rb(s,d[n],l,e,e);n++);if(m.deep&&t.length){for(n=0,r=t.length;n<r;n++)g(t[n]);i&&(a=o)}},p=function(e){var t,n=u.get(e?"_start":"_end"),r=n[e?"firstChild":"lastChild"];return yc(t=r)&&Do.isElement(t)&&("_start"===t.id||"_end"===t.id)&&(r=r[e?"firstChild":"lastChild"]),Do.isText(r)&&0===r.data.length&&(r=e?n.previousSibling||n.nextSibling:n.nextSibling||n.previousSibling),u.remove(n,!0),r},o=function(e){var t,n,r=e.commonAncestorContainer;if(e=Lc(s,e,d,!0),m.split){if(e=gm(e),(t=Zv(s,e,!0))!==(n=Zv(s,e))){if(/^(TR|TH|TD)$/.test(t.nodeName)&&t.firstChild&&(t="TR"===t.nodeName?t.firstChild.firstChild||t:t.firstChild||t),r&&/^T(HEAD|BODY|FOOT|R)$/.test(r.nodeName)&&/^(TH|TD)$/.test(n.nodeName)&&n.firstChild&&(n=n.firstChild||n),Qv(u,t,n)){var o=A.from(t.firstChild).getOr(t);return i(tb(u,o,!0,"span",{id:"_start","data-mce-type":"bookmark"})),void p(!0)}if(Qv(u,n,t))return o=A.from(n.lastChild).getOr(n),i(tb(u,o,!1,"span",{id:"_end","data-mce-type":"bookmark"})),void p(!1);t=eb(u,t,"span",{id:"_start","data-mce-type":"bookmark"}),n=eb(u,n,"span",{id:"_end","data-mce-type":"bookmark"}),i(t),i(n),t=p(!0),n=p()}else t=n=i(t);e.startContainer=t.parentNode?t.parentNode:t,e.startOffset=u.nodeIndex(t),e.endContainer=n.parentNode?n.parentNode:n,e.endOffset=u.nodeIndex(n)+1}Mc(u,e,function(e){Gv(e,function(e){g(e),Do.isElement(e)&&"underline"===s.dom.getStyle(e,"text-decoration")&&e.parentNode&&"underline"===Nc.getTextDecoration(u,e.parentNode)&&rb(s,{deep:!1,exact:!0,inline:"span",styles:{textDecoration:"underline"}},null,e)})})};if(e)e.nodeType?((n=u.createRng()).setStartBefore(e),n.setEndAfter(e),o(n)):o(e);else if("false"!==u.getContentEditable(r.getNode()))r.isCollapsed()&&m.inline&&!u.select("td[data-mce-selected],th[data-mce-selected]").length?function(e,t,n,r){var o,i,a,u,s,c,l,f=e.dom,d=e.selection,m=[],g=d.getRng();for(o=g.startContainer,i=g.startOffset,3===(s=o).nodeType&&(i!==o.nodeValue.length&&(u=!0),s=s.parentNode);s;){if(dm.matchNode(e,s,t,n,r)){c=s;break}s.nextSibling&&(u=!0),m.push(s),s=s.parentNode}if(c)if(u){a=d.getBookmark(),g.collapse(!0);var p=Lc(e,g,e.formatter.get(t),!0);p=gm(p),e.formatter.remove(t,n,p),d.moveToBookmark(a)}else{l=Vu(e.getBody(),c);var h=ym(!1).dom(),v=Em(m,h);wm(e,h,l||c),Cm(e,l,!1),d.setCursorLocation(v,1),f.isEmpty(c)&&f.remove(c)}}(s,c,l,f):(t=Fu.getPersistentBookmark(s.selection,!0),o(r.getRng()),r.moveToBookmark(t),m.inline&&dm.match(s,c,l,r.getStart())&&Nc.moveStart(u,r,r.getRng()),s.nodeChanged());else{e=r.getNode();for(var h=0,v=d.length;h<v&&(!d[h].ceFalseOverride||!rb(s,d[h],l,e,e));h++);}},ab=Yt.each,ub=function(e){return e&&1===e.nodeType&&!yc(e)&&!Uu(e)&&!Do.isBogus(e)},sb=function(e,t){var n;for(n=e;n;n=n[t]){if(3===n.nodeType&&0!==n.nodeValue.length)return e;if(1===n.nodeType&&!yc(n))return n}return e},cb=function(e,t,n){var r,o,i=new tl(e);if(t&&n&&(t=sb(t,"previousSibling"),n=sb(n,"nextSibling"),i.compare(t,n))){for(r=t.nextSibling;r&&r!==n;)r=(o=r).nextSibling,t.appendChild(o);return e.remove(n),Yt.each(Yt.grep(n.childNodes),function(e){t.appendChild(e)}),t}return n},lb=function(e,t,n){ab(e.childNodes,function(e){ub(e)&&(t(e)&&n(e),e.hasChildNodes()&&lb(e,t,n))})},fb=function(n,e){return d(function(e,t){return!(!t||!Nc.getStyle(n,t,e))},e)},db=function(r,e,t){return d(function(e,t,n){r.setStyle(n,e,t),""===n.getAttribute("style")&&n.removeAttribute("style"),mb(r,n)},e,t)},mb=function(e,t){"SPAN"===t.nodeName&&0===e.getAttribs(t).length&&e.remove(t,!0)},gb=function(e,t){var n;1===t.nodeType&&t.parentNode&&1===t.parentNode.nodeType&&(n=Nc.getTextDecoration(e,t.parentNode),e.getStyle(t,"color")&&n?e.setStyle(t,"text-decoration",n):e.getStyle(t,"text-decoration")===n&&e.setStyle(t,"text-decoration",null))},pb=function(n,e,r,o){ab(e,function(t){ab(n.dom.select(t.inline,o),function(e){ub(e)&&ob(n,t,r,e,t.exact?e:null)}),function(r,e,t){if(e.clear_child_styles){var n=e.links?"*:not(a)":"*";ab(r.select(n,t),function(n){ub(n)&&ab(e.styles,function(e,t){r.setStyle(n,t,"")})})}}(n.dom,t,o)})},hb=function(e,t,n,r){(t.styles.color||t.styles.textDecoration)&&(Yt.walk(r,d(gb,e),"childNodes"),gb(e,r))},vb=function(e,t,n,r){t.styles&&t.styles.backgroundColor&&lb(r,fb(e,"fontSize"),db(e,"backgroundColor",Nc.replaceVars(t.styles.backgroundColor,n)))},bb=function(e,t,n,r){"sub"!==t.inline&&"sup"!==t.inline||(lb(r,fb(e,"fontSize"),db(e,"fontSize","")),e.remove(e.select("sup"===t.inline?"sub":"sup",r),!0))},yb=function(e,t,n,r){r&&!1!==t.merge_siblings&&(r=cb(e,Nc.getNonWhiteSpaceSibling(r),r),r=cb(e,r,Nc.getNonWhiteSpaceSibling(r,!0)))},Cb=function(t,n,r,o,i){dm.matchNode(t,i.parentNode,r,o)&&ob(t,n,o,i)||n.merge_with_parents&&t.dom.getParent(i.parentNode,function(e){if(dm.matchNode(t,e,r,o))return ob(t,n,o,i),!0})},xb=Yt.each,wb=function(g,p,h,r){var e,t,v=g.formatter.get(p),b=v[0],o=!r&&g.selection.isCollapsed(),i=g.dom,n=g.selection,y=function(n,e){if(e=e||b,n){if(e.onformat&&e.onformat(n,e,h,r),xb(e.styles,function(e,t){i.setStyle(n,t,Nc.replaceVars(e,h))}),e.styles){var t=i.getAttrib(n,"style");t&&n.setAttribute("data-mce-style",t)}xb(e.attributes,function(e,t){i.setAttrib(n,t,Nc.replaceVars(e,h))}),xb(e.classes,function(e){e=Nc.replaceVars(e,h),i.hasClass(n,e)||i.addClass(n,e)})}},C=function(e,t){var n=!1;return!!b.selector&&(xb(e,function(e){if(!("collapsed"in e&&e.collapsed!==o))return i.is(t,e.selector)&&!Uu(t)?(y(t,e),!(n=!0)):void 0}),n)},a=function(s,e,t,c){var l,f,d=[],m=!0;l=b.inline||b.block,f=s.create(l),y(f),Mc(s,e,function(e){var a,u=function(e){var t,n,r,o;if(o=m,t=e.nodeName.toLowerCase(),n=e.parentNode.nodeName.toLowerCase(),1===e.nodeType&&s.getContentEditable(e)&&(o=m,m="true"===s.getContentEditable(e),r=!0),Nc.isEq(t,"br"))return a=0,void(b.block&&s.remove(e));if(b.wrapper&&dm.matchNode(g,e,p,h))a=0;else{if(m&&!r&&b.block&&!b.wrapper&&Nc.isTextBlock(g,t)&&Nc.isValid(g,n,l))return e=s.rename(e,l),y(e),d.push(e),void(a=0);if(b.selector){var i=C(v,e);if(!b.inline||i)return void(a=0)}!m||r||!Nc.isValid(g,l,t)||!Nc.isValid(g,n,l)||!c&&3===e.nodeType&&1===e.nodeValue.length&&65279===e.nodeValue.charCodeAt(0)||Uu(e)||b.inline&&s.isBlock(e)?(a=0,xb(Yt.grep(e.childNodes),u),r&&(m=o),a=0):(a||(a=s.clone(f,!1),e.parentNode.insertBefore(a,e),d.push(a)),a.appendChild(e))}};xb(e,u)}),!0===b.links&&xb(d,function(e){var t=function(e){"A"===e.nodeName&&y(e,b),xb(Yt.grep(e.childNodes),t)};t(e)}),xb(d,function(e){var t,n,r,o,i,a=function(e){var n=!1;return xb(e.childNodes,function(e){if((t=e)&&1===t.nodeType&&!yc(t)&&!Uu(t)&&!Do.isBogus(t))return n=e,!1;var t}),n};n=0,xb(e.childNodes,function(e){Nc.isWhiteSpaceNode(e)||yc(e)||n++}),t=n,!(1<d.length)&&s.isBlock(e)||0!==t?(b.inline||b.wrapper)&&(b.exact||1!==t||((o=a(r=e))&&!yc(o)&&dm.matchName(s,o,b)&&(i=s.clone(o,!1),y(i),s.replace(i,r,!0),s.remove(o,1)),e=i||r),pb(g,v,h,e),Cb(g,b,p,h,e),vb(s,b,h,e),bb(s,b,h,e),yb(s,b,h,e)):s.remove(e,1)})};if("false"!==i.getContentEditable(n.getNode())){if(b){if(r)r.nodeType?C(v,r)||((t=i.createRng()).setStartBefore(r),t.setEndAfter(r),a(i,Lc(g,t,v),0,!0)):a(i,r,0,!0);else if(o&&b.inline&&!i.select("td[data-mce-selected],th[data-mce-selected]").length)!function(e,t,n){var r,o,i,a,u,s,c=e.selection;a=(r=c.getRng(!0)).startOffset,s=r.startContainer.nodeValue,(o=Vu(e.getBody(),c.getStart()))&&(i=bm(o));var l,f,d=/[^\s\u00a0\u00ad\u200b\ufeff]/;s&&0<a&&a<s.length&&d.test(s.charAt(a))&&d.test(s.charAt(a-1))?(u=c.getBookmark(),r.collapse(!0),r=Lc(e,r,e.formatter.get(t)),r=gm(r),e.formatter.apply(t,n,r),c.moveToBookmark(u)):(o&&i.nodeValue===pm||(l=e.getDoc(),f=ym(!0).dom(),i=(o=l.importNode(f,!0)).firstChild,r.insertNode(o),a=1),e.formatter.apply(t,n,o),c.setCursorLocation(i,a))}(g,p,h);else{var u=g.selection.getNode();g.settings.forced_root_block||!v[0].defaultBlock||i.getParent(u,i.isBlock)||wb(g,v[0].defaultBlock),g.selection.setRng(cl(g.selection.getRng())),e=Fu.getPersistentBookmark(g.selection,!0),a(i,Lc(g,n.getRng(),v)),b.styles&&hb(i,b,h,u),n.moveToBookmark(e),Nc.moveStart(i,n,n.getRng()),g.nodeChanged()}Xv(p,g)}}else{r=n.getNode();for(var s=0,c=v.length;s<c;s++)if(v[s].ceFalseOverride&&i.is(r,v[s].selector))return void y(r,v[s])}},Nb={applyFormat:wb},Eb=Yt.each,Sb=function(e,t,n,r,o){var i,a,u,s,c,l,f,d;null===t.get()&&(a=e,u={},(i=t).set({}),a.on("NodeChange",function(n){var r=Nc.getParents(a.dom,n.element),o={};r=Yt.grep(r,function(e){return 1===e.nodeType&&!e.getAttribute("data-mce-bogus")}),Eb(i.get(),function(e,n){Eb(r,function(t){return a.formatter.matchNode(t,n,{},e.similar)?(u[n]||(Eb(e,function(e){e(!0,{node:t,format:n,parents:r})}),u[n]=e),o[n]=e,!1):!dm.matchesUnInheritedFormatSelector(a,t,n)&&void 0})}),Eb(u,function(e,t){o[t]||(delete u[t],Eb(e,function(e){e(!1,{node:n.element,format:t,parents:r})}))})})),c=n,l=r,f=o,d=(s=t).get(),Eb(c.split(","),function(e){d[e]||(d[e]=[],d[e].similar=f),d[e].push(l)}),s.set(d)},kb={get:function(r){var t={valigntop:[{selector:"td,th",styles:{verticalAlign:"top"}}],valignmiddle:[{selector:"td,th",styles:{verticalAlign:"middle"}}],valignbottom:[{selector:"td,th",styles:{verticalAlign:"bottom"}}],alignleft:[{selector:"figure.image",collapsed:!1,classes:"align-left",ceFalseOverride:!0,preview:"font-family font-size"},{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li",styles:{textAlign:"left"},inherit:!1,preview:!1,defaultBlock:"div"},{selector:"img,table",collapsed:!1,styles:{"float":"left"},preview:"font-family font-size"}],aligncenter:[{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li",styles:{textAlign:"center"},inherit:!1,preview:"font-family font-size",defaultBlock:"div"},{selector:"figure.image",collapsed:!1,classes:"align-center",ceFalseOverride:!0,preview:"font-family font-size"},{selector:"img",collapsed:!1,styles:{display:"block",marginLeft:"auto",marginRight:"auto"},preview:!1},{selector:"table",collapsed:!1,styles:{marginLeft:"auto",marginRight:"auto"},preview:"font-family font-size"}],alignright:[{selector:"figure.image",collapsed:!1,classes:"align-right",ceFalseOverride:!0,preview:"font-family font-size"},{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li",styles:{textAlign:"right"},inherit:!1,preview:"font-family font-size",defaultBlock:"div"},{selector:"img,table",collapsed:!1,styles:{"float":"right"},preview:"font-family font-size"}],alignjustify:[{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li",styles:{textAlign:"justify"},inherit:!1,defaultBlock:"div",preview:"font-family font-size"}],bold:[{inline:"strong",remove:"all"},{inline:"span",styles:{fontWeight:"bold"}},{inline:"b",remove:"all"}],italic:[{inline:"em",remove:"all"},{inline:"span",styles:{fontStyle:"italic"}},{inline:"i",remove:"all"}],underline:[{inline:"span",styles:{textDecoration:"underline"},exact:!0},{inline:"u",remove:"all"}],strikethrough:[{inline:"span",styles:{textDecoration:"line-through"},exact:!0},{inline:"strike",remove:"all"}],forecolor:{inline:"span",styles:{color:"%value"},links:!0,remove_similar:!0,clear_child_styles:!0},hilitecolor:{inline:"span",styles:{backgroundColor:"%value"},links:!0,remove_similar:!0,clear_child_styles:!0},fontname:{inline:"span",toggle:!1,styles:{fontFamily:"%value"},clear_child_styles:!0},fontsize:{inline:"span",toggle:!1,styles:{fontSize:"%value"},clear_child_styles:!0},fontsize_class:{inline:"span",attributes:{"class":"%value"}},blockquote:{block:"blockquote",wrapper:1,remove:"all"},subscript:{inline:"sub"},superscript:{inline:"sup"},code:{inline:"code"},link:{inline:"a",selector:"a",remove:"all",split:!0,deep:!0,onmatch:function(){return!0},onformat:function(n,e,t){Yt.each(t,function(e,t){r.setAttrib(n,t,e)})}},removeformat:[{selector:"b,strong,em,i,font,u,strike,sub,sup,dfn,code,samp,kbd,var,cite,mark,q,del,ins",remove:"all",split:!0,expand:!1,block_expand:!0,deep:!0},{selector:"span",attributes:["style","class"],remove:"empty",split:!0,expand:!1,deep:!0},{selector:"*",attributes:["style","class"],split:!1,expand:!1,deep:!0}]};return Yt.each("p h1 h2 h3 h4 h5 h6 div address pre div dt dd samp".split(/\s/),function(e){t[e]={block:e,remove:"all"}}),t}},Tb=Yt.each,Ab=pi.DOM,Rb=function(e,t){var n,o,r,m=t&&t.schema||ni({}),g=function(e){var t,n,r;return o="string"==typeof e?{name:e,classes:[],attrs:{}}:e,t=Ab.create(o.name),n=t,(r=o).classes.length&&Ab.addClass(n,r.classes.join(" ")),Ab.setAttribs(n,r.attrs),t},p=function(n,e,t){var r,o,i,a,u,s,c,l,f=0<e.length&&e[0],d=f&&f.name;if(u=d,s="string"!=typeof(a=n)?a.nodeName.toLowerCase():a,c=m.getElementRule(s),i=!(!(l=c&&c.parentsRequired)||!l.length)&&(u&&-1!==Yt.inArray(l,u)?u:l[0]))d===i?(o=e[0],e=e.slice(1)):o=i;else if(f)o=e[0],e=e.slice(1);else if(!t)return n;return o&&(r=g(o)).appendChild(n),t&&(r||(r=Ab.create("div")).appendChild(n),Yt.each(t,function(e){var t=g(e);r.insertBefore(t,n)})),p(r,e,o&&o.siblings)};return e&&e.length?(o=e[0],n=g(o),(r=Ab.create("div")).appendChild(p(n,e.slice(1),o.siblings)),r):""},_b=function(e){var t,a={classes:[],attrs:{}};return"*"!==(e=a.selector=Yt.trim(e))&&(t=e.replace(/(?:([#\.]|::?)([\w\-]+)|(\[)([^\]]+)\]?)/g,function(e,t,n,r,o){switch(t){case"#":a.attrs.id=n;break;case".":a.classes.push(n);break;case":":-1!==Yt.inArray("checked disabled enabled read-only required".split(" "),n)&&(a.attrs[n]=n)}if("["===r){var i=o.match(/([\w\-]+)(?:\=\"([^\"]+))?/);i&&(a.attrs[i[1]]=i[2])}return""})),a.name=t||"div",a},Db=function(e){return e&&"string"==typeof e?(e=(e=e.split(/\s*,\s*/)[0]).replace(/\s*(~\+|~|\+|>)\s*/g,"$1"),Yt.map(e.split(/(?:>|\s+(?![^\[\]]+\]))/),function(e){var t=Yt.map(e.split(/(?:~\+|~|\+)/),_b),n=t.pop();return t.length&&(n.siblings=t),n}).reverse()):[]},Bb=function(n,e){var t,r,o,i,a,u,s="";if(!1===(u=n.settings.preview_styles))return"";"string"!=typeof u&&(u="font-family font-size font-weight font-style text-decoration text-transform color background-color border border-radius outline text-shadow");var c=function(e){return e.replace(/%(\w+)/g,"")};if("string"==typeof e){if(!(e=n.formatter.get(e)))return;e=e[0]}return"preview"in e&&!1===(u=e.preview)?"":(t=e.block||e.inline||"span",(i=Db(e.selector)).length?(i[0].name||(i[0].name=t),t=e.selector,r=Rb(i,n)):r=Rb([t],n),o=Ab.select(t,r)[0]||r.firstChild,Tb(e.styles,function(e,t){(e=c(e))&&Ab.setStyle(o,t,e)}),Tb(e.attributes,function(e,t){(e=c(e))&&Ab.setAttrib(o,t,e)}),Tb(e.classes,function(e){e=c(e),Ab.hasClass(o,e)||Ab.addClass(o,e)}),n.fire("PreviewFormats"),Ab.setStyles(r,{position:"absolute",left:-65535}),n.getBody().appendChild(r),a=Ab.getStyle(n.getBody(),"fontSize",!0),a=/px$/.test(a)?parseInt(a,10):0,Tb(u.split(" "),function(e){var t=Ab.getStyle(o,e,!0);if(!("background-color"===e&&/transparent|rgba\s*\([^)]+,\s*0\)/.test(t)&&(t=Ab.getStyle(n.getBody(),e,!0),"#ffffff"===Ab.toHex(t).toLowerCase())||"color"===e&&"#000000"===Ab.toHex(t).toLowerCase())){if("font-size"===e&&/em|%$/.test(t)){if(0===a)return;t=parseFloat(t)/(/%$/.test(t)?100:1)*a+"px"}"border"===e&&t&&(s+="padding:0 2px;"),s+=e+":"+t+";"}}),n.fire("AfterPreviewFormats"),Ab.remove(r),s)},Ob=function(e,t,n,r,o){var i=t.get(n);!dm.match(e,n,r,o)||"toggle"in i[0]&&!i[0].toggle?Nb.applyFormat(e,n,r,o):ib(e,n,r,o)},Pb=function(e){e.addShortcut("meta+b","","Bold"),e.addShortcut("meta+i","","Italic"),e.addShortcut("meta+u","","Underline");for(var t=1;t<=6;t++)e.addShortcut("access+"+t,"",["FormatBlock",!1,"h"+t]);e.addShortcut("access+7","",["FormatBlock",!1,"p"]),e.addShortcut("access+8","",["FormatBlock",!1,"div"]),e.addShortcut("access+9","",["FormatBlock",!1,"address"])};function Lb(e){var t,n,r,o=(t=e,n={},(r=function(e,t){e&&("string"!=typeof e?Yt.each(e,function(e,t){r(t,e)}):(t=t.length?t:[t],Yt.each(t,function(e){"undefined"==typeof e.deep&&(e.deep=!e.selector),"undefined"==typeof e.split&&(e.split=!e.selector||e.inline),"undefined"==typeof e.remove&&e.selector&&!e.inline&&(e.remove="none"),e.selector&&e.inline&&(e.mixed=!0,e.block_expand=!0),"string"==typeof e.classes&&(e.classes=e.classes.split(/\s+/))}),n[e]=t))})(kb.get(t.dom)),r(t.settings.formats),{get:function(e){return e?n[e]:n},register:r,unregister:function(e){return e&&n[e]&&delete n[e],n}}),i=Oi(null);return Pb(e),Sm(e),{get:o.get,register:o.register,unregister:o.unregister,apply:d(Nb.applyFormat,e),remove:d(ib,e),toggle:d(Ob,e,o),match:d(dm.match,e),matchAll:d(dm.matchAll,e),matchNode:d(dm.matchNode,e),canApply:d(dm.canApply,e),formatChanged:d(Sb,e,i),getCssText:d(Bb,e)}}var Ib,Mb=Object.prototype.hasOwnProperty,Fb=(Ib=function(e,t){return t},function(){for(var e=new Array(arguments.length),t=0;t<e.length;t++)e[t]=arguments[t];if(0===e.length)throw new Error("Can't merge zero objects");for(var n={},r=0;r<e.length;r++){var o=e[r];for(var i in o)Mb.call(o,i)&&(n[i]=Ib(n[i],o[i]))}return n}),zb={register:function(t,s,c){t.addAttributeFilter("data-mce-tabindex",function(e,t){for(var n,r=e.length;r--;)(n=e[r]).attr("tabindex",n.attributes.map["data-mce-tabindex"]),n.attr(t,null)}),t.addAttributeFilter("src,href,style",function(e,t){for(var n,r,o=e.length,i="data-mce-"+t,a=s.url_converter,u=s.url_converter_scope;o--;)(r=(n=e[o]).attributes.map[i])!==undefined?(n.attr(t,0<r.length?r:null),n.attr(i,null)):(r=n.attributes.map[t],"style"===t?r=c.serializeStyle(c.parseStyle(r),n.name):a&&(r=a.call(u,r,t,n.name)),n.attr(t,0<r.length?r:null))}),t.addAttributeFilter("class",function(e){for(var t,n,r=e.length;r--;)(n=(t=e[r]).attr("class"))&&(n=t.attr("class").replace(/(?:^|\s)mce-item-\w+(?!\S)/g,""),t.attr("class",0<n.length?n:null))}),t.addAttributeFilter("data-mce-type",function(e,t,n){for(var r,o=e.length;o--;)"bookmark"!==(r=e[o]).attributes.map["data-mce-type"]||n.cleanup||r.remove()}),t.addNodeFilter("noscript",function(e){for(var t,n=e.length;n--;)(t=e[n].firstChild)&&(t.value=$o.decode(t.value))}),t.addNodeFilter("script,style",function(e,t){for(var n,r,o,i=e.length,a=function(e){return e.replace(/(<!--\[CDATA\[|\]\]-->)/g,"\n").replace(/^[\r\n]*|[\r\n]*$/g,"").replace(/^\s*((<!--)?(\s*\/\/)?\s*<!\[CDATA\[|(<!--\s*)?\/\*\s*<!\[CDATA\[\s*\*\/|(\/\/)?\s*<!--|\/\*\s*<!--\s*\*\/)\s*[\r\n]*/gi,"").replace(/\s*(\/\*\s*\]\]>\s*\*\/(-->)?|\s*\/\/\s*\]\]>(-->)?|\/\/\s*(-->)?|\]\]>|\/\*\s*-->\s*\*\/|\s*-->\s*)\s*$/g,"")};i--;)r=(n=e[i]).firstChild?n.firstChild.value:"","script"===t?((o=n.attr("type"))&&n.attr("type","mce-no/type"===o?null:o.replace(/^mce\-/,"")),"xhtml"===s.element_format&&0<r.length&&(n.firstChild.value="// <![CDATA[\n"+a(r)+"\n// ]]>")):"xhtml"===s.element_format&&0<r.length&&(n.firstChild.value="\x3c!--\n"+a(r)+"\n--\x3e")}),t.addNodeFilter("#comment",function(e){for(var t,n=e.length;n--;)0===(t=e[n]).value.indexOf("[CDATA[")?(t.name="#cdata",t.type=4,t.value=t.value.replace(/^\[CDATA\[|\]\]$/g,"")):0===t.value.indexOf("mce:protected ")&&(t.name="#text",t.type=3,t.raw=!0,t.value=unescape(t.value).substr(14))}),t.addNodeFilter("xml:namespace,input",function(e,t){for(var n,r=e.length;r--;)7===(n=e[r]).type?n.remove():1===n.type&&("input"!==t||"type"in n.attributes.map||n.attr("type","text"))}),t.addAttributeFilter("data-mce-type",function(e){F(e,function(e){"format-caret"===e.attr("data-mce-type")&&(e.isEmpty(t.schema.getNonEmptyElements())?e.remove():e.unwrap())})}),t.addAttributeFilter("data-mce-src,data-mce-href,data-mce-style,data-mce-selected,data-mce-expando,data-mce-type,data-mce-resize",function(e,t){for(var n=e.length;n--;)e[n].attr(t,null)})},trimTrailingBr:function(e){var t,n,r=function(e){return e&&"br"===e.name};r(t=e.lastChild)&&r(n=t.prev)&&(t.remove(),n.remove())}},Ub={process:function(e,t,n){return f=n,(l=e)&&l.hasEventListeners("PreProcess")&&!f.no_events?(o=t,i=n,c=(r=e).dom,o=o.cloneNode(!0),(a=document.implementation).createHTMLDocument&&(u=a.createHTMLDocument(""),Yt.each("BODY"===o.nodeName?o.childNodes:[o],function(e){u.body.appendChild(u.importNode(e,!0))}),o="BODY"!==o.nodeName?u.body.firstChild:u.body,s=c.doc,c.doc=u),pp(r,Fb(i,{node:o})),s&&(c.doc=s),o):t;var r,o,i,a,u,s,c,l,f}},Vb=function(e,a,u){e.addNodeFilter("font",function(e){F(e,function(e){var t,n=a.parse(e.attr("style")),r=e.attr("color"),o=e.attr("face"),i=e.attr("size");r&&(n.color=r),o&&(n["font-family"]=o),i&&(n["font-size"]=u[parseInt(e.attr("size"),10)-1]),e.name="span",e.attr("style",a.serialize(n)),t=e,F(["color","face","size"],function(e){t.attr(e,null)})})})},Hb=function(e,t){var n,r=oi();t.convert_fonts_to_spans&&Vb(e,r,Yt.explode(t.font_size_legacy_values)),n=r,e.addNodeFilter("strike",function(e){F(e,function(e){var t=n.parse(e.attr("style"));t["text-decoration"]="line-through",e.name="span",e.attr("style",n.serialize(t))})})},jb={register:function(e,t){t.inline_styles&&Hb(e,t)}},qb=/^[ \t\r\n]*$/,$b={"#text":3,"#comment":8,"#cdata":4,"#pi":7,"#doctype":10,"#document-fragment":11},Wb=function(e,t,n){var r,o,i=n?"lastChild":"firstChild",a=n?"prev":"next";if(e[i])return e[i];if(e!==t){if(r=e[a])return r;for(o=e.parent;o&&o!==t;o=o.parent)if(r=o[a])return r}},Kb=function(){function a(e,t){this.name=e,1===(this.type=t)&&(this.attributes=[],this.attributes.map={})}return a.create=function(e,t){var n,r;if(n=new a(e,$b[e]||1),t)for(r in t)n.attr(r,t[r]);return n},a.prototype.replace=function(e){return e.parent&&e.remove(),this.insert(e,this),this.remove(),this},a.prototype.attr=function(e,t){var n,r;if("string"!=typeof e){for(r in e)this.attr(r,e[r]);return this}if(n=this.attributes){if(t!==undefined){if(null===t){if(e in n.map)for(delete n.map[e],r=n.length;r--;)if(n[r].name===e)return n=n.splice(r,1),this;return this}if(e in n.map){for(r=n.length;r--;)if(n[r].name===e){n[r].value=t;break}}else n.push({name:e,value:t});return n.map[e]=t,this}return n.map[e]}},a.prototype.clone=function(){var e,t,n,r,o,i=new a(this.name,this.type);if(n=this.attributes){for((o=[]).map={},e=0,t=n.length;e<t;e++)"id"!==(r=n[e]).name&&(o[o.length]={name:r.name,value:r.value},o.map[r.name]=r.value);i.attributes=o}return i.value=this.value,i.shortEnded=this.shortEnded,i},a.prototype.wrap=function(e){return this.parent.insert(e,this),e.append(this),this},a.prototype.unwrap=function(){var e,t;for(e=this.firstChild;e;)t=e.next,this.insert(e,this,!0),e=t;this.remove()},a.prototype.remove=function(){var e=this.parent,t=this.next,n=this.prev;return e&&(e.firstChild===this?(e.firstChild=t)&&(t.prev=null):n.next=t,e.lastChild===this?(e.lastChild=n)&&(n.next=null):t.prev=n,this.parent=this.next=this.prev=null),this},a.prototype.append=function(e){var t;return e.parent&&e.remove(),(t=this.lastChild)?((t.next=e).prev=t,this.lastChild=e):this.lastChild=this.firstChild=e,e.parent=this,e},a.prototype.insert=function(e,t,n){var r;return e.parent&&e.remove(),r=t.parent||this,n?(t===r.firstChild?r.firstChild=e:t.prev.next=e,e.prev=t.prev,(e.next=t).prev=e):(t===r.lastChild?r.lastChild=e:t.next.prev=e,e.next=t.next,(e.prev=t).next=e),e.parent=r,e},a.prototype.getAll=function(e){var t,n=[];for(t=this.firstChild;t;t=Wb(t,this))t.name===e&&n.push(t);return n},a.prototype.empty=function(){var e,t,n;if(this.firstChild){for(e=[],n=this.firstChild;n;n=Wb(n,this))e.push(n);for(t=e.length;t--;)(n=e[t]).parent=n.firstChild=n.lastChild=n.next=n.prev=null}return this.firstChild=this.lastChild=null,this},a.prototype.isEmpty=function(e,t,n){var r,o,i=this.firstChild;if(t=t||{},i)do{if(1===i.type){if(i.attributes.map["data-mce-bogus"])continue;if(e[i.name])return!1;for(r=i.attributes.length;r--;)if("name"===(o=i.attributes[r].name)||0===o.indexOf("data-mce-bookmark"))return!1}if(8===i.type)return!1;if(3===i.type&&!qb.test(i.value))return!1;if(3===i.type&&i.parent&&t[i.parent.name]&&qb.test(i.value))return!1;if(n&&n(i))return!1}while(i=Wb(i,this));return!0},a.prototype.walk=function(e){return Wb(this,null,e)},a}(),Xb=function(e,t,n,r){(e.padd_empty_with_br||t.insert)&&n[r.name]?r.empty().append(new Kb("br",1)).shortEnded=!0:r.empty().append(new Kb("#text",3)).value="\xa0"},Yb=function(e){return Gb(e,"#text")&&"\xa0"===e.firstChild.value},Gb=function(e,t){return e&&e.firstChild&&e.firstChild===e.lastChild&&e.firstChild.name===t},Jb=function(r,e,t,n){return n.isEmpty(e,t,function(e){return t=e,(n=r.getElementRule(t.name))&&n.paddEmpty;var t,n})},Qb=function(e,t){return e&&(t[e.name]||"br"===e.name)},Zb=function(e,p){var h=e.schema;p.remove_trailing_brs&&e.addNodeFilter("br",function(e,t,n){var r,o,i,a,u,s,c,l,f=e.length,d=Yt.extend({},h.getBlockElements()),m=h.getNonEmptyElements(),g=h.getNonEmptyElements();for(d.body=1,r=0;r<f;r++)if(i=(o=e[r]).parent,d[o.parent.name]&&o===i.lastChild){for(u=o.prev;u;){if("span"!==(s=u.name)||"bookmark"!==u.attr("data-mce-type")){if("br"!==s)break;if("br"===s){o=null;break}}u=u.prev}o&&(o.remove(),Jb(h,m,g,i)&&(c=h.getElementRule(i.name))&&(c.removeEmpty?i.remove():c.paddEmpty&&Xb(p,n,d,i)))}else{for(a=o;i&&i.firstChild===a&&i.lastChild===a&&!d[(a=i).name];)i=i.parent;a===i&&!0!==p.padd_empty_with_br&&((l=new Kb("#text",3)).value="\xa0",o.replace(l))}}),e.addAttributeFilter("href",function(e){var t,n,r,o=e.length;if(!p.allow_unsafe_link_target)for(;o--;)"a"===(t=e[o]).name&&"_blank"===t.attr("target")&&t.attr("rel",(n=t.attr("rel"),r=n?Yt.trim(n):"",/\b(noopener)\b/g.test(r)?r:r.split(" ").filter(function(e){return 0<e.length}).concat(["noopener"]).sort().join(" ")))}),p.allow_html_in_named_anchor||e.addAttributeFilter("id,name",function(e){for(var t,n,r,o,i=e.length;i--;)if("a"===(o=e[i]).name&&o.firstChild&&!o.attr("href"))for(r=o.parent,t=o.lastChild;n=t.prev,r.insert(t,o),t=n;);}),p.fix_list_elements&&e.addNodeFilter("ul,ol",function(e){for(var t,n,r=e.length;r--;)if("ul"===(n=(t=e[r]).parent).name||"ol"===n.name)if(t.prev&&"li"===t.prev.name)t.prev.append(t);else{var o=new Kb("li",1);o.attr("style","list-style-type: none"),t.wrap(o)}}),p.validate&&h.getValidClasses()&&e.addAttributeFilter("class",function(e){for(var t,n,r,o,i,a,u,s=e.length,c=h.getValidClasses();s--;){for(n=(t=e[s]).attr("class").split(" "),i="",r=0;r<n.length;r++)o=n[r],u=!1,(a=c["*"])&&a[o]&&(u=!0),a=c[t.name],!u&&a&&a[o]&&(u=!0),u&&(i&&(i+=" "),i+=o);i.length||(i=null),t.attr("class",i)}})},ey=Yt.makeMap,ty=Yt.each,ny=Yt.explode,ry=Yt.extend;function oy(k,T){void 0===T&&(T=ni());var A={},R=[],_={},D={};(k=k||{}).validate=!("validate"in k)||k.validate,k.root_name=k.root_name||"body";var B=function(e){var t,n,r;(n=e.name)in A&&((r=_[n])?r.push(e):_[n]=[e]),t=R.length;for(;t--;)(n=R[t].name)in e.attributes.map&&((r=D[n])?r.push(e):D[n]=[e]);return e},e={schema:T,addAttributeFilter:function(e,n){ty(ny(e),function(e){var t;for(t=0;t<R.length;t++)if(R[t].name===e)return void R[t].callbacks.push(n);R.push({name:e,callbacks:[n]})})},getAttributeFilters:function(){return[].concat(R)},addNodeFilter:function(e,n){ty(ny(e),function(e){var t=A[e];t||(A[e]=t=[]),t.push(n)})},getNodeFilters:function(){var e=[];for(var t in A)A.hasOwnProperty(t)&&e.push({name:t,callbacks:A[t]});return e},filterNode:B,parse:function(e,a){var t,n,r,o,i,u,s,c,l,f,d,m=[];a=a||{},_={},D={},l=ry(ey("script,style,head,html,body,title,meta,param"),T.getBlockElements());var g=T.getNonEmptyElements(),p=T.children,h=k.validate,v="forced_root_block"in a?a.forced_root_block:k.forced_root_block,b=T.getWhiteSpaceElements(),y=/^[ \t\r\n]+/,C=/[ \t\r\n]+$/,x=/[ \t\r\n]+/g,w=/^[ \t\r\n]+$/;f=b.hasOwnProperty(a.context)||b.hasOwnProperty(k.root_name);var N=function(e,t){var n,r=new Kb(e,t);return e in A&&((n=_[e])?n.push(r):_[e]=[r]),r},E=function(e){var t,n,r,o,i=T.getBlockElements();for(t=e.prev;t&&3===t.type;){if(0<(r=t.value.replace(C,"")).length)return void(t.value=r);if(n=t.next){if(3===n.type&&n.value.length){t=t.prev;continue}if(!i[n.name]&&"script"!==n.name&&"style"!==n.name){t=t.prev;continue}}o=t.prev,t.remove(),t=o}};t=Nv({validate:h,allow_script_urls:k.allow_script_urls,allow_conditional_comments:k.allow_conditional_comments,self_closing_elements:function(e){var t,n={};for(t in e)"li"!==t&&"p"!==t&&(n[t]=e[t]);return n}(T.getSelfClosingElements()),cdata:function(e){d.append(N("#cdata",4)).value=e},text:function(e,t){var n;f||(e=e.replace(x," "),Qb(d.lastChild,l)&&(e=e.replace(y,""))),0!==e.length&&((n=N("#text",3)).raw=!!t,d.append(n).value=e)},comment:function(e){d.append(N("#comment",8)).value=e},pi:function(e,t){d.append(N(e,7)).value=t,E(d)},doctype:function(e){d.append(N("#doctype",10)).value=e,E(d)},start:function(e,t,n){var r,o,i,a,u;if(i=h?T.getElementRule(e):{}){for((r=N(i.outputName||e,1)).attributes=t,r.shortEnded=n,d.append(r),(u=p[d.name])&&p[r.name]&&!u[r.name]&&m.push(r),o=R.length;o--;)(a=R[o].name)in t.map&&((s=D[a])?s.push(r):D[a]=[r]);l[e]&&E(r),n||(d=r),!f&&b[e]&&(f=!0)}},end:function(e){var t,n,r,o,i;if(n=h?T.getElementRule(e):{}){if(l[e]&&!f){if((t=d.firstChild)&&3===t.type)if(0<(r=t.value.replace(y,"")).length)t.value=r,t=t.next;else for(o=t.next,t.remove(),t=o;t&&3===t.type;)r=t.value,o=t.next,(0===r.length||w.test(r))&&(t.remove(),t=o),t=o;if((t=d.lastChild)&&3===t.type)if(0<(r=t.value.replace(C,"")).length)t.value=r,t=t.prev;else for(o=t.prev,t.remove(),t=o;t&&3===t.type;)r=t.value,o=t.prev,(0===r.length||w.test(r))&&(t.remove(),t=o),t=o}if(f&&b[e]&&(f=!1),n.removeEmpty&&Jb(T,g,b,d)&&!d.attributes.map.name&&!d.attr("id"))return i=d.parent,l[d.name]?d.empty().remove():d.unwrap(),void(d=i);n.paddEmpty&&(Yb(d)||Jb(T,g,b,d))&&Xb(k,a,l,d),d=d.parent}}},T);var S=d=new Kb(a.context||k.root_name,11);if(t.parse(e),h&&m.length&&(a.context?a.invalid=!0:function(e){var t,n,r,o,i,a,u,s,c,l,f,d,m,g,p,h;for(d=ey("tr,td,th,tbody,thead,tfoot,table"),l=T.getNonEmptyElements(),f=T.getWhiteSpaceElements(),m=T.getTextBlockElements(),g=T.getSpecialElements(),t=0;t<e.length;t++)if((n=e[t]).parent&&!n.fixed)if(m[n.name]&&"li"===n.parent.name){for(p=n.next;p&&m[p.name];)p.name="li",p.fixed=!0,n.parent.insert(p,n.parent),p=p.next;n.unwrap(n)}else{for(o=[n],r=n.parent;r&&!T.isValidChild(r.name,n.name)&&!d[r.name];r=r.parent)o.push(r);if(r&&1<o.length){for(o.reverse(),i=a=B(o[0].clone()),c=0;c<o.length-1;c++){for(T.isValidChild(a.name,o[c].name)?(u=B(o[c].clone()),a.append(u)):u=a,s=o[c].firstChild;s&&s!==o[c+1];)h=s.next,u.append(s),s=h;a=u}Jb(T,l,f,i)?r.insert(n,o[0],!0):(r.insert(i,o[0],!0),r.insert(n,i)),r=o[0],(Jb(T,l,f,r)||Gb(r,"br"))&&r.empty().remove()}else if(n.parent){if("li"===n.name){if((p=n.prev)&&("ul"===p.name||"ul"===p.name)){p.append(n);continue}if((p=n.next)&&("ul"===p.name||"ul"===p.name)){p.insert(n,p.firstChild,!0);continue}n.wrap(B(new Kb("ul",1)));continue}T.isValidChild(n.parent.name,"div")&&T.isValidChild("div",n.name)?n.wrap(B(new Kb("div",1))):g[n.name]?n.empty().remove():n.unwrap()}}}(m)),v&&("body"===S.name||a.isRootContent)&&function(){var e,t,n=S.firstChild,r=function(e){e&&((n=e.firstChild)&&3===n.type&&(n.value=n.value.replace(y,"")),(n=e.lastChild)&&3===n.type&&(n.value=n.value.replace(C,"")))};if(T.isValidChild(S.name,v.toLowerCase())){for(;n;)e=n.next,3===n.type||1===n.type&&"p"!==n.name&&!l[n.name]&&!n.attr("data-mce-type")?(t||((t=N(v,1)).attr(k.forced_root_block_attrs),S.insert(t,n)),t.append(n)):(r(t),t=null),n=e;r(t)}}(),!a.invalid){for(c in _){for(s=A[c],i=(n=_[c]).length;i--;)n[i].parent||n.splice(i,1);for(r=0,o=s.length;r<o;r++)s[r](n,c,a)}for(r=0,o=R.length;r<o;r++)if((s=R[r]).name in D){for(i=(n=D[s.name]).length;i--;)n[i].parent||n.splice(i,1);for(i=0,u=s.callbacks.length;i<u;i++)s.callbacks[i](n,s.name,a)}}return S}};return Zb(e,k),jb.register(e,k),e}var iy=function(e,t,n){-1===Yt.inArray(t,n)&&(e.addAttributeFilter(n,function(e,t){for(var n=e.length;n--;)e[n].attr(t,null)}),t.push(n))},ay=function(e,t,n){var r=fa(n.getInner?t.innerHTML:e.getOuterHTML(t));return n.selection||yo(rr.fromDom(t))?r:Yt.trim(r)},uy=function(e,t,n){var r=n.selection?Fb({forced_root_block:!1},n):n,o=e.parse(t,r);return zb.trimTrailingBr(o),o},sy=function(e,t,n,r,o){var i,a,u,s,c=(i=r,ul(t,n).serialize(i));return a=e,s=c,!(u=o).no_events&&a?hp(a,Fb(u,{content:s})).content:s};function cy(e,t){var a,u,s,c,l,n,r=(a=e,n=["data-mce-selected"],s=(u=t)&&u.dom?u.dom:pi.DOM,c=u&&u.schema?u.schema:ni(a),a.entity_encoding=a.entity_encoding||"named",a.remove_trailing_brs=!("remove_trailing_brs"in a)||a.remove_trailing_brs,l=oy(a,c),zb.register(l,a,s),{schema:c,addNodeFilter:l.addNodeFilter,addAttributeFilter:l.addAttributeFilter,serialize:function(e,t){var n=Fb({format:"html"},t||{}),r=Ub.process(u,e,n),o=ay(s,r,n),i=uy(l,o,n);return"tree"===n.format?i:sy(u,a,c,i,n)},addRules:function(e){c.addValidElements(e)},setRules:function(e){c.setValidElements(e)},addTempAttr:d(iy,l,n),getTempAttrs:function(){return n}});return{schema:r.schema,addNodeFilter:r.addNodeFilter,addAttributeFilter:r.addAttributeFilter,serialize:r.serialize,addRules:r.addRules,setRules:r.setRules,addTempAttr:r.addTempAttr,getTempAttrs:r.getTempAttrs}}function ly(e){return{getBookmark:d(vc,e),moveToBookmark:d(bc,e)}}(ly||(ly={})).isBookmarkNode=yc;var fy,dy,my=ly,gy=Do.isContentEditableFalse,py=Do.isContentEditableTrue,hy=function(r,a){var u,s,c,l,f,d,m,g,p,h,v,b,i,y,C,x,w,N=a.dom,E=Yt.each,S=a.getDoc(),k=document,T=Math.abs,A=Math.round,R=a.getBody();l={nw:[0,0,-1,-1],ne:[1,0,1,-1],se:[1,1,1,1],sw:[0,1,-1,1]};var e=".mce-content-body";a.contentStyles.push(e+" div.mce-resizehandle {position: absolute;border: 1px solid black;box-sizing: content-box;background: #FFF;width: 7px;height: 7px;z-index: 10000}"+e+" .mce-resizehandle:hover {background: #000}"+e+" img[data-mce-selected],"+e+" hr[data-mce-selected] {outline: 1px solid black;resize: none}"+e+" .mce-clonedresizable {position: absolute;"+(de.gecko?"":"outline: 1px dashed black;")+"opacity: .5;filter: alpha(opacity=50);z-index: 10000}"+e+" .mce-resize-helper {background: #555;background: rgba(0,0,0,0.75);border-radius: 3px;border: 1px;color: white;display: none;font-family: sans-serif;font-size: 12px;white-space: nowrap;line-height: 14px;margin: 5px 10px;padding: 5px;position: absolute;z-index: 10001}");var _=function(e){return e&&("IMG"===e.nodeName||a.dom.is(e,"figure.image"))},n=function(e){var t,n,r=e.target;t=e,n=a.selection.getRng(),!_(t.target)||fv(t.clientX,t.clientY,n)||e.isDefaultPrevented()||(e.preventDefault(),a.selection.select(r))},D=function(e){return a.dom.is(e,"figure.image")?e.querySelector("img"):e},B=function(e){var t=a.settings.object_resizing;return!1!==t&&!de.iOS&&("string"!=typeof t&&(t="table,img,figure.image,div"),"false"!==e.getAttribute("data-mce-resize")&&e!==a.getBody()&&Dr(rr.fromDom(e),t))},O=function(e){var t,n,r,o;t=e.screenX-d,n=e.screenY-m,y=t*f[2]+h,C=n*f[3]+v,y=y<5?5:y,C=C<5?5:C,(_(u)&&!1!==a.settings.resize_img_proportional?!mv.modifierPressed(e):mv.modifierPressed(e)||_(u)&&f[2]*f[3]!=0)&&(T(t)>T(n)?(C=A(y*b),y=A(C/b)):(y=A(C/b),C=A(y*b))),N.setStyles(D(s),{width:y,height:C}),r=0<(r=f.startPos.x+t)?r:0,o=0<(o=f.startPos.y+n)?o:0,N.setStyles(c,{left:r,top:o,display:"block"}),c.innerHTML=y+" &times; "+C,f[2]<0&&s.clientWidth<=y&&N.setStyle(s,"left",g+(h-y)),f[3]<0&&s.clientHeight<=C&&N.setStyle(s,"top",p+(v-C)),(t=R.scrollWidth-x)+(n=R.scrollHeight-w)!=0&&N.setStyles(c,{left:r-t,top:o-n}),i||(yp(a,u,h,v),i=!0)},P=function(){i=!1;var e=function(e,t){t&&(u.style[e]||!a.schema.isValid(u.nodeName.toLowerCase(),e)?N.setStyle(D(u),e,t):N.setAttrib(D(u),e,t))};e("width",y),e("height",C),N.unbind(S,"mousemove",O),N.unbind(S,"mouseup",P),k!==S&&(N.unbind(k,"mousemove",O),N.unbind(k,"mouseup",P)),N.remove(s),N.remove(c),o(u),Cp(a,u,y,C),N.setAttrib(u,"style",N.getAttrib(u,"style")),a.nodeChanged()},o=function(e){var t,r,o,n,i;L(),F(),t=N.getPos(e,R),g=t.x,p=t.y,i=e.getBoundingClientRect(),r=i.width||i.right-i.left,o=i.height||i.bottom-i.top,u!==e&&(u=e,y=C=0),n=a.fire("ObjectSelected",{target:e}),B(e)&&!n.isDefaultPrevented()?E(l,function(n,e){var t;(t=N.get("mceResizeHandle"+e))&&N.remove(t),t=N.add(R,"div",{id:"mceResizeHandle"+e,"data-mce-bogus":"all","class":"mce-resizehandle",unselectable:!0,style:"cursor:"+e+"-resize; margin:0; padding:0"}),11===de.ie&&(t.contentEditable=!1),N.bind(t,"mousedown",function(e){var t;e.stopImmediatePropagation(),e.preventDefault(),d=(t=e).screenX,m=t.screenY,h=D(u).clientWidth,v=D(u).clientHeight,b=v/h,(f=n).startPos={x:r*n[0]+g,y:o*n[1]+p},x=R.scrollWidth,w=R.scrollHeight,s=u.cloneNode(!0),N.addClass(s,"mce-clonedresizable"),N.setAttrib(s,"data-mce-bogus","all"),s.contentEditable=!1,s.unSelectabe=!0,N.setStyles(s,{left:g,top:p,margin:0}),s.removeAttribute("data-mce-selected"),R.appendChild(s),N.bind(S,"mousemove",O),N.bind(S,"mouseup",P),k!==S&&(N.bind(k,"mousemove",O),N.bind(k,"mouseup",P)),c=N.add(R,"div",{"class":"mce-resize-helper","data-mce-bogus":"all"},h+" &times; "+v)}),n.elm=t,N.setStyles(t,{left:r*n[0]+g-t.offsetWidth/2,top:o*n[1]+p-t.offsetHeight/2})}):L(),u.setAttribute("data-mce-selected","1")},L=function(){var e,t;for(e in F(),u&&u.removeAttribute("data-mce-selected"),l)(t=N.get("mceResizeHandle"+e))&&(N.unbind(t),N.remove(t))},I=function(e){var t,n=function(e,t){if(e)do{if(e===t)return!0}while(e=e.parentNode)};i||a.removed||(E(N.select("img[data-mce-selected],hr[data-mce-selected]"),function(e){e.removeAttribute("data-mce-selected")}),t="mousedown"===e.type?e.target:r.getNode(),n(t=N.$(t).closest("table,img,figure.image,hr")[0],R)&&(z(),n(r.getStart(!0),t)&&n(r.getEnd(!0),t))?o(t):L())},M=function(e){return gy(function(e,t){for(;t&&t!==e;){if(py(t)||gy(t))return t;t=t.parentNode}return null}(a.getBody(),e))},F=function(){for(var e in l){var t=l[e];t.elm&&(N.unbind(t.elm),delete t.elm)}},z=function(){try{a.getDoc().execCommand("enableObjectResizing",!1,!1)}catch(e){}};return a.on("init",function(){z(),de.ie&&11<=de.ie&&(a.on("mousedown click",function(e){var t=e.target,n=t.nodeName;i||!/^(TABLE|IMG|HR)$/.test(n)||M(t)||(2!==e.button&&a.selection.select(t,"TABLE"===n),"mousedown"===e.type&&a.nodeChanged())}),a.dom.bind(R,"mscontrolselect",function(e){var t=function(e){ve.setEditorTimeout(a,function(){a.selection.select(e)})};if(M(e.target))return e.preventDefault(),void t(e.target);/^(TABLE|IMG|HR)$/.test(e.target.nodeName)&&(e.preventDefault(),"IMG"===e.target.tagName&&t(e.target))}));var t=ve.throttle(function(e){a.composing||I(e)});a.on("nodechange ResizeEditor ResizeWindow drop FullscreenStateChanged",t),a.on("keyup compositionend",function(e){u&&"TABLE"===u.nodeName&&t(e)}),a.on("hide blur",L),a.on("contextmenu",n)}),a.on("remove",F),{isResizable:B,showResizeRect:o,hideResizeRect:L,updateResizeRect:I,destroy:function(){u=s=null}}},vy=function(e){for(var t=0,n=0,r=e;r&&r.nodeType;)t+=r.offsetLeft||0,n+=r.offsetTop||0,r=r.offsetParent;return{x:t,y:n}},by=function(e,t,n){var r,o,i,a,u,s=e.dom,c=s.getRoot(),l=0;if(u={elm:t,alignToTop:n},e.fire("scrollIntoView",u),!u.isDefaultPrevented()&&Do.isElement(t)){if(!1===n&&(l=t.offsetHeight),"BODY"!==c.nodeName){var f=e.selection.getScrollContainer();if(f)return r=vy(t).y-vy(f).y+l,a=f.clientHeight,void((r<(i=f.scrollTop)||i+a<r+25)&&(f.scrollTop=r<i?r:r-a+25))}o=s.getViewPort(e.getWin()),r=s.getPos(t).y+l,i=o.y,a=o.h,(r<o.y||i+a<r+25)&&e.getWin().scrollTo(0,r<i?r:r-a+25)}},yy=function(d,e){ee(mu.fromRangeStart(e).getClientRects()).each(function(e){var t,n,r,o,i,a,u,s,c,l=function(e){if(e.inline)return e.getBody().getBoundingClientRect();var t=e.getWin();return{left:0,right:t.innerWidth,top:0,bottom:t.innerHeight,width:t.innerWidth,height:t.innerHeight}}(d),f={x:(i=t=l,a=n=e,a.left>i.left&&a.right<i.right?0:a.left<i.left?a.left-i.left:a.right-i.right),y:(r=t,o=n,o.top>r.top&&o.bottom<r.bottom?0:o.top<r.top?o.top-r.top:o.bottom-r.bottom)};s=0!==f.x?0<f.x?f.x+4:f.x-4:0,c=0!==f.y?0<f.y?f.y+4:f.y-4:0,(u=d).inline?(u.getBody().scrollLeft+=s,u.getBody().scrollTop+=c):u.getWin().scrollBy(s,c)})},Cy=function(e){return Do.isContentEditableTrue(e)||Do.isContentEditableFalse(e)},xy=function(e,t,n){var r,o,i,a,u,s=n;if(s.caretPositionFromPoint)(o=s.caretPositionFromPoint(e,t))&&((r=n.createRange()).setStart(o.offsetNode,o.offset),r.collapse(!0));else if(n.caretRangeFromPoint)r=n.caretRangeFromPoint(e,t);else if(s.body.createTextRange){r=s.body.createTextRange();try{r.moveToPoint(e,t),r.collapse(!0)}catch(c){r=function(e,n,t){var r,o,i;if(r=t.elementFromPoint(e,n),o=t.body.createTextRange(),r&&"HTML"!==r.tagName||(r=t.body),o.moveToElementText(r),0<(i=(i=Yt.toArray(o.getClientRects())).sort(function(e,t){return(e=Math.abs(Math.max(e.top-n,e.bottom-n)))-(t=Math.abs(Math.max(t.top-n,t.bottom-n)))})).length){n=(i[0].bottom+i[0].top)/2;try{return o.moveToPoint(e,n),o.collapse(!0),o}catch(a){}}return null}(e,t,n)}return i=r,a=n.body,u=i&&i.parentElement?i.parentElement():null,Do.isContentEditableFalse(function(e,t,n){for(;e&&e!==t;){if(n(e))return e;e=e.parentNode}return null}(u,a,Cy))?null:i}return r},wy=function(n,e){return $(e,function(e){var t=n.fire("GetSelectionRange",{range:e});return t.range!==e?t.range:e})},Ny=function(e,t){var n=(t||document).createDocumentFragment();return F(e,function(e){n.appendChild(e.dom())}),rr.fromDom(n)},Ey=Er("element","width","rows"),Sy=Er("element","cells"),ky=Er("x","y"),Ty=function(e,t){var n=parseInt(yr(e,t),10);return isNaN(n)?1:n},Ay=function(e){return U(e,function(e,t){return t.cells().length>e?t.cells().length:e},0)},Ry=function(e,t){for(var n=e.rows(),r=0;r<n.length;r++)for(var o=n[r].cells(),i=0;i<o.length;i++)if(Or(o[i],t))return A.some(ky(i,r));return A.none()},_y=function(e,t,n,r,o){for(var i=[],a=e.rows(),u=n;u<=o;u++){var s=a[u].cells(),c=t<r?s.slice(t,r+1):s.slice(r,t+1);i.push(Sy(a[u].element(),c))}return i},Dy=function(e){var o=Ey(ia(e),0,[]);return F(Hi(e,"tr"),function(n,r){F(Hi(n,"td,th"),function(e,t){!function(e,t,n,r,o){for(var i=Ty(o,"rowspan"),a=Ty(o,"colspan"),u=e.rows(),s=n;s<n+i;s++){u[s]||(u[s]=Sy(aa(r),[]));for(var c=t;c<t+a;c++)u[s].cells()[c]=s===n&&c===t?o:ia(o)}}(o,function(e,t,n){for(;r=t,o=n,i=void 0,((i=e.rows())[o]?i[o].cells():[])[r];)t++;var r,o,i;return t}(o,t,r),r,n,e)})}),Ey(o.element(),Ay(o.rows()),o.rows())},By=function(e){return n=$((t=e).rows(),function(e){var t=$(e.cells(),function(e){var t=aa(e);return Cr(t,"colspan"),Cr(t,"rowspan"),t}),n=ia(e.element());return Ai(n,t),n}),r=ia(t.element()),o=rr.fromTag("tbody"),Ai(o,n),Ti(r,o),r;var t,n,r,o},Oy=function(l,e,t){return Ry(l,e).bind(function(c){return Ry(l,t).map(function(e){return t=l,r=e,o=(n=c).x(),i=n.y(),a=r.x(),u=r.y(),s=i<u?_y(t,o,i,a,u):_y(t,o,u,a,i),Ey(t.element(),Ay(s),s);var t,n,r,o,i,a,u,s})})},Py=function(n,t){return V(n,function(e){return"li"===ur(e)&&Lh(e,t)}).fold(j([]),function(e){return(t=n,V(t,function(e){return"ul"===ur(e)||"ol"===ur(e)})).map(function(e){return[rr.fromTag("li"),rr.fromTag(ur(e))]}).getOr([]);var t})},Ly=function(e,t){var n,r=rr.fromDom(t.commonAncestorContainer),o=Wl(r,e),i=z(o,function(e){return lo(e)||so(e)}),a=Py(o,t),u=i.concat(a.length?a:po(n=r)?Ir(n).filter(go).fold(j([]),function(e){return[n,e]}):go(n)?[n]:[]);return $(u,ia)},Iy=function(){return Ny([])},My=function(e,t){return n=rr.fromDom(t.cloneContents()),r=Ly(e,t),o=U(r,function(e,t){return Ti(t,e),t},n),0<r.length?Ny([o]):o;var n,r,o},Fy=function(e,o){return(t=e,n=o[0],Ki(n,"table",d(Or,t))).bind(function(e){var t=o[0],n=o[o.length-1],r=Dy(e);return Oy(r,t,n).map(function(e){return Ny([By(e)])})}).getOrThunk(Iy);var t,n},zy=function(e,t){var n,r,o=Xd(t,e);return 0<o.length?Fy(e,o):(n=e,0<(r=t).length&&r[0].collapsed?Iy():My(n,r[0]))},Uy=function(e,t){if(void 0===t&&(t={}),t.get=!0,t.format=t.format||"html",t.selection=!0,(t=e.fire("BeforeGetContent",t)).isDefaultPrevented())return e.fire("GetContent",t),t.content;if("text"===t.format)return c=e,A.from(c.selection.getRng()).map(function(e){return fa(e.toString())}).getOr("");t.getInner=!0;var n,r,o,i,a,u,s,c,l=(r=t,i=(n=e).selection.getRng(),a=n.dom.create("body"),u=n.selection.getSel(),s=wy(n,Hd(u)),i.cloneContents?(o=r.contextual?zy(rr.fromDom(n.getBody()),s).dom():i.cloneContents())&&a.appendChild(o):a.innerHTML=i.toString(),n.selection.serializer.serialize(a,r));return"tree"===t.format?l:(t.content=e.selection.isCollapsed()?"":l,e.fire("GetContent",t),t.content)},Vy=function(e,t,n){var r,o,i,a=e.selection.getRng(),u=e.getDoc();if((n=n||{format:"html"}).set=!0,n.selection=!0,n.content=t,n.no_events||!(n=e.fire("BeforeSetContent",n)).isDefaultPrevented()){if(t=n.content,a.insertNode){t+='<span id="__caret">_</span>',a.startContainer===u&&a.endContainer===u?u.body.innerHTML=t:(a.deleteContents(),0===u.body.childNodes.length?u.body.innerHTML=t:a.createContextualFragment?a.insertNode(a.createContextualFragment(t)):(o=u.createDocumentFragment(),i=u.createElement("div"),o.appendChild(i),i.outerHTML=t,a.insertNode(o))),r=e.dom.get("__caret"),(a=u.createRange()).setStartBefore(r),a.setEndBefore(r),e.selection.setRng(a),e.dom.remove("__caret");try{e.selection.setRng(a)}catch(s){}}else a.item&&(u.execCommand("Delete",!1,null),a=e.getRng()),/^\s+/.test(t)?(a.pasteHTML('<span id="__mce_tmp">_</span>'+t),e.dom.remove("__mce_tmp")):a.pasteHTML(t);n.no_events||e.fire("SetContent",n)}else e.fire("SetContent",n)},Hy=function(e,t,n,r,o){var i=n?t.startContainer:t.endContainer,a=n?t.startOffset:t.endOffset;return A.from(i).map(rr.fromDom).map(function(e){return r&&t.collapsed?e:Hr(e,o(e,a)).getOr(e)}).bind(function(e){return cr(e)?A.some(e):Ir(e)}).map(function(e){return e.dom()}).getOr(e)},jy=function(e,t,n){return Hy(e,t,!0,n,function(e,t){return Math.min(e.dom().childNodes.length,t)})},qy=function(e,t,n){return Hy(e,t,!1,n,function(e,t){return 0<t?t-1:t})},$y=function(e,t){for(var n=e;e&&Do.isText(e)&&0===e.length;)e=t?e.nextSibling:e.previousSibling;return e||n},Wy=Yt.each,Ky=function(e){return!!e.select},Xy=function(e){return!(!e||!e.ownerDocument)&&Pr(rr.fromDom(e.ownerDocument),rr.fromDom(e))},Yy=function(u,s,e,c){var n,t,l,f,a,r=function(e,t){return Vy(c,e,t)},o=function(e){var t=m();t.collapse(!!e),i(t)},d=function(){return s.getSelection?s.getSelection():s.document.selection},m=function(){var e,t,n,r,o=function(e,t,n){try{return t.compareBoundaryPoints(e,n)}catch(r){return-1}};if(!s)return null;if(null==(r=s.document))return null;if(c.bookmark!==undefined&&!1===$p(c)){var i=tp(c);if(i.isSome())return i.map(function(e){return wy(c,[e])[0]}).getOr(r.createRange())}try{(e=d())&&(t=0<e.rangeCount?e.getRangeAt(0):e.createRange?e.createRange():r.createRange())}catch(a){}return(t=wy(c,[t])[0])||(t=r.createRange?r.createRange():r.body.createTextRange()),t.setStart&&9===t.startContainer.nodeType&&t.collapsed&&(n=u.getRoot(),t.setStart(n,0),t.setEnd(n,0)),l&&f&&(0===o(t.START_TO_START,t,l)&&0===o(t.END_TO_END,t,l)?t=f:f=l=null),t},i=function(e,t){var n,r;if((o=e)&&(Ky(o)||Xy(o.startContainer)&&Xy(o.endContainer))){var o,i=Ky(e)?e:null;if(i){f=null;try{i.select()}catch(a){}}else{if(n=d(),e=c.fire("SetSelectionRange",{range:e,forward:t}).range,n){f=e;try{n.removeAllRanges(),n.addRange(e)}catch(a){}!1===t&&n.extend&&(n.collapse(e.endContainer,e.endOffset),n.extend(e.startContainer,e.startOffset)),l=0<n.rangeCount?n.getRangeAt(0):null}e.collapsed||e.startContainer!==e.endContainer||!n.setBaseAndExtent||de.ie||e.endOffset-e.startOffset<2&&e.startContainer.hasChildNodes()&&(r=e.startContainer.childNodes[e.startOffset])&&"IMG"===r.tagName&&(n.setBaseAndExtent(e.startContainer,e.startOffset,e.endContainer,e.endOffset),n.anchorNode===e.startContainer&&n.focusNode===e.endContainer||n.setBaseAndExtent(r,0,r,1)),c.fire("AfterSetSelectionRange",{range:e,forward:t})}}},g=function(){var e,t,n=d();return!(n&&n.anchorNode&&n.focusNode)||((e=u.createRng()).setStart(n.anchorNode,n.anchorOffset),e.collapse(!0),(t=u.createRng()).setStart(n.focusNode,n.focusOffset),t.collapse(!0),e.compareBoundaryPoints(e.START_TO_START,t)<=0)},p={bookmarkManager:null,controlSelection:null,dom:u,win:s,serializer:e,editor:c,collapse:o,setCursorLocation:function(e,t){var n=u.createRng();e?(n.setStart(e,t),n.setEnd(e,t),i(n),o(!1)):(Ih(u,n,c.getBody(),!0),i(n))},getContent:function(e){return Uy(c,e)},setContent:r,getBookmark:function(e,t){return n.getBookmark(e,t)},moveToBookmark:function(e){return n.moveToBookmark(e)},select:function(e,t){var r,n,o;return(r=u,n=e,o=t,A.from(n).map(function(e){var t=r.nodeIndex(e),n=r.createRng();return n.setStart(e.parentNode,t),n.setEnd(e.parentNode,t+1),o&&(Ih(r,n,e,!0),Ih(r,n,e,!1)),n})).each(i),e},isCollapsed:function(){var e=m(),t=d();return!(!e||e.item)&&(e.compareEndPoints?0===e.compareEndPoints("StartToEnd",e):!t||e.collapsed)},isForward:g,setNode:function(e){return r(u.getOuterHTML(e)),e},getNode:function(){return e=c.getBody(),(t=m())?(r=t.startContainer,o=t.endContainer,i=t.startOffset,a=t.endOffset,n=t.commonAncestorContainer,!t.collapsed&&(r===o&&a-i<2&&r.hasChildNodes()&&(n=r.childNodes[i]),3===r.nodeType&&3===o.nodeType&&(r=r.length===i?$y(r.nextSibling,!0):r.parentNode,o=0===a?$y(o.previousSibling,!1):o.parentNode,r&&r===o))?r:n&&3===n.nodeType?n.parentNode:n):e;var e,t,n,r,o,i,a},getSel:d,setRng:i,getRng:m,getStart:function(e){return jy(c.getBody(),m(),e)},getEnd:function(e){return qy(c.getBody(),m(),e)},getSelectedBlocks:function(e,t){return function(e,t,n,r){var o,i,a=[];if(i=e.getRoot(),n=e.getParent(n||jy(i,t,t.collapsed),e.isBlock),r=e.getParent(r||qy(i,t,t.collapsed),e.isBlock),n&&n!==i&&a.push(n),n&&r&&n!==r)for(var u=new ro(o=n,i);(o=u.next())&&o!==r;)e.isBlock(o)&&a.push(o);return r&&n!==r&&r!==i&&a.push(r),a}(u,m(),e,t)},normalize:function(){var e=m(),t=d();if(!qd(t)&&Mh(c)){var n=Ag(u,e);return n.each(function(e){i(e,g())}),n.getOr(e)}return e},selectorChanged:function(e,t){var i;return a||(a={},i={},c.on("NodeChange",function(e){var n=e.element,r=u.getParents(n,null,u.getRoot()),o={};Wy(a,function(e,n){Wy(r,function(t){if(u.is(t,n))return i[n]||(Wy(e,function(e){e(!0,{node:t,selector:n,parents:r})}),i[n]=e),o[n]=e,!1})}),Wy(i,function(e,t){o[t]||(delete i[t],Wy(e,function(e){e(!1,{node:n,selector:t,parents:r})}))})})),a[e]||(a[e]=[]),a[e].push(t),p},getScrollContainer:function(){for(var e,t=u.getRoot();t&&"BODY"!==t.nodeName;){if(t.scrollHeight>t.clientHeight){e=t;break}t=t.parentNode}return e},scrollIntoView:function(e,t){return by(c,e,t)},placeCaretAt:function(e,t){return i(xy(e,t,c.getDoc()))},getBoundingClientRect:function(){var e=m();return e.collapsed?vu.fromRangeStart(e).getClientRects()[0]:e.getBoundingClientRect()},destroy:function(){s=l=f=null,t.destroy()}};return n=my(p),t=hy(p,c),p.bookmarkManager=n,p.controlSelection=t,p},Gy=Do.isContentEditableFalse,Jy=Va,Qy=_s,Zy=Rs,eC=function(e,t,n,r){var o=e===pu.Forwards,i=o?Zy:Qy;if(!r.collapsed){var a=Jy(r);if(Gy(a))return Om(e,t,a,e===pu.Backwards,!0)}var u=ga(r.startContainer),s=As(e,t.getBody(),r);if(i(s))return Pm(t,s.getNode(!o));var c=Al.normalizePosition(o,n(s));if(!c)return u?r:null;if(i(c))return Om(e,t,c.getNode(!o),o,!0);var l=n(c);return l&&i(l)&&Fs(c,l)?Om(e,t,l.getNode(!o),o,!0):u?Im(t,c.toRange(),!0):null},tC=function(e,t,n,r){var o,i,a,u,s,c,l,f,d;if(d=Jy(r),o=As(e,t.getBody(),r),i=n(t.getBody(),tv(1),o),a=z(i,nv(1)),s=qt.last(o.getClientRects()),(Zy(o)||Ds(o))&&(d=o.getNode()),(Qy(o)||Bs(o))&&(d=o.getNode(!0)),!s)return null;if(c=s.left,(u=sv(a,c))&&Gy(u.node))return l=Math.abs(c-u.left),f=Math.abs(c-u.right),Om(e,t,u.node,l<f,!0);if(d){var m=function(e,t,n,r){var o,i,a,u,s,c,l=ec(t),f=[],d=0,m=function(e){return qt.last(e.getClientRects())};1===e?(o=l.next,i=Ua,a=za,u=vu.after(r)):(o=l.prev,i=za,a=Ua,u=vu.before(r)),c=m(u);do{if(u.isVisible()&&!a(s=m(u),c)){if(0<f.length&&i(s,qt.last(f))&&d++,(s=Ia(s)).position=u,s.line=d,n(s))return f;f.push(s)}}while(u=o(u));return f}(e,t.getBody(),tv(1),d);if(u=sv(z(m,nv(1)),c))return Im(t,u.position.toRange(),!0);if(u=qt.last(z(m,nv(0))))return Im(t,u.position.toRange(),!0)}},nC=function(e,t,n){var r,o,i,a,u=ec(e.getBody()),s=d(Ms,u.next),c=d(Ms,u.prev);if(n.collapsed&&e.settings.forced_root_block){if(!(r=e.dom.getParent(n.startContainer,"PRE")))return;(1===t?s(vu.fromRangeStart(n)):c(vu.fromRangeStart(n)))||(a=(i=e).dom.create(i.settings.forced_root_block),(!de.ie||11<=de.ie)&&(a.innerHTML='<br data-mce-bogus="1">'),o=a,1===t?e.$(r).after(o):e.$(r).before(o),e.selection.select(o,!0),e.selection.collapse())}},rC=function(l,f){return function(){var e,t,n,r,o,i,a,u,s,c=(t=f,r=ec((e=l).getBody()),o=d(Ms,r.next),i=d(Ms,r.prev),a=t?pu.Forwards:pu.Backwards,u=t?o:i,s=e.selection.getRng(),(n=eC(a,e,u,s))?n:(n=nC(e,a,s))||null);return!!c&&(l.selection.setRng(c),!0)}},oC=function(u,s){return function(){var e,t,n,r,o,i,a=(r=(t=s)?1:-1,o=t?ev:Zh,i=(e=u).selection.getRng(),(n=tC(r,e,o,i))?n:(n=nC(e,r,i))||null);return!!a&&(u.selection.setRng(a),!0)}};(dy=fy||(fy={}))[dy.Br=0]="Br",dy[dy.Block=1]="Block",dy[dy.Wrap=2]="Wrap",dy[dy.Eol=3]="Eol";var iC=function(e,t){return e===pu.Backwards?t.reverse():t},aC=function(e,t,n,r){for(var o,i,a,u,s,c,l=ec(n),f=r,d=[];f&&(s=l,c=f,o=t===pu.Forwards?s.next(c):s.prev(c));){if(Do.isBr(o.getNode(!1)))return t===pu.Forwards?{positions:iC(t,d).concat([o]),breakType:fy.Br,breakAt:A.some(o)}:{positions:iC(t,d),breakType:fy.Br,breakAt:A.some(o)};if(o.isVisible()){if(e(f,o)){var m=(i=t,a=f,u=o,Do.isBr(u.getNode(i===pu.Forwards))?fy.Br:!1===bs(a,u)?fy.Block:fy.Wrap);return{positions:iC(t,d),breakType:m,breakAt:A.some(o)}}d.push(o),f=o}else f=o}return{positions:iC(t,d),breakType:fy.Eol,breakAt:A.none()}},uC=function(n,r,o,e){return r(o,e).breakAt.map(function(e){var t=r(o,e).positions;return n===pu.Backwards?t.concat(e):[e].concat(t)}).getOr([])},sC=function(e,i){return U(e,function(e,o){return e.fold(function(){return A.some(o)},function(r){return $a([ee(r.getClientRects()),ee(o.getClientRects())],function(e,t){var n=Math.abs(i-e.left);return Math.abs(i-t.left)<=n?o:r}).or(e)})},A.none())},cC=function(t,e){return ee(e.getClientRects()).bind(function(e){return sC(t,e.left)})},lC=d(aC,mu.isAbove,-1),fC=d(aC,mu.isBelow,1),dC=d(uC,-1,lC),mC=d(uC,1,fC),gC=function(e,t,n,r,o){var i,a,u,s,c=Hi(rr.fromDom(n),"td,th,caption").map(function(e){return e.dom()}),l=z((i=e,G(c,function(e){var t,n,r=(t=Ia(e.getBoundingClientRect()),n=-1,{left:t.left-n,top:t.top-n,right:t.right+2*n,bottom:t.bottom+2*n,width:t.width+n,height:t.height+n});return[{x:r.left,y:i(r),cell:e},{x:r.right,y:i(r),cell:e}]})),function(e){return t(e,o)});return(a=l,u=r,s=o,U(a,function(e,r){return e.fold(function(){return A.some(r)},function(e){var t=Math.sqrt(Math.abs(e.x-u)+Math.abs(e.y-s)),n=Math.sqrt(Math.abs(r.x-u)+Math.abs(r.y-s));return A.some(n<t?r:e)})},A.none())).map(function(e){return e.cell})},pC=d(gC,function(e){return e.bottom},function(e,t){return e.y<t}),hC=d(gC,function(e){return e.top},function(e,t){return e.y>t}),vC=function(t,n){return ee(n.getClientRects()).bind(function(e){return pC(t,e.left,e.top)}).bind(function(e){return cC((t=e,cc.lastPositionIn(t).map(function(e){return lC(t,e).positions.concat(e)}).getOr([])),n);var t})},bC=function(t,n){return te(n.getClientRects()).bind(function(e){return hC(t,e.left,e.top)}).bind(function(e){return cC((t=e,cc.firstPositionIn(t).map(function(e){return[e].concat(fC(t,e).positions)}).getOr([])),n);var t})},yC=function(e,t){e.selection.setRng(t),yy(e,t)},CC=function(e,t,n){var r,o,i,a,u=e(t,n);return(a=u).breakType===fy.Wrap&&0===a.positions.length||!Do.isBr(n.getNode())&&(i=u).breakType===fy.Br&&1===i.positions.length?(r=e,o=t,!u.breakAt.map(function(e){return r(o,e).breakAt.isSome()}).getOr(!1)):u.breakAt.isNone()},xC=d(CC,lC),wC=d(CC,fC),NC=function(e,t,n,r){var o,i,a,u,s=e.selection.getRng(),c=t?1:-1;if(ns()&&(o=t,i=s,a=n,u=vu.fromRangeStart(i),cc.positionIn(!o,a).map(function(e){return e.isEqual(u)}).getOr(!1))){var l=Om(c,e,n,!t,!0);return yC(e,l),!0}return!1},EC=function(e,t){var n=t.getNode(e);return Do.isElement(n)&&"TABLE"===n.nodeName?A.some(n):A.none()},SC=function(u,s,c){var e=EC(!!s,c),t=!1===s;e.fold(function(){return yC(u,c.toRange())},function(a){return cc.positionIn(t,u.getBody()).filter(function(e){return e.isEqual(c)}).fold(function(){return yC(u,c.toRange())},function(e){return n=s,o=a,t=c,void((i=Qm(r=u))?r.undoManager.transact(function(){var e=rr.fromTag(i);br(e,Zm(r)),Ti(e,rr.fromTag("br")),n?Si(rr.fromDom(o),e):Ei(rr.fromDom(o),e);var t=r.dom.createRng();t.setStart(e.dom(),0),t.setEnd(e.dom(),0),yC(r,t)}):yC(r,t.toRange()));var n,r,o,t,i})})},kC=function(e,t,n,r){var o,i,a,u,s,c,l=e.selection.getRng(),f=vu.fromRangeStart(l),d=e.getBody();if(!t&&xC(r,f)){var m=(u=d,vC(s=n,c=f).orThunk(function(){return ee(c.getClientRects()).bind(function(e){return sC(dC(u,vu.before(s)),e.left)})}).getOr(vu.before(s)));return SC(e,t,m),!0}return!(!t||!wC(r,f))&&(o=d,m=bC(i=n,a=f).orThunk(function(){return ee(a.getClientRects()).bind(function(e){return sC(mC(o,vu.after(i)),e.left)})}).getOr(vu.after(i)),SC(e,t,m),!0)},TC=function(t,n){return function(){return A.from(t.dom.getParent(t.selection.getNode(),"td,th")).bind(function(e){return A.from(t.dom.getParent(e,"table")).map(function(e){return NC(t,n,e)})}).getOr(!1)}},AC=function(n,r){return function(){return A.from(n.dom.getParent(n.selection.getNode(),"td,th")).bind(function(t){return A.from(n.dom.getParent(t,"table")).map(function(e){return kC(n,r,e,t)})}).getOr(!1)}},RC=function(e){return M(["figcaption"],ur(e))},_C=function(e){var t=document.createRange();return t.setStartBefore(e.dom()),t.setEndBefore(e.dom()),t},DC=function(e,t,n){n?Ti(e,t):ki(e,t)},BC=function(e,t,n,r){return""===t?(l=e,f=r,d=rr.fromTag("br"),DC(l,d,f),_C(d)):(o=e,i=r,a=t,u=n,s=rr.fromTag(a),c=rr.fromTag("br"),br(s,u),Ti(s,c),DC(o,s,i),_C(c));var o,i,a,u,s,c,l,f,d},OC=function(e,t,n){return t?(o=e.dom(),fC(o,n).breakAt.isNone()):(r=e.dom(),lC(r,n).breakAt.isNone());var r,o},PC=function(t,n){var e,r,o,i=rr.fromDom(t.getBody()),a=vu.fromRangeStart(t.selection.getRng()),u=Qm(t),s=Zm(t);return(e=a,r=i,o=d(Or,r),Wi(rr.fromDom(e.container()),co,o).filter(RC)).exists(function(){if(OC(i,n,a)){var e=BC(i,u,s,n);return t.selection.setRng(e),!0}return!1})},LC=function(e,t){return function(){return!!e.selection.isCollapsed()&&PC(e,t)}},IC=function(e,r){return G($(e,function(e){return Fb({shiftKey:!1,altKey:!1,ctrlKey:!1,metaKey:!1,keyCode:0,action:o},e)}),function(e){return t=e,(n=r).keyCode===t.keyCode&&n.shiftKey===t.shiftKey&&n.altKey===t.altKey&&n.ctrlKey===t.ctrlKey&&n.metaKey===t.metaKey?[e]:[];var t,n})},MC=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var r=Array.prototype.slice.call(arguments,1);return function(){return e.apply(null,r)}},FC=function(e,t){return V(IC(e,t),function(e){return e.action()})},zC=function(i,a){i.on("keydown",function(e){var t,n,r,o;!1===e.isDefaultPrevented()&&(t=i,n=a,r=e,o=tr.detect().os,FC([{keyCode:mv.RIGHT,action:rC(t,!0)},{keyCode:mv.LEFT,action:rC(t,!1)},{keyCode:mv.UP,action:oC(t,!1)},{keyCode:mv.DOWN,action:oC(t,!0)},{keyCode:mv.RIGHT,action:TC(t,!0)},{keyCode:mv.LEFT,action:TC(t,!1)},{keyCode:mv.UP,action:AC(t,!1)},{keyCode:mv.DOWN,action:AC(t,!0)},{keyCode:mv.RIGHT,action:wd.move(t,n,!0)},{keyCode:mv.LEFT,action:wd.move(t,n,!1)},{keyCode:mv.RIGHT,ctrlKey:!o.isOSX(),altKey:o.isOSX(),action:wd.moveNextWord(t,n)},{keyCode:mv.LEFT,ctrlKey:!o.isOSX(),altKey:o.isOSX(),action:wd.movePrevWord(t,n)},{keyCode:mv.UP,action:LC(t,!1)},{keyCode:mv.DOWN,action:LC(t,!0)}],r).each(function(e){r.preventDefault()}))})},UC=function(o,i){o.on("keydown",function(e){var t,n,r;!1===e.isDefaultPrevented()&&(t=o,n=i,r=e,FC([{keyCode:mv.BACKSPACE,action:MC(Pf,t,!1)},{keyCode:mv.DELETE,action:MC(Pf,t,!0)},{keyCode:mv.BACKSPACE,action:MC(Um,t,!1)},{keyCode:mv.DELETE,action:MC(Um,t,!0)},{keyCode:mv.BACKSPACE,action:MC(kd,t,n,!1)},{keyCode:mv.DELETE,action:MC(kd,t,n,!0)},{keyCode:mv.BACKSPACE,action:MC(im,t,!1)},{keyCode:mv.DELETE,action:MC(im,t,!0)},{keyCode:mv.BACKSPACE,action:MC(af,t,!1)},{keyCode:mv.DELETE,action:MC(af,t,!0)},{keyCode:mv.BACKSPACE,action:MC(tf,t,!1)},{keyCode:mv.DELETE,action:MC(tf,t,!0)},{keyCode:mv.BACKSPACE,action:MC(_m,t,!1)},{keyCode:mv.DELETE,action:MC(_m,t,!0)}],r).each(function(e){r.preventDefault()}))}),o.on("keyup",function(e){var t,n;!1===e.isDefaultPrevented()&&(t=o,n=e,FC([{keyCode:mv.BACKSPACE,action:MC(Lf,t)},{keyCode:mv.DELETE,action:MC(Lf,t)}],n))})},VC=function(e){return A.from(e.dom.getParent(e.selection.getStart(!0),e.dom.isBlock))},HC=function(e,t){var n,r,o,i=t,a=e.dom,u=e.schema.getMoveCaretBeforeOnEnterElements();if(t){if(/^(LI|DT|DD)$/.test(t.nodeName)){var s=function(e){for(;e;){if(1===e.nodeType||3===e.nodeType&&e.data&&/[\r\n\s]/.test(e.data))return e;e=e.nextSibling}}(t.firstChild);s&&/^(UL|OL|DL)$/.test(s.nodeName)&&t.insertBefore(a.doc.createTextNode("\xa0"),t.firstChild)}if(o=a.createRng(),t.normalize(),t.hasChildNodes()){for(n=new ro(t,t);r=n.current();){if(Do.isText(r)){o.setStart(r,0),o.setEnd(r,0);break}if(u[r.nodeName.toLowerCase()]){o.setStartBefore(r),o.setEndBefore(r);break}i=r,r=n.next()}r||(o.setStart(i,0),o.setEnd(i,0))}else Do.isBr(t)?t.nextSibling&&a.isBlock(t.nextSibling)?(o.setStartBefore(t),o.setEndBefore(t)):(o.setStartAfter(t),o.setEndAfter(t)):(o.setStart(t,0),o.setEnd(t,0));e.selection.setRng(o),a.remove(void 0),e.selection.scrollIntoView(t)}},jC=function(e,t){var n,r,o=e.getRoot();for(n=t;n!==o&&"false"!==e.getContentEditable(n);)"true"===e.getContentEditable(n)&&(r=n),n=n.parentNode;return n!==o?r:o},qC=VC,$C=function(e){return VC(e).fold(j(""),function(e){return e.nodeName.toUpperCase()})},WC=function(e){return VC(e).filter(function(e){return po(rr.fromDom(e))}).isSome()},KC=function(e,t){return e&&e.parentNode&&e.parentNode.nodeName===t},XC=function(e){return e&&/^(OL|UL|LI)$/.test(e.nodeName)},YC=function(e){var t=e.parentNode;return/^(LI|DT|DD)$/.test(t.nodeName)?t:e},GC=function(e,t,n){for(var r=e[n?"firstChild":"lastChild"];r&&!Do.isElement(r);)r=r[n?"nextSibling":"previousSibling"];return r===t},JC=function(e,t,n,r,o){var i=e.dom,a=e.selection.getRng();if(n!==e.getBody()){var u;XC(u=n)&&XC(u.parentNode)&&(o="LI");var s,c,l=o?t(o):i.create("BR");if(GC(n,r,!0)&&GC(n,r,!1))KC(n,"LI")?i.insertAfter(l,YC(n)):i.replace(l,n);else if(GC(n,r,!0))KC(n,"LI")?(i.insertAfter(l,YC(n)),l.appendChild(i.doc.createTextNode(" ")),l.appendChild(n)):n.parentNode.insertBefore(l,n);else if(GC(n,r,!1))i.insertAfter(l,YC(n));else{n=YC(n);var f=a.cloneRange();f.setStartAfter(r),f.setEndAfter(n);var d=f.extractContents();"LI"===o&&(c="LI",(s=d).firstChild&&s.firstChild.nodeName===c)?(l=d.firstChild,i.insertAfter(d,n)):(i.insertAfter(d,n),i.insertAfter(l,n))}i.remove(r),HC(e,l)}},QC=function(e){e.innerHTML='<br data-mce-bogus="1">'},ZC=function(e,t){return e.nodeName===t||e.previousSibling&&e.previousSibling.nodeName===t},ex=function(e,t){return t&&e.isBlock(t)&&!/^(TD|TH|CAPTION|FORM)$/.test(t.nodeName)&&!/^(fixed|absolute)/i.test(t.style.position)&&"true"!==e.getContentEditable(t)},tx=function(e,t,n){return!1===Do.isText(t)?n:e?1===n&&t.data.charAt(n-1)===la?0:n:n===t.data.length-1&&t.data.charAt(n)===la?t.data.length:n},nx=function(e,t){var n,r,o=e.getRoot();for(n=t;n!==o&&"false"!==e.getContentEditable(n);)"true"===e.getContentEditable(n)&&(r=n),n=n.parentNode;return n!==o?r:o},rx=function(e,t){var n=Qm(e);n&&n.toLowerCase()===t.tagName.toLowerCase()&&e.dom.setAttribs(t,Zm(e))},ox=function(a,e){var t,u,s,i,c,n,r,o,l,f,d,m,g,p,h,v,b,y,C,x=a.dom,w=a.schema,N=w.getNonEmptyElements(),E=a.selection.getRng(),S=function(e){var t,n,r,o=s,i=w.getTextInlineElements();if(e||"TABLE"===f||"HR"===f?(t=x.create(e||m),rx(a,t)):t=c.cloneNode(!1),r=t,!1===ng(a))x.setAttrib(t,"style",null),x.setAttrib(t,"class",null);else do{if(i[o.nodeName]){if(Uu(o))continue;n=o.cloneNode(!1),x.setAttrib(n,"id",""),t.hasChildNodes()?n.appendChild(t.firstChild):r=n,t.appendChild(n)}}while((o=o.parentNode)&&o!==u);return QC(r),t},k=function(e){var t,n,r,o;if(o=tx(e,s,i),Do.isText(s)&&(e?0<o:o<s.nodeValue.length))return!1;if(s.parentNode===c&&g&&!e)return!0;if(e&&Do.isElement(s)&&s===c.firstChild)return!0;if(ZC(s,"TABLE")||ZC(s,"HR"))return g&&!e||!g&&e;for(t=new ro(s,c),Do.isText(s)&&(e&&0===o?t.prev():e||o!==s.nodeValue.length||t.next());n=t.current();){if(Do.isElement(n)){if(!n.getAttribute("data-mce-bogus")&&(r=n.nodeName.toLowerCase(),N[r]&&"br"!==r))return!1}else if(Do.isText(n)&&!/^[ \t\r\n]*$/.test(n.nodeValue))return!1;e?t.prev():t.next()}return!0},T=function(){r=/^(H[1-6]|PRE|FIGURE)$/.test(f)&&"HGROUP"!==d?S(m):S(),rg(a)&&ex(x,l)&&x.isEmpty(c)?r=x.split(l,c):x.insertAfter(r,c),HC(a,r)};Ag(x,E).each(function(e){E.setStart(e.startContainer,e.startOffset),E.setEnd(e.endContainer,e.endOffset)}),s=E.startContainer,i=E.startOffset,m=Qm(a),n=e.shiftKey,Do.isElement(s)&&s.hasChildNodes()&&(g=i>s.childNodes.length-1,s=s.childNodes[Math.min(i,s.childNodes.length-1)]||s,i=g&&Do.isText(s)?s.nodeValue.length:0),(u=nx(x,s))&&((m&&!n||!m&&n)&&(s=function(e,t,n,r,o){var i,a,u,s,c,l,f,d=t||"P",m=e.dom,g=nx(m,r);if(!(a=m.getParent(r,m.isBlock))||!ex(m,a)){if(l=(a=a||g)===e.getBody()||(f=a)&&/^(TD|TH|CAPTION)$/.test(f.nodeName)?a.nodeName.toLowerCase():a.parentNode.nodeName.toLowerCase(),!a.hasChildNodes())return i=m.create(d),rx(e,i),a.appendChild(i),n.setStart(i,0),n.setEnd(i,0),i;for(s=r;s.parentNode!==a;)s=s.parentNode;for(;s&&!m.isBlock(s);)s=(u=s).previousSibling;if(u&&e.schema.isValidChild(l,d.toLowerCase())){for(i=m.create(d),rx(e,i),u.parentNode.insertBefore(i,u),s=u;s&&!m.isBlock(s);)c=s.nextSibling,i.appendChild(s),s=c;n.setStart(r,o),n.setEnd(r,o)}}return r}(a,m,E,s,i)),c=x.getParent(s,x.isBlock),l=c?x.getParent(c.parentNode,x.isBlock):null,f=c?c.nodeName.toUpperCase():"","LI"!==(d=l?l.nodeName.toUpperCase():"")||e.ctrlKey||(l=(c=l).parentNode,f=d),/^(LI|DT|DD)$/.test(f)&&x.isEmpty(c)?JC(a,S,l,c,m):m&&c===a.getBody()||(m=m||"P",ga(c)?(r=Na(c),x.isEmpty(c)&&QC(c),HC(a,r)):k()?T():k(!0)?(r=c.parentNode.insertBefore(S(),c),HC(a,ZC(c,"HR")?r:c)):((t=(y=E,C=y.cloneRange(),C.setStart(y.startContainer,tx(!0,y.startContainer,y.startOffset)),C.setEnd(y.endContainer,tx(!1,y.endContainer,y.endOffset)),C).cloneRange()).setEndAfter(c),o=t.extractContents(),b=o,F(Vi(rr.fromDom(b),lr),function(e){var t=e.dom();t.nodeValue=fa(t.nodeValue)}),function(e){for(;Do.isText(e)&&(e.nodeValue=e.nodeValue.replace(/^[\r\n]+/,"")),e=e.firstChild;);}(o),r=o.firstChild,x.insertAfter(o,c),function(e,t,n){var r,o=n,i=[];if(o){for(;o=o.firstChild;){if(e.isBlock(o))return;Do.isElement(o)&&!t[o.nodeName.toLowerCase()]&&i.push(o)}for(r=i.length;r--;)!(o=i[r]).hasChildNodes()||o.firstChild===o.lastChild&&""===o.firstChild.nodeValue?e.remove(o):(a=e,(u=o)&&"A"===u.nodeName&&a.isEmpty(u)&&e.remove(o));var a,u}}(x,N,r),p=x,(h=c).normalize(),(v=h.lastChild)&&!/^(left|right)$/gi.test(p.getStyle(v,"float",!0))||p.add(h,"br"),x.isEmpty(c)&&QC(c),r.normalize(),x.isEmpty(r)?(x.remove(r),T()):HC(a,r)),x.setAttrib(r,"id",""),a.fire("NewBlock",{newBlock:r})))},ix=function(e,t){return qC(e).filter(function(e){return 0<t.length&&Dr(rr.fromDom(e),t)}).isSome()},ax=function(e){return ix(e,eg(e))},ux=function(e){return ix(e,tg(e))},sx=uf([{br:[]},{block:[]},{none:[]}]),cx=function(e,t){return ux(e)},lx=function(n){return function(e,t){return""===Qm(e)===n}},fx=function(n){return function(e,t){return WC(e)===n}},dx=function(n,r){return function(e,t){return $C(e)===n.toUpperCase()===r}},mx=function(e){return dx("pre",e)},gx=function(n){return function(e,t){return Jm(e)===n}},px=function(e,t){return ax(e)},hx=function(e,t){return t},vx=function(e){var t=Qm(e),n=jC(e.dom,e.selection.getStart());return n&&e.schema.isValidChild(n.nodeName,t||"P")},bx=function(e,t){return function(n,r){return U(e,function(e,t){return e&&t(n,r)},!0)?A.some(t):A.none()}},yx=function(e,t){return Wf([bx([cx],sx.none()),bx([dx("summary",!0)],sx.br()),bx([mx(!0),gx(!1),hx],sx.br()),bx([mx(!0),gx(!1)],sx.block()),bx([mx(!0),gx(!0),hx],sx.block()),bx([mx(!0),gx(!0)],sx.br()),bx([fx(!0),hx],sx.br()),bx([fx(!0)],sx.block()),bx([lx(!0),hx,vx],sx.block()),bx([lx(!0)],sx.br()),bx([px],sx.br()),bx([lx(!1),hx],sx.br()),bx([vx],sx.block())],[e,t.shiftKey]).getOr(sx.none())},Cx=function(e,t){yx(e,t).fold(function(){Fg(e,t)},function(){ox(e,t)},o)},xx=function(o){o.on("keydown",function(e){var t,n,r;e.keyCode===mv.ENTER&&(t=o,(n=e).isDefaultPrevented()||(n.preventDefault(),(r=t.undoManager).typing&&(r.typing=!1,r.add()),t.undoManager.transact(function(){!1===t.selection.isCollapsed()&&t.execCommand("Delete"),Cx(t,n)})))})},wx=function(n,r){var e=r.container(),t=r.offset();return Do.isText(e)?(e.insertData(t,n),A.some(mu(e,t+n.length))):Is(r).map(function(e){var t=rr.fromText(n);return r.isAtEnd()?Si(e,t):Ei(e,t),mu(t.dom(),n.length)})},Nx=d(wx,"\xa0"),Ex=d(wx," "),Sx=function(t,n,r){var e=z(Wl(rr.fromDom(r.container()),n),co);return ee(e).fold(function(){return cc.navigate(t,n.dom(),r).forall(function(e){return!1===bs(e,r,n.dom())})},function(e){return cc.navigate(t,e.dom(),r).isNone()})},kx=d(Sx,!1),Tx=d(Sx,!0),Ax=function(e){return mu.isTextPosition(e)&&!e.isAtStart()&&!e.isAtEnd()},Rx=function(e,t){var n=z(Wl(rr.fromDom(t.container()),e),co);return ee(n).getOr(e)},_x=function(e,t){return Ax(t)?Ls(t):Ls(t)||cc.prevPosition(Rx(e,t).dom(),t).exists(Ls)},Dx=function(e,t){return Ax(t)?Ps(t):Ps(t)||cc.nextPosition(Rx(e,t).dom(),t).exists(Ps)},Bx=function(e){return Is(e).bind(function(e){return Wi(e,cr)}).exists(function(e){return t=wr(e,"white-space"),M(["pre","pre-line","pre-wrap"],t);var t})},Ox=function(e,t){return o=e,i=t,cc.prevPosition(o.dom(),i).isNone()||(n=e,r=t,cc.nextPosition(n.dom(),r).isNone())||kx(e,t)||Tx(e,t)||ff(e,t)||lf(e,t);var n,r,o,i},Px=function(e,t){return os(e.charAt(t))},Lx=function(e){var t=e.container();return Do.isText(t)&&Wn(t.data,"\xa0")},Ix=function(e,t,n){var r,o,i=mu(t,0);return Px(n,0)&&(r=e,Bx(o=i)||!(kx(r,o)||ff(r,o)||_x(r,o)))?" "+n.slice(1):n},Mx=function(e,t,n){var r,o,i=mu(t,n.length);return Px(n,n.length-1)&&(r=e,Bx(o=i)||!(Tx(r,o)||lf(r,o)||Dx(r,o)))?n.slice(0,-1)+" ":n},Fx=function(i,e){return A.some(e).filter(Lx).bind(function(e){var t,n=e.container(),r=n.nodeValue,o=Ix(i,n,(t=Mx(i,n,r),$(t.split(""),function(e,t,n){return os(e)&&0<t&&t<n.length-1&&as(n[t-1])&&as(n[t+1])?" ":e}).join("")));return r!==o?(e.container().nodeValue=o,A.some(e)):A.none()})},zx=function(t){var e=rr.fromDom(t.getBody());t.selection.isCollapsed()&&Fx(e,mu.fromRangeStart(t.selection.getRng())).each(function(e){t.selection.setRng(e.toRange())})},Ux=function(r,o){return function(e){return t=r,!Bx(n=e)&&(Ox(t,n)||_x(t,n)||Dx(t,n))?Nx(o):Ex(o);var t,n}},Vx=function(e){var t,n,r=vu.fromRangeStart(e.selection.getRng()),o=rr.fromDom(e.getBody());if(e.selection.isCollapsed()){var i=d(Al.isInlineTarget,e),a=vu.fromRangeStart(e.selection.getRng());return ld(i,e.getBody(),a).bind((n=o,function(e){return e.fold(function(e){return cc.prevPosition(n.dom(),vu.before(e))},function(e){return cc.firstPositionIn(e)},function(e){return cc.lastPositionIn(e)},function(e){return cc.nextPosition(n.dom(),vu.after(e))})})).bind(Ux(o,r)).exists((t=e,function(e){return t.selection.setRng(e.toRange()),t.nodeChanged(),!0}))}return!1},Hx=function(r){r.on("keydown",function(e){var t,n;!1===e.isDefaultPrevented()&&(t=r,n=e,FC([{keyCode:mv.SPACEBAR,action:MC(Vx,t)}],n).each(function(e){n.preventDefault()}))})},jx=function(e,t){var n;t.hasAttribute("data-mce-caret")&&(Na(t),(n=e).selection.setRng(n.selection.getRng()),e.selection.scrollIntoView(t))},qx=function(e,t){var n,r=(n=e,Xi(rr.fromDom(n.getBody()),"*[data-mce-caret]").fold(j(null),function(e){return e.dom()}));if(r)return"compositionstart"===t.type?(t.preventDefault(),t.stopPropagation(),void jx(e,r)):void(va(r)&&(jx(e,r),e.undoManager.add()))},$x=function(e){e.on("keyup compositionstart",d(qx,e))},Wx=tr.detect().browser,Kx=function(t){var e,n;e=t,n=Bi(function(){e.composing||zx(e)},0),Wx.isIE()&&(e.on("keypress",function(e){n.throttle()}),e.on("remove",function(e){n.cancel()})),t.on("input",function(e){!1===e.isComposing&&zx(t)})},Xx=function(e){var t=wd.setupSelectedState(e);$x(e),zC(e,t),UC(e,t),xx(e),Hx(e),Kx(e)};function Yx(u){var s,n,r,o=Yt.each,c=mv.BACKSPACE,l=mv.DELETE,f=u.dom,d=u.selection,e=u.settings,t=u.parser,i=de.gecko,a=de.ie,m=de.webkit,g="data:text/mce-internal,",p=a?"Text":"URL",h=function(e,t){try{u.getDoc().execCommand(e,!1,t)}catch(n){}},v=function(e){return e.isDefaultPrevented()},b=function(){u.shortcuts.add("meta+a",null,"SelectAll")},y=function(){u.on("keydown",function(e){if(!v(e)&&e.keyCode===c&&d.isCollapsed()&&0===d.getRng().startOffset){var t=d.getNode().previousSibling;if(t&&t.nodeName&&"table"===t.nodeName.toLowerCase())return e.preventDefault(),!1}})},C=function(){u.inline||(u.contentStyles.push("body {min-height: 150px}"),u.on("click",function(e){var t;if("HTML"===e.target.nodeName){if(11<de.ie)return void u.getBody().focus();t=u.selection.getRng(),u.getBody().focus(),u.selection.setRng(t),u.selection.normalize(),u.nodeChanged()}}))};return u.on("keydown",function(e){var t,n,r,o,i;if(!v(e)&&e.keyCode===mv.BACKSPACE&&(n=(t=d.getRng()).startContainer,r=t.startOffset,o=f.getRoot(),i=n,t.collapsed&&0===r)){for(;i&&i.parentNode&&i.parentNode.firstChild===i&&i.parentNode!==o;)i=i.parentNode;"BLOCKQUOTE"===i.tagName&&(u.formatter.toggle("blockquote",null,i),(t=f.createRng()).setStart(n,0),t.setEnd(n,0),d.setRng(t))}}),s=function(e){var t=f.create("body"),n=e.cloneContents();return t.appendChild(n),d.serializer.serialize(t,{format:"html"})},u.on("keydown",function(e){var t,n,r,o,i,a=e.keyCode;if(!v(e)&&(a===l||a===c)){if(t=u.selection.isCollapsed(),n=u.getBody(),t&&!f.isEmpty(n))return;if(!t&&(r=u.selection.getRng(),o=s(r),(i=f.createRng()).selectNode(u.getBody()),o!==s(i)))return;e.preventDefault(),u.setContent(""),n.firstChild&&f.isBlock(n.firstChild)?u.selection.setCursorLocation(n.firstChild,0):u.selection.setCursorLocation(n,0),u.nodeChanged()}}),de.windowsPhone||u.on("keyup focusin mouseup",function(e){mv.modifierPressed(e)||d.normalize()},!0),m&&(u.settings.content_editable||f.bind(u.getDoc(),"mousedown mouseup",function(e){var t;if(e.target===u.getDoc().documentElement)if(t=d.getRng(),u.getBody().focus(),"mousedown"===e.type){if(ha(t.startContainer))return;d.placeCaretAt(e.clientX,e.clientY)}else d.setRng(t)}),u.on("click",function(e){var t=e.target;/^(IMG|HR)$/.test(t.nodeName)&&"false"!==f.getContentEditableParent(t)&&(e.preventDefault(),u.selection.select(t),u.nodeChanged()),"A"===t.nodeName&&f.hasClass(t,"mce-item-anchor")&&(e.preventDefault(),d.select(t))}),e.forced_root_block&&u.on("init",function(){h("DefaultParagraphSeparator",e.forced_root_block)}),u.on("init",function(){u.dom.bind(u.getBody(),"submit",function(e){e.preventDefault()})}),y(),t.addNodeFilter("br",function(e){for(var t=e.length;t--;)"Apple-interchange-newline"===e[t].attr("class")&&e[t].remove()}),de.iOS?(u.inline||u.on("keydown",function(){document.activeElement===document.body&&u.getWin().focus()}),C(),u.on("click",function(e){var t=e.target;do{if("A"===t.tagName)return void e.preventDefault()}while(t=t.parentNode)}),u.contentStyles.push(".mce-content-body {-webkit-touch-callout: none}")):b()),11<=de.ie&&(C(),y()),de.ie&&(b(),h("AutoUrlDetect",!1),u.on("dragstart",function(e){var t,n,r;(t=e).dataTransfer&&(u.selection.isCollapsed()&&"IMG"===t.target.tagName&&d.select(t.target),0<(n=u.selection.getContent()).length&&(r=g+escape(u.id)+","+escape(n),t.dataTransfer.setData(p,r)))}),u.on("drop",function(e){if(!v(e)){var t=(i=e).dataTransfer&&(a=i.dataTransfer.getData(p))&&0<=a.indexOf(g)?(a=a.substr(g.length).split(","),{id:unescape(a[0]),html:unescape(a[1])}):null;if(t&&t.id!==u.id){e.preventDefault();var n=xy(e.x,e.y,u.getDoc());d.setRng(n),r=t.html,o=!0,u.queryCommandSupported("mceInsertClipboardContent")?u.execCommand("mceInsertClipboardContent",!1,{content:r,internal:o}):u.execCommand("mceInsertContent",!1,r)}}var r,o,i,a})),i&&(u.on("keydown",function(e){if(!v(e)&&e.keyCode===c){if(!u.getBody().getElementsByTagName("hr").length)return;if(d.isCollapsed()&&0===d.getRng().startOffset){var t=d.getNode(),n=t.previousSibling;if("HR"===t.nodeName)return f.remove(t),void e.preventDefault();n&&n.nodeName&&"hr"===n.nodeName.toLowerCase()&&(f.remove(n),e.preventDefault())}}}),Range.prototype.getClientRects||u.on("mousedown",function(e){if(!v(e)&&"HTML"===e.target.nodeName){var t=u.getBody();t.blur(),ve.setEditorTimeout(u,function(){t.focus()})}}),n=function(){var e=f.getAttribs(d.getStart().cloneNode(!1));return function(){var t=d.getStart();t!==u.getBody()&&(f.setAttrib(t,"style",null),o(e,function(e){t.setAttributeNode(e.cloneNode(!0))}))}},r=function(){return!d.isCollapsed()&&f.getParent(d.getStart(),f.isBlock)!==f.getParent(d.getEnd(),f.isBlock)},u.on("keypress",function(e){var t;if(!v(e)&&(8===e.keyCode||46===e.keyCode)&&r())return t=n(),u.getDoc().execCommand("delete",!1,null),t(),e.preventDefault(),!1}),f.bind(u.getDoc(),"cut",function(e){var t;!v(e)&&r()&&(t=n(),ve.setEditorTimeout(u,function(){t()}))}),e.readonly||u.on("BeforeExecCommand MouseDown",function(){h("StyleWithCSS",!1),h("enableInlineTableEditing",!1),e.object_resizing||h("enableObjectResizing",!1)}),u.on("SetContent ExecCommand",function(e){"setcontent"!==e.type&&"mceInsertLink"!==e.command||o(f.select("a"),function(e){var t=e.parentNode,n=f.getRoot();if(t.lastChild===e){for(;t&&!f.isBlock(t);){if(t.parentNode.lastChild!==t||t===n)return;t=t.parentNode}f.add(t,"br",{"data-mce-bogus":1})}})}),u.contentStyles.push("img:-moz-broken {-moz-force-broken-image-icon:1;min-width:24px;min-height:24px}"),de.mac&&u.on("keydown",function(e){!mv.metaKeyPressed(e)||e.shiftKey||37!==e.keyCode&&39!==e.keyCode||(e.preventDefault(),u.selection.getSel().modify("move",37===e.keyCode?"backward":"forward","lineboundary"))}),y()),{refreshContentEditable:function(){},isHidden:function(){var e;return!i||u.removed?0:!(e=u.selection.getSel())||!e.rangeCount||0===e.rangeCount}}}var Gx=function(e){return Do.isElement(e)&&mo(rr.fromDom(e))},Jx=function(t){t.on("click",function(e){3<=e.detail&&function(e){var t=e.selection.getRng(),n=mu.fromRangeStart(t),r=mu.fromRangeEnd(t);if(mu.isElementPosition(n)){var o=n.container();Gx(o)&&cc.firstPositionIn(o).each(function(e){return t.setStart(e.container(),e.offset())})}mu.isElementPosition(r)&&(o=n.container(),Gx(o)&&cc.lastPositionIn(o).each(function(e){return t.setEnd(e.container(),e.offset())})),e.selection.setRng(cl(t))}(t)})},Qx=function(e){var t,n;(t=e).on("click",function(e){t.dom.getParent(e.target,"details")&&e.preventDefault()}),(n=e).parser.addNodeFilter("details",function(e){F(e,function(e){e.attr("data-mce-open",e.attr("open")),e.attr("open","open")})}),n.serializer.addNodeFilter("details",function(e){F(e,function(e){var t=e.attr("data-mce-open");e.attr("open",R(t)?t:null),e.attr("data-mce-open",null)})})},Zx=pi.DOM,ew=function(e){var t;e.bindPendingEventDelegates(),e.initialized=!0,e.fire("init"),e.focus(!0),e.nodeChanged({initial:!0}),e.execCallback("init_instance_callback",e),(t=e).settings.auto_focus&&ve.setEditorTimeout(t,function(){var e;(e=!0===t.settings.auto_focus?t:t.editorManager.get(t.settings.auto_focus)).destroyed||e.focus()},100)},tw=function(t,e){var n,r,u,o,i,a,s,c,l,f=t.settings,d=t.getElement(),m=t.getDoc();f.inline||(t.getElement().style.visibility=t.orgVisibility),e||f.content_editable||(m.open(),m.write(t.iframeHTML),m.close()),f.content_editable&&(t.on("remove",function(){var e=this.getBody();Zx.removeClass(e,"mce-content-body"),Zx.removeClass(e,"mce-edit-focus"),Zx.setAttrib(e,"contentEditable",null)}),Zx.addClass(d,"mce-content-body"),t.contentDocument=m=f.content_document||document,t.contentWindow=f.content_window||window,t.bodyElement=d,f.content_document=f.content_window=null,f.root_name=d.nodeName.toLowerCase()),(n=t.getBody()).disabled=!0,t.readonly=f.readonly,t.readonly||(t.inline&&"static"===Zx.getStyle(n,"position",!0)&&(n.style.position="relative"),n.contentEditable=t.getParam("content_editable_state",!0)),n.disabled=!1,t.editorUpload=Th(t),t.schema=ni(f),t.dom=pi(m,{keep_values:!0,url_converter:t.convertURL,url_converter_scope:t,hex_colors:f.force_hex_style_colors,class_filter:f.class_filter,update_styles:!0,root_element:t.inline?t.getBody():null,collect:f.content_editable,schema:t.schema,contentCssCors:gg(t),onSetAttrib:function(e){t.fire("SetAttrib",e)}}),t.parser=((o=oy((u=t).settings,u.schema)).addAttributeFilter("src,href,style,tabindex",function(e,t){for(var n,r,o,i=e.length,a=u.dom;i--;)if(r=(n=e[i]).attr(t),o="data-mce-"+t,!n.attributes.map[o]){if(0===r.indexOf("data:")||0===r.indexOf("blob:"))continue;"style"===t?((r=a.serializeStyle(a.parseStyle(r),n.name)).length||(r=null),n.attr(o,r),n.attr(t,r)):"tabindex"===t?(n.attr(o,r),n.attr(t,null)):n.attr(o,u.convertURL(r,t,n.name))}}),o.addNodeFilter("script",function(e){for(var t,n,r=e.length;r--;)0!==(n=(t=e[r]).attr("type")||"no/type").indexOf("mce-")&&t.attr("type","mce-"+n)}),o.addNodeFilter("#cdata",function(e){for(var t,n=e.length;n--;)(t=e[n]).type=8,t.name="#comment",t.value="[CDATA["+t.value+"]]"}),o.addNodeFilter("p,h1,h2,h3,h4,h5,h6,div",function(e){for(var t,n=e.length,r=u.schema.getNonEmptyElements();n--;)(t=e[n]).isEmpty(r)&&0===t.getAll("br").length&&(t.append(new Kb("br",1)).shortEnded=!0)}),o),t.serializer=cy(f,t),t.selection=Yy(t.dom,t.getWin(),t.serializer,t),t.annotator=qc(t),t.formatter=Lb(t),t.undoManager=Hv(t),t._nodeChangeDispatcher=new Fh(t),t._selectionOverrides=yv(t),Qx(t),Jx(t),Xx(t),Bh(t),t.fire("PreInit"),f.browser_spellcheck||f.gecko_spellcheck||(m.body.spellcheck=!1,Zx.setAttrib(n,"spellcheck","false")),t.quirks=Yx(t),t.fire("PostRender"),f.directionality&&(n.dir=f.directionality),f.nowrap&&(n.style.whiteSpace="nowrap"),f.protect&&t.on("BeforeSetContent",function(t){Yt.each(f.protect,function(e){t.content=t.content.replace(e,function(e){return"\x3c!--mce:protected "+escape(e)+"--\x3e"})})}),t.on("SetContent",function(){t.addVisual(t.getBody())}),t.load({initial:!0,format:"html"}),t.startContent=t.getContent({format:"raw"}),t.on("compositionstart compositionend",function(e){t.composing="compositionstart"===e.type}),0<t.contentStyles.length&&(r="",Yt.each(t.contentStyles,function(e){r+=e+"\r\n"}),t.dom.addStyle(r)),(i=t,i.inline?Zx.styleSheetLoader:i.dom.styleSheetLoader).loadAll(t.contentCSS,function(e){ew(t)},function(e){ew(t)}),f.content_style&&(a=t,s=f.content_style,c=rr.fromDom(a.getDoc().head),l=rr.fromTag("style"),vr(l,"type","text/css"),Ti(l,rr.fromText(s)),Ti(c,l))},nw=pi.DOM,rw=function(e,t){var n,r,o,i,a,u,s,c=e.editorManager.translate("Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help"),l=(n=e.id,r=c,o=t.height,i=$m(e),s=rr.fromTag("iframe"),br(s,i),br(s,{id:n+"_ifr",frameBorder:"0",allowTransparency:"true",title:r}),xr(s,{width:"100%",height:(a=o,u="number"==typeof a?a+"px":a,u||""),display:"block"}),s).dom();l.onload=function(){l.onload=null,e.fire("load")};var f,d,m,g,p=function(e,t){if(document.domain!==window.location.hostname&&de.ie&&de.ie<12){var n=kh.uuid("mce");e[n]=function(){tw(e)};var r='javascript:(function(){document.open();document.domain="'+document.domain+'";var ed = window.parent.tinymce.get("'+e.id+'");document.write(ed.iframeHTML);document.close();ed.'+n+"(true);})()";return nw.setAttrib(t,"src",r),!0}return!1}(e,l);return e.contentAreaContainer=t.iframeContainer,e.iframeElement=l,e.iframeHTML=(g=Wm(f=e)+"<html><head>",Km(f)!==f.documentBaseUrl&&(g+='<base href="'+f.documentBaseURI.getURI()+'" />'),g+='<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />',d=Xm(f),m=Ym(f),Gm(f)&&(g+='<meta http-equiv="Content-Security-Policy" content="'+Gm(f)+'" />'),g+='</head><body id="'+d+'" class="mce-content-body '+m+'" data-id="'+f.id+'"><br></body></html>'),nw.add(t.iframeContainer,l),p},ow=function(e,t){var n=rw(e,t);t.editorContainer&&(nw.get(t.editorContainer).style.display=e.orgDisplay,e.hidden=nw.isHidden(t.editorContainer)),e.getElement().style.display="none",nw.setAttrib(e.id,"aria-hidden","true"),n||tw(e)},iw=pi.DOM,aw=function(t,n,e){var r=fh.get(e),o=fh.urls[e]||t.documentBaseUrl.replace(/\/$/,"");if(e=Yt.trim(e),r&&-1===Yt.inArray(n,e)){if(Yt.each(fh.dependencies(e),function(e){aw(t,n,e)}),t.plugins[e])return;try{var i=new r(t,o,t.$);(t.plugins[e]=i).init&&(i.init(t,o),n.push(e))}catch(VN){lh.pluginInitError(t,e,VN)}}},uw=function(e){return e.replace(/^\-/,"")},sw=function(e){return{editorContainer:e,iframeContainer:e}},cw=function(e){var t,n,r=e.getElement();return e.inline?sw(null):(t=r,n=iw.create("div"),iw.insertAfter(n,t),sw(n))},lw=function(e){var t,n,r,o,i,a,u,s,c,l,f,d=e.settings,m=e.getElement();return e.orgDisplay=m.style.display,R(d.theme)?(l=(o=e).settings,f=o.getElement(),i=l.width||iw.getStyle(f,"width")||"100%",a=l.height||iw.getStyle(f,"height")||f.offsetHeight,u=l.min_height||100,(s=/^[0-9\.]+(|px)$/i).test(""+i)&&(i=Math.max(parseInt(i,10),100)),s.test(""+a)&&(a=Math.max(parseInt(a,10),u)),c=o.theme.renderUI({targetNode:f,width:i,height:a,deltaWidth:l.delta_width,deltaHeight:l.delta_height}),l.content_editable||(a=(c.iframeHeight||a)+("number"==typeof a?c.deltaHeight||0:""))<u&&(a=u),c.height=a,c):P(d.theme)?(r=(t=e).getElement(),(n=t.settings.theme(t,r)).editorContainer.nodeType&&(n.editorContainer.id=n.editorContainer.id||t.id+"_parent"),n.iframeContainer&&n.iframeContainer.nodeType&&(n.iframeContainer.id=n.iframeContainer.id||t.id+"_iframecontainer"),n.height=n.iframeHeight?n.iframeHeight:r.offsetHeight,n):cw(e)},fw=function(t){var e,n,r,o,i,a,u=t.settings,s=t.getElement();return t.rtl=u.rtl_ui||t.editorManager.i18n.rtl,t.editorManager.i18n.setCode(u.language),u.aria_label=u.aria_label||iw.getAttrib(s,"aria-label",t.getLang("aria.rich_text_area")),t.fire("ScriptsLoaded"),o=(n=t).settings.theme,R(o)?(n.settings.theme=uw(o),r=dh.get(o),n.theme=new r(n,dh.urls[o]),n.theme.init&&n.theme.init(n,dh.urls[o]||n.documentBaseUrl.replace(/\/$/,""),n.$)):n.theme={},i=t,a=[],Yt.each(i.settings.plugins.split(/[ ,]/),function(e){aw(i,a,uw(e))}),e=lw(t),t.editorContainer=e.editorContainer?e.editorContainer:null,u.content_css&&Yt.each(Yt.explode(u.content_css),function(e){t.contentCSS.push(t.documentBaseURI.toAbsolute(e))}),u.content_editable?tw(t):ow(t,e)},dw=pi.DOM,mw=function(e){return"-"===e.charAt(0)},gw=function(i,a){var u=Ci.ScriptLoader;!function(e,t,n,r){var o=t.settings,i=o.theme;if(R(i)){if(!mw(i)&&!dh.urls.hasOwnProperty(i)){var a=o.theme_url;a?dh.load(i,t.documentBaseURI.toAbsolute(a)):dh.load(i,"themes/"+i+"/theme"+n+".js")}e.loadQueue(function(){dh.waitFor(i,r)})}else r()}(u,i,a,function(){var e,t,n,r,o;e=u,(n=(t=i).settings).language&&"en"!==n.language&&!n.language_url&&(n.language_url=t.editorManager.baseURL+"/langs/"+n.language+".js"),n.language_url&&!t.editorManager.i18n.data[n.language]&&e.add(n.language_url),r=i.settings,o=a,Yt.isArray(r.plugins)&&(r.plugins=r.plugins.join(" ")),Yt.each(r.external_plugins,function(e,t){fh.load(t,e),r.plugins+=" "+t}),Yt.each(r.plugins.split(/[ ,]/),function(e){if((e=Yt.trim(e))&&!fh.urls[e])if(mw(e)){e=e.substr(1,e.length);var t=fh.dependencies(e);Yt.each(t,function(e){var t={prefix:"plugins/",resource:e,suffix:"/plugin"+o+".js"};e=fh.createUrl(t,e),fh.load(e.resource,e)})}else fh.load(e,{prefix:"plugins/",resource:e,suffix:"/plugin"+o+".js"})}),u.loadQueue(function(){i.removed||fw(i)},i,function(e){lh.pluginLoadError(i,e[0]),i.removed||fw(i)})})},pw=function(t){var e=t.settings,n=t.id,r=function(){dw.unbind(window,"ready",r),t.render()};if(ke.Event.domLoaded){if(t.getElement()&&de.contentEditable){e.inline?t.inline=!0:(t.orgVisibility=t.getElement().style.visibility,t.getElement().style.visibility="hidden");var o=t.getElement().form||dw.getParent(n,"form");o&&(t.formElement=o,e.hidden_input&&!/TEXTAREA|INPUT/i.test(t.getElement().nodeName)&&(dw.insertAfter(dw.create("input",{type:"hidden",name:n}),n),t.hasHiddenInput=!0),t.formEventDelegate=function(e){t.fire(e.type,e)},dw.bind(o,"submit reset",t.formEventDelegate),t.on("reset",function(){t.setContent(t.startContent,{format:"raw"})}),!e.submit_patch||o.submit.nodeType||o.submit.length||o._mceOldSubmit||(o._mceOldSubmit=o.submit,o.submit=function(){return t.editorManager.triggerSave(),t.setDirty(!1),o._mceOldSubmit(o)})),t.windowManager=th(t),t.notificationManager=eh(t),"xml"===e.encoding&&t.on("GetContent",function(e){e.save&&(e.content=dw.encode(e.content))}),e.add_form_submit_trigger&&t.on("submit",function(){t.initialized&&t.save()}),e.add_unload_trigger&&(t._beforeUnload=function(){!t.initialized||t.destroyed||t.isHidden()||t.save({format:"raw",no_events:!0,set_dirty:!1})},t.editorManager.on("BeforeUnload",t._beforeUnload)),t.editorManager.add(t),gw(t,t.suffix)}}else dw.bind(window,"ready",r)},hw=function(e,t,n){var r=e.sidebars?e.sidebars:[];r.push({name:t,settings:n}),e.sidebars=r},vw=Yt.each,bw=Yt.trim,yw="source protocol authority userInfo user password host port relative path directory file query anchor".split(" "),Cw={ftp:21,http:80,https:443,mailto:25},xw=function(r,e){var t,n,o=this;if(r=bw(r),t=(e=o.settings=e||{}).base_uri,/^([\w\-]+):([^\/]{2})/i.test(r)||/^\s*#/.test(r))o.source=r;else{var i=0===r.indexOf("//");0!==r.indexOf("/")||i||(r=(t&&t.protocol||"http")+"://mce_host"+r),/^[\w\-]*:?\/\//.test(r)||(n=e.base_uri?e.base_uri.path:new xw(document.location.href).directory,""==e.base_uri.protocol?r="//mce_host"+o.toAbsPath(n,r):(r=/([^#?]*)([#?]?.*)/.exec(r),r=(t&&t.protocol||"http")+"://mce_host"+o.toAbsPath(n,r[1])+r[2])),r=r.replace(/@@/g,"(mce_at)"),r=/^(?:(?![^:@]+:[^:@\/]*@)([^:\/?#.]+):)?(?:\/\/)?((?:(([^:@\/]*):?([^:@\/]*))?@)?([^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/.exec(r),vw(yw,function(e,t){var n=r[t];n&&(n=n.replace(/\(mce_at\)/g,"@@")),o[e]=n}),t&&(o.protocol||(o.protocol=t.protocol),o.userInfo||(o.userInfo=t.userInfo),o.port||"mce_host"!==o.host||(o.port=t.port),o.host&&"mce_host"!==o.host||(o.host=t.host),o.source=""),i&&(o.protocol="")}};xw.prototype={setPath:function(e){e=/^(.*?)\/?(\w+)?$/.exec(e),this.path=e[0],this.directory=e[1],this.file=e[2],this.source="",this.getURI()},toRelative:function(e){var t;if("./"===e)return e;if("mce_host"!==(e=new xw(e,{base_uri:this})).host&&this.host!==e.host&&e.host||this.port!==e.port||this.protocol!==e.protocol&&""!==e.protocol)return e.getURI();var n=this.getURI(),r=e.getURI();return n===r||"/"===n.charAt(n.length-1)&&n.substr(0,n.length-1)===r?n:(t=this.toRelPath(this.path,e.path),e.query&&(t+="?"+e.query),e.anchor&&(t+="#"+e.anchor),t)},toAbsolute:function(e,t){return(e=new xw(e,{base_uri:this})).getURI(t&&this.isSameOrigin(e))},isSameOrigin:function(e){if(this.host==e.host&&this.protocol==e.protocol){if(this.port==e.port)return!0;var t=Cw[this.protocol];if(t&&(this.port||t)==(e.port||t))return!0}return!1},toRelPath:function(e,t){var n,r,o,i=0,a="";if(e=(e=e.substring(0,e.lastIndexOf("/"))).split("/"),n=t.split("/"),e.length>=n.length)for(r=0,o=e.length;r<o;r++)if(r>=n.length||e[r]!==n[r]){i=r+1;break}if(e.length<n.length)for(r=0,o=n.length;r<o;r++)if(r>=e.length||e[r]!==n[r]){i=r+1;break}if(1===i)return t;for(r=0,o=e.length-(i-1);r<o;r++)a+="../";for(r=i-1,o=n.length;r<o;r++)a+=r!==i-1?"/"+n[r]:n[r];return a},toAbsPath:function(e,t){var n,r,o,i=0,a=[];for(r=/\/$/.test(t)?"/":"",e=e.split("/"),t=t.split("/"),vw(e,function(e){e&&a.push(e)}),e=a,n=t.length-1,a=[];0<=n;n--)0!==t[n].length&&"."!==t[n]&&(".."!==t[n]?0<i?i--:a.push(t[n]):i++);return 0!==(o=(n=e.length-i)<=0?a.reverse().join("/"):e.slice(0,n).join("/")+"/"+a.reverse().join("/")).indexOf("/")&&(o="/"+o),r&&o.lastIndexOf("/")!==o.length-1&&(o+=r),o},getURI:function(e){var t,n=this;return n.source&&!e||(t="",e||(n.protocol?t+=n.protocol+"://":t+="//",n.userInfo&&(t+=n.userInfo+"@"),n.host&&(t+=n.host),n.port&&(t+=":"+n.port)),n.path&&(t+=n.path),n.query&&(t+="?"+n.query),n.anchor&&(t+="#"+n.anchor),n.source=t),n.source}},xw.parseDataUri=function(e){var t,n;return e=decodeURIComponent(e).split(","),(n=/data:([^;]+)/.exec(e[0]))&&(t=n[1]),{type:t,data:e[1]}},xw.getDocumentBaseUrl=function(e){var t;return t=0!==e.protocol.indexOf("http")&&"file:"!==e.protocol?e.href:e.protocol+"//"+e.host+e.pathname,/^[^:]+:\/\/\/?[^\/]+\//.test(t)&&(t=t.replace(/[\?#].*$/,"").replace(/[\/\\][^\/]+$/,""),/[\/\\]$/.test(t)||(t+="/")),t};var ww=function(e,t,n){var r,o,i,a,u;if(t.format=t.format?t.format:"html",t.get=!0,t.getInner=!0,t.no_events||e.fire("BeforeGetContent",t),"raw"===t.format)r=Yt.trim(Sv.trimExternal(e.serializer,n.innerHTML));else if("text"===t.format)r=fa(n.innerText||n.textContent);else{if("tree"===t.format)return e.serializer.serialize(n,t);i=(o=e).serializer.serialize(n,t),a=Qm(o),u=new RegExp("^(<"+a+"[^>]*>(&nbsp;|&#160;|\\s|\xa0|<br \\/>|)<\\/"+a+">[\r\n]*|<br \\/>[\r\n]*)$"),r=i.replace(u,"")}return"text"===t.format||yo(rr.fromDom(n))?t.content=r:t.content=Yt.trim(r),t.no_events||e.fire("GetContent",t),t.content},Nw=function(e,t){t(e),e.firstChild&&Nw(e.firstChild,t),e.next&&Nw(e.next,t)},Ew=function(e,t,n){var r=function(e,n,t){var r={},o={},i=[];for(var a in t.firstChild&&Nw(t.firstChild,function(t){F(e,function(e){e.name===t.name&&(r[e.name]?r[e.name].nodes.push(t):r[e.name]={filter:e,nodes:[t]})}),F(n,function(e){"string"==typeof t.attr(e.name)&&(o[e.name]?o[e.name].nodes.push(t):o[e.name]={filter:e,nodes:[t]})})}),r)r.hasOwnProperty(a)&&i.push(r[a]);for(var a in o)o.hasOwnProperty(a)&&i.push(o[a]);return i}(e,t,n);F(r,function(t){F(t.filter.callbacks,function(e){e(t.nodes,t.filter.name,{})})})},Sw=function(e){return e instanceof Kb},kw=function(e,t){var r;e.dom.setHTML(e.getBody(),t),$p(r=e)&&cc.firstPositionIn(r.getBody()).each(function(e){var t=e.getNode(),n=Do.isTable(t)?cc.firstPositionIn(t).getOr(e):e;r.selection.setRng(n.toRange())})},Tw=function(u,s,c){return void 0===c&&(c={}),c.format=c.format?c.format:"html",c.set=!0,c.content=Sw(s)?"":s,Sw(s)||c.no_events||(u.fire("BeforeSetContent",c),s=c.content),A.from(u.getBody()).fold(j(s),function(e){return Sw(s)?function(e,t,n,r){Ew(e.parser.getNodeFilters(),e.parser.getAttributeFilters(),n);var o=ul({validate:e.validate},e.schema).serialize(n);return r.content=yo(rr.fromDom(t))?o:Yt.trim(o),kw(e,r.content),r.no_events||e.fire("SetContent",r),n}(u,e,s,c):(t=u,n=e,o=c,0===(r=s).length||/^\s+$/.test(r)?(a='<br data-mce-bogus="1">',"TABLE"===n.nodeName?r="<tr><td>"+a+"</td></tr>":/^(UL|OL)$/.test(n.nodeName)&&(r="<li>"+a+"</li>"),(i=Qm(t))&&t.schema.isValidChild(n.nodeName.toLowerCase(),i.toLowerCase())?(r=a,r=t.dom.createHTML(i,t.settings.forced_root_block_attrs,r)):r||(r='<br data-mce-bogus="1">'),kw(t,r),t.fire("SetContent",o)):("raw"!==o.format&&(r=ul({validate:t.validate},t.schema).serialize(t.parser.parse(r,{isRootContent:!0,insert:!0}))),o.content=yo(rr.fromDom(n))?r:Yt.trim(r),kw(t,o.content),o.no_events||t.fire("SetContent",o)),o.content);var t,n,r,o,i,a})},Aw=pi.DOM,Rw=function(e){return A.from(e).each(function(e){return e.destroy()})},_w=function(e){if(!e.removed){var t=e._selectionOverrides,n=e.editorUpload,r=e.getBody(),o=e.getElement();r&&e.save({is_removing:!0}),e.removed=!0,e.unbindAllNativeEvents(),e.hasHiddenInput&&o&&Aw.remove(o.nextSibling),!e.inline&&r&&(i=e,Aw.setStyle(i.id,"display",i.orgDisplay)),vp(e),e.editorManager.remove(e),Aw.remove(e.getContainer()),Rw(t),Rw(n),e.destroy()}var i},Dw=function(e,t){var n,r,o,i=e.selection,a=e.dom;e.destroyed||(t||e.removed?(t||(e.editorManager.off("beforeunload",e._beforeUnload),e.theme&&e.theme.destroy&&e.theme.destroy(),Rw(i),Rw(a)),(r=(n=e).formElement)&&(r._mceOldSubmit&&(r.submit=r._mceOldSubmit,r._mceOldSubmit=null),Aw.unbind(r,"submit reset",n.formEventDelegate)),(o=e).contentAreaContainer=o.formElement=o.container=o.editorContainer=null,o.bodyElement=o.contentDocument=o.contentWindow=null,o.iframeElement=o.targetElm=null,o.selection&&(o.selection=o.selection.win=o.selection.dom=o.selection.dom.doc=null),e.destroyed=!0):e.remove())},Bw=pi.DOM,Ow=Yt.extend,Pw=Yt.each,Lw=Yt.resolve,Iw=de.ie,Mw=function(e,t,n){var r,o,i,a,u,s,c,l=this,f=l.documentBaseUrl=n.documentBaseURL,d=n.baseURI;r=l,o=e,i=f,a=n.defaultSettings,u=t,c={id:o,theme:"modern",delta_width:0,delta_height:0,popup_css:"",plugins:"",document_base_url:i,add_form_submit_trigger:!0,submit_patch:!0,add_unload_trigger:!0,convert_urls:!0,relative_urls:!0,remove_script_host:!0,object_resizing:!0,doctype:"<!DOCTYPE html>",visual:!0,font_size_style_values:"xx-small,x-small,small,medium,large,x-large,xx-large",font_size_legacy_values:"xx-small,small,medium,large,x-large,xx-large,300%",forced_root_block:"p",hidden_input:!0,render_ui:!0,indentation:"40px",inline_styles:!0,convert_fonts_to_spans:!0,indent:"simple",indent_before:"p,h1,h2,h3,h4,h5,h6,blockquote,div,title,style,pre,script,td,th,ul,ol,li,dl,dt,dd,area,table,thead,tfoot,tbody,tr,section,summary,article,hgroup,aside,figure,figcaption,option,optgroup,datalist",indent_after:"p,h1,h2,h3,h4,h5,h6,blockquote,div,title,style,pre,script,td,th,ul,ol,li,dl,dt,dd,area,table,thead,tfoot,tbody,tr,section,summary,article,hgroup,aside,figure,figcaption,option,optgroup,datalist",entity_encoding:"named",url_converter:(s=r).convertURL,url_converter_scope:s,ie7_compat:!0},t=wl(pl,c,a,u),l.settings=t,Ni.language=t.language||"en",Ni.languageLoad=t.language_load,Ni.baseURL=n.baseURL,l.id=e,l.setDirty(!1),l.plugins={},l.documentBaseURI=new xw(t.document_base_url,{base_uri:d}),l.baseURI=d,l.contentCSS=[],l.contentStyles=[],l.shortcuts=new Lp(l),l.loadedCSS={},l.editorCommands=new cp(l),l.suffix=n.suffix,l.editorManager=n,l.inline=t.inline,l.buttons={},l.menuItems={},t.cache_suffix&&(de.cacheSuffix=t.cache_suffix.replace(/^[\?\&]+/,"")),!1===t.override_viewport&&(de.overrideViewPort=!1),n.fire("SetupEditor",{editor:l}),l.execCallback("setup",l),l.$=pn.overrideDefaults(function(){return{context:l.inline?l.getBody():l.getDoc(),element:l.getBody()}})};Ow(Mw.prototype={render:function(){pw(this)},focus:function(e){qp(this,e)},hasFocus:function(){return $p(this)},execCallback:function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var r,o=this.settings[e];if(o)return this.callbackLookup&&(r=this.callbackLookup[e])&&(o=r.func,r=r.scope),"string"==typeof o&&(r=(r=o.replace(/\.\w+$/,""))?Lw(r):0,o=Lw(o),this.callbackLookup=this.callbackLookup||{},this.callbackLookup[e]={func:o,scope:r}),o.apply(r||this,Array.prototype.slice.call(arguments,1))},translate:function(e){if(e&&Yt.is(e,"string")){var n=this.settings.language||"en",r=this.editorManager.i18n;e=r.data[n+"."+e]||e.replace(/\{\#([^\}]+)\}/g,function(e,t){return r.data[n+"."+t]||"{#"+t+"}"})}return this.editorManager.translate(e)},getLang:function(e,t){return this.editorManager.i18n.data[(this.settings.language||"en")+"."+e]||(t!==undefined?t:"{#"+e+"}")},getParam:function(e,t,n){return Sl(this,e,t,n)},nodeChanged:function(e){this._nodeChangeDispatcher.nodeChanged(e)},addButton:function(e,t){var n=this;t.cmd&&(t.onclick=function(){n.execCommand(t.cmd)}),t.stateSelector&&"undefined"==typeof t.active&&(t.active=!1),t.text||t.icon||(t.icon=e),t.tooltip=t.tooltip||t.title,n.buttons[e]=t},addSidebar:function(e,t){return hw(this,e,t)},addMenuItem:function(e,t){var n=this;t.cmd&&(t.onclick=function(){n.execCommand(t.cmd)}),n.menuItems[e]=t},addContextToolbar:function(e,t){var n,r=this;r.contextToolbars=r.contextToolbars||[],"string"==typeof e&&(n=e,e=function(e){return r.dom.is(e,n)}),r.contextToolbars.push({id:kh.uuid("mcet"),predicate:e,items:t})},addCommand:function(e,t,n){this.editorCommands.addCommand(e,t,n)},addQueryStateHandler:function(e,t,n){this.editorCommands.addQueryStateHandler(e,t,n)},addQueryValueHandler:function(e,t,n){this.editorCommands.addQueryValueHandler(e,t,n)},addShortcut:function(e,t,n,r){this.shortcuts.add(e,t,n,r)},execCommand:function(e,t,n,r){return this.editorCommands.execCommand(e,t,n,r)},queryCommandState:function(e){return this.editorCommands.queryCommandState(e)},queryCommandValue:function(e){return this.editorCommands.queryCommandValue(e)},queryCommandSupported:function(e){return this.editorCommands.queryCommandSupported(e)},show:function(){this.hidden&&(this.hidden=!1,this.inline?this.getBody().contentEditable=!0:(Bw.show(this.getContainer()),Bw.hide(this.id)),this.load(),this.fire("show"))},hide:function(){var e=this,t=e.getDoc();e.hidden||(Iw&&t&&!e.inline&&t.execCommand("SelectAll"),e.save(),e.inline?(e.getBody().contentEditable=!1,e===e.editorManager.focusedEditor&&(e.editorManager.focusedEditor=null)):(Bw.hide(e.getContainer()),Bw.setStyle(e.id,"display",e.orgDisplay)),e.hidden=!0,e.fire("hide"))},isHidden:function(){return!!this.hidden},setProgressState:function(e,t){this.fire("ProgressState",{state:e,time:t})},load:function(e){var t,n=this.getElement();return this.removed?"":n?((e=e||{}).load=!0,t=this.setContent(n.value!==undefined?n.value:n.innerHTML,e),e.element=n,e.no_events||this.fire("LoadContent",e),e.element=n=null,t):void 0},save:function(e){var t,n,r=this,o=r.getElement();if(o&&r.initialized&&!r.removed)return(e=e||{}).save=!0,e.element=o,e.content=r.getContent(e),e.no_events||r.fire("SaveContent",e),"raw"===e.format&&r.fire("RawSaveContent",e),t=e.content,/TEXTAREA|INPUT/i.test(o.nodeName)?o.value=t:(!e.is_removing&&r.inline||(o.innerHTML=t),(n=Bw.getParent(r.id,"form"))&&Pw(n.elements,function(e){if(e.name===r.id)return e.value=t,!1})),e.element=o=null,!1!==e.set_dirty&&r.setDirty(!1),t},setContent:function(e,t){return Tw(this,e,t)},getContent:function(e){return t=this,void 0===(n=e)&&(n={}),A.from(t.getBody()).fold(j("tree"===n.format?new Kb("body",11):""),function(e){return ww(t,n,e)});var t,n},insertContent:function(e,t){t&&(e=Ow({content:e},t)),this.execCommand("mceInsertContent",!1,e)},isDirty:function(){return!this.isNotDirty},setDirty:function(e){var t=!this.isNotDirty;this.isNotDirty=!e,e&&e!==t&&this.fire("dirty")},setMode:function(e){var t,n;(n=e)!==Ep(t=this)&&(t.initialized?Np(t,"readonly"===n):t.on("init",function(){Np(t,"readonly"===n)}),bp(t,n))},getContainer:function(){return this.container||(this.container=Bw.get(this.editorContainer||this.id+"_parent")),this.container},getContentAreaContainer:function(){return this.contentAreaContainer},getElement:function(){return this.targetElm||(this.targetElm=Bw.get(this.id)),this.targetElm},getWin:function(){var e;return this.contentWindow||(e=this.iframeElement)&&(this.contentWindow=e.contentWindow),this.contentWindow},getDoc:function(){var e;return this.contentDocument||(e=this.getWin())&&(this.contentDocument=e.document),this.contentDocument},getBody:function(){var e=this.getDoc();return this.bodyElement||(e?e.body:null)},convertURL:function(e,t,n){var r=this.settings;return r.urlconverter_callback?this.execCallback("urlconverter_callback",e,n,!0,t):!r.convert_urls||n&&"LINK"===n.nodeName||0===e.indexOf("file:")||0===e.length?e:r.relative_urls?this.documentBaseURI.toRelative(e):e=this.documentBaseURI.toAbsolute(e,r.remove_script_host)},addVisual:function(e){var n,r=this,o=r.settings,i=r.dom;e=e||r.getBody(),r.hasVisual===undefined&&(r.hasVisual=o.visual),Pw(i.select("table,a",e),function(e){var t;switch(e.nodeName){case"TABLE":return n=o.visual_table_class||"mce-item-table",void((t=i.getAttrib(e,"border"))&&"0"!==t||!r.hasVisual?i.removeClass(e,n):i.addClass(e,n));case"A":return void(i.getAttrib(e,"href")||(t=i.getAttrib(e,"name")||e.id,n=o.visual_anchor_class||"mce-item-anchor",t&&r.hasVisual?i.addClass(e,n):i.removeClass(e,n)))}}),r.fire("VisualAid",{element:e,hasVisual:r.hasVisual})},remove:function(){_w(this)},destroy:function(e){Dw(this,e)},uploadImages:function(e){return this.editorUpload.uploadImages(e)},_scanForImages:function(){return this.editorUpload.scanForImages()}},_p);var Fw,zw,Uw,Vw={isEditorUIElement:function(e){return-1!==e.className.toString().indexOf("mce-")}},Hw=function(n,e){var t,r;tr.detect().browser.isIE()?(r=n).on("focusout",function(){Zg(r)}):(t=e,n.on("mouseup touchend",function(e){t.throttle()})),n.on("keyup nodechange",function(e){var t;"nodechange"===(t=e).type&&t.selectionChange||Zg(n)})},jw=function(e){var t,n,r,o=Bi(function(){Zg(e)},0);e.inline&&(t=e,n=o,r=function(){n.throttle()},pi.DOM.bind(document,"mouseup",r),t.on("remove",function(){pi.DOM.unbind(document,"mouseup",r)})),e.on("init",function(){Hw(e,o)}),e.on("remove",function(){o.cancel()})},qw=pi.DOM,$w=function(e){return Vw.isEditorUIElement(e)},Ww=function(t,e){var n=t?t.settings.custom_ui_selector:"";return null!==qw.getParent(e,function(e){return $w(e)||!!n&&t.dom.is(e,n)})},Kw=function(r,e){var t=e.editor;jw(t),t.on("focusin",function(){var e=r.focusedEditor;e!==this&&(e&&e.fire("blur",{focusedEditor:this}),r.setActive(this),(r.focusedEditor=this).fire("focus",{blurredEditor:e}),this.focus(!0))}),t.on("focusout",function(){var t=this;ve.setEditorTimeout(t,function(){var e=r.focusedEditor;Ww(t,function(){try{return document.activeElement}catch(e){return document.body}}())||e!==t||(t.fire("blur",{focusedEditor:null}),r.focusedEditor=null)})}),Fw||(Fw=function(e){var t,n=r.activeEditor;t=e.target,n&&t.ownerDocument===document&&(t===document.body||Ww(n,t)||r.focusedEditor!==n||(n.fire("blur",{focusedEditor:null}),r.focusedEditor=null))},qw.bind(document,"focusin",Fw))},Xw=function(e,t){e.focusedEditor===t.editor&&(e.focusedEditor=null),e.activeEditor||(qw.unbind(document,"focusin",Fw),Fw=null)},Yw=function(e){e.on("AddEditor",d(Kw,e)),e.on("RemoveEditor",d(Xw,e))},Gw=pi.DOM,Jw=Yt.explode,Qw=Yt.each,Zw=Yt.extend,eN=0,tN=!1,nN=[],rN=[],oN=function(t){var n=t.type;Qw(Uw.get(),function(e){switch(n){case"scroll":e.fire("ScrollWindow",t);break;case"resize":e.fire("ResizeWindow",t)}})},iN=function(e){e!==tN&&(e?pn(window).on("resize scroll",oN):pn(window).off("resize scroll",oN),tN=e)},aN=function(t){var e=rN;delete nN[t.id];for(var n=0;n<nN.length;n++)if(nN[n]===t){nN.splice(n,1);break}return rN=z(rN,function(e){return t!==e}),Uw.activeEditor===t&&(Uw.activeEditor=0<rN.length?rN[0]:null),Uw.focusedEditor===t&&(Uw.focusedEditor=null),e.length!==rN.length};Zw(Uw={defaultSettings:{},$:pn,majorVersion:"4",minorVersion:"9.3",releaseDate:"2019-01-31",editors:nN,i18n:oh,activeEditor:null,settings:{},setup:function(){var e,t,n,r,o="";if(t=xw.getDocumentBaseUrl(document.location),/^[^:]+:\/\/\/?[^\/]+\//.test(t)&&(t=t.replace(/[\?#].*$/,"").replace(/[\/\\][^\/]+$/,""),/[\/\\]$/.test(t)||(t+="/")),n=window.tinymce||window.tinyMCEPreInit)e=n.base||n.baseURL,o=n.suffix;else{for(var i=document.getElementsByTagName("script"),a=0;a<i.length;a++){var u=(r=i[a].src).substring(r.lastIndexOf("/"));if(/tinymce(\.full|\.jquery|)(\.min|\.dev|)\.js/.test(r)){-1!==u.indexOf(".min")&&(o=".min"),e=r.substring(0,r.lastIndexOf("/"));break}}!e&&document.currentScript&&(-1!==(r=document.currentScript.src).indexOf(".min")&&(o=".min"),e=r.substring(0,r.lastIndexOf("/")))}this.baseURL=new xw(t).toAbsolute(e),this.documentBaseURL=t,this.baseURI=new xw(this.baseURL),this.suffix=o,Yw(this)},overrideDefaults:function(e){var t,n;(t=e.base_url)&&(this.baseURL=new xw(this.documentBaseURL).toAbsolute(t.replace(/\/+$/,"")),this.baseURI=new xw(this.baseURL)),n=e.suffix,e.suffix&&(this.suffix=n);var r=(this.defaultSettings=e).plugin_base_urls;for(var o in r)Ni.PluginManager.urls[o]=r[o]},init:function(r){var n,u,s=this;u=Yt.makeMap("area base basefont br col frame hr img input isindex link meta param embed source wbr track colgroup option tbody tfoot thead tr script noscript style textarea video audio iframe object menu"," ");var c=function(e){var t=e.id;return t||(t=(t=e.name)&&!Gw.get(t)?e.name:Gw.uniqueId(),e.setAttribute("id",t)),t},l=function(e,t){return t.constructor===RegExp?t.test(e.className):Gw.hasClass(e,t)},f=function(e){n=e},e=function(){var o,i=0,a=[],n=function(e,t,n){var r=new Mw(e,t,s);a.push(r),r.on("init",function(){++i===o.length&&f(a)}),r.targetElm=r.targetElm||n,r.render()};Gw.unbind(window,"ready",e),function(e){var t=r[e];t&&t.apply(s,Array.prototype.slice.call(arguments,2))}("onpageload"),o=pn.unique(function(t){var e,n=[];if(de.ie&&de.ie<11)return lh.initError("TinyMCE does not support the browser you are using. For a list of supported browsers please see: https://www.tinymce.com/docs/get-started/system-requirements/"),[];if(t.types)return Qw(t.types,function(e){n=n.concat(Gw.select(e.selector))}),n;if(t.selector)return Gw.select(t.selector);if(t.target)return[t.target];switch(t.mode){case"exact":0<(e=t.elements||"").length&&Qw(Jw(e),function(t){var e;(e=Gw.get(t))?n.push(e):Qw(document.forms,function(e){Qw(e.elements,function(e){e.name===t&&(t="mce_editor_"+eN++,Gw.setAttrib(e,"id",t),n.push(e))})})});break;case"textareas":case"specific_textareas":Qw(Gw.select("textarea"),function(e){t.editor_deselector&&l(e,t.editor_deselector)||t.editor_selector&&!l(e,t.editor_selector)||n.push(e)})}return n}(r)),r.types?Qw(r.types,function(t){Yt.each(o,function(e){return!Gw.is(e,t.selector)||(n(c(e),Zw({},r,t),e),!1)})}):(Yt.each(o,function(e){var t;(t=s.get(e.id))&&t.initialized&&!(t.getContainer()||t.getBody()).parentNode&&(aN(t),t.unbindAllNativeEvents(),t.destroy(!0),t.removed=!0,t=null)}),0===(o=Yt.grep(o,function(e){return!s.get(e.id)})).length?f([]):Qw(o,function(e){var t;t=e,r.inline&&t.tagName.toLowerCase()in u?lh.initError("Could not initialize inline editor on invalid inline target element",e):n(c(e),r,e)}))};return s.settings=r,Gw.bind(window,"ready",e),new me(function(t){n?t(n):f=function(e){t(e)}})},get:function(t){return 0===arguments.length?rN.slice(0):R(t)?V(rN,function(e){return e.id===t}).getOr(null):L(t)&&rN[t]?rN[t]:null},add:function(e){var t=this;return nN[e.id]===e||(null===t.get(e.id)&&("length"!==e.id&&(nN[e.id]=e),nN.push(e),rN.push(e)),iN(!0),t.activeEditor=e,t.fire("AddEditor",{editor:e}),zw||(zw=function(){t.fire("BeforeUnload")},Gw.bind(window,"beforeunload",zw))),e},createEditor:function(e,t){return this.add(new Mw(e,t,this))},remove:function(e){var t,n,r=this;if(e){if(!R(e))return n=e,B(r.get(n.id))?null:(aN(n)&&r.fire("RemoveEditor",{editor:n}),0===rN.length&&Gw.unbind(window,"beforeunload",zw),n.remove(),iN(0<rN.length),n);Qw(Gw.select(e),function(e){(n=r.get(e.id))&&r.remove(n)})}else for(t=rN.length-1;0<=t;t--)r.remove(rN[t])},execCommand:function(e,t,n){var r=this.get(n);switch(e){case"mceAddEditor":return this.get(n)||new Mw(n,this.settings,this).render(),!0;case"mceRemoveEditor":return r&&r.remove(),!0;case"mceToggleEditor":return r?r.isHidden()?r.show():r.hide():this.execCommand("mceAddEditor",0,n),!0}return!!this.activeEditor&&this.activeEditor.execCommand(e,t,n)},triggerSave:function(){Qw(rN,function(e){e.save()})},addI18n:function(e,t){oh.add(e,t)},translate:function(e){return oh.translate(e)},setActive:function(e){var t=this.activeEditor;this.activeEditor!==e&&(t&&t.fire("deactivate",{relatedTarget:e}),e.fire("activate",{relatedTarget:t})),this.activeEditor=e}},gp),Uw.setup();var uN,sN=Uw;function cN(n){return{walk:function(e,t){return Mc(n,e,t)},split:gm,normalize:function(t){return Ag(n,t).fold(j(!1),function(e){return t.setStart(e.startContainer,e.startOffset),t.setEnd(e.endContainer,e.endOffset),!0})}}}(uN=cN||(cN={})).compareRanges=xg,uN.getCaretRangeFromPoint=xy,uN.getSelectedNode=Va,uN.getNode=Ha;var lN,fN,dN=cN,mN=Math.min,gN=Math.max,pN=Math.round,hN=function(e,t,n){var r,o,i,a,u,s;return r=t.x,o=t.y,i=e.w,a=e.h,u=t.w,s=t.h,"b"===(n=(n||"").split(""))[0]&&(o+=s),"r"===n[1]&&(r+=u),"c"===n[0]&&(o+=pN(s/2)),"c"===n[1]&&(r+=pN(u/2)),"b"===n[3]&&(o-=a),"r"===n[4]&&(r-=i),"c"===n[3]&&(o-=pN(a/2)),"c"===n[4]&&(r-=pN(i/2)),vN(r,o,i,a)},vN=function(e,t,n,r){return{x:e,y:t,w:n,h:r}},bN={inflate:function(e,t,n){return vN(e.x-t,e.y-n,e.w+2*t,e.h+2*n)},relativePosition:hN,findBestRelativePosition:function(e,t,n,r){var o,i;for(i=0;i<r.length;i++)if((o=hN(e,t,r[i])).x>=n.x&&o.x+o.w<=n.w+n.x&&o.y>=n.y&&o.y+o.h<=n.h+n.y)return r[i];return null},intersect:function(e,t){var n,r,o,i;return n=gN(e.x,t.x),r=gN(e.y,t.y),o=mN(e.x+e.w,t.x+t.w),i=mN(e.y+e.h,t.y+t.h),o-n<0||i-r<0?null:vN(n,r,o-n,i-r)},clamp:function(e,t,n){var r,o,i,a,u,s,c,l,f,d;return u=e.x,s=e.y,c=e.x+e.w,l=e.y+e.h,f=t.x+t.w,d=t.y+t.h,r=gN(0,t.x-u),o=gN(0,t.y-s),i=gN(0,c-f),a=gN(0,l-d),u+=r,s+=o,n&&(c+=r,l+=o,u-=i,s-=a),vN(u,s,(c-=i)-u,(l-=a)-s)},create:vN,fromClientRect:function(e){return vN(e.left,e.top,e.width,e.height)}},yN={},CN={add:function(e,t){yN[e.toLowerCase()]=t},has:function(e){return!!yN[e.toLowerCase()]},get:function(e){var t=e.toLowerCase(),n=yN.hasOwnProperty(t)?yN[t]:null;if(null===n)throw new Error("Could not find module for type: "+e);return n},create:function(e,t){var n;if("string"==typeof e?(t=t||{}).type=e:e=(t=e).type,e=e.toLowerCase(),!(n=yN[e]))throw new Error("Could not find control by type: "+e);return(n=new n(t)).type=e,n}},xN=Yt.each,wN=Yt.extend,NN=function(){};NN.extend=lN=function(n){var e,t,r,o=this.prototype,i=function(){var e,t,n;if(!fN&&(this.init&&this.init.apply(this,arguments),t=this.Mixins))for(e=t.length;e--;)(n=t[e]).init&&n.init.apply(this,arguments)},a=function(){return this},u=function(n,r){return function(){var e,t=this._super;return this._super=o[n],e=r.apply(this,arguments),this._super=t,e}};for(t in fN=!0,e=new this,fN=!1,n.Mixins&&(xN(n.Mixins,function(e){for(var t in e)"init"!==t&&(n[t]=e[t])}),o.Mixins&&(n.Mixins=o.Mixins.concat(n.Mixins))),n.Methods&&xN(n.Methods.split(","),function(e){n[e]=a}),n.Properties&&xN(n.Properties.split(","),function(e){var t="_"+e;n[e]=function(e){return e!==undefined?(this[t]=e,this):this[t]}}),n.Statics&&xN(n.Statics,function(e,t){i[t]=e}),n.Defaults&&o.Defaults&&(n.Defaults=wN({},o.Defaults,n.Defaults)),n)"function"==typeof(r=n[t])&&o[t]?e[t]=u(t,r):e[t]=r;return i.prototype=e,(i.constructor=i).extend=lN,i};var EN=Math.min,SN=Math.max,kN=Math.round,TN=function(e,n){var r,o,t,i;if(n=n||'"',null===e)return"null";if("string"==(t=typeof e))return o="\bb\tt\nn\ff\rr\"\"''\\\\",n+e.replace(/([\u0080-\uFFFF\x00-\x1f\"\'\\])/g,function(e,t){return'"'===n&&"'"===e?e:(r=o.indexOf(t))+1?"\\"+o.charAt(r+1):(e=t.charCodeAt().toString(16),"\\u"+"0000".substring(e.length)+e)})+n;if("object"===t){if(e.hasOwnProperty&&"[object Array]"===Object.prototype.toString.call(e)){for(r=0,o="[";r<e.length;r++)o+=(0<r?",":"")+TN(e[r],n);return o+"]"}for(i in o="{",e)e.hasOwnProperty(i)&&(o+="function"!=typeof e[i]?(1<o.length?","+n:n)+i+n+":"+TN(e[i],n):"");return o+"}"}return""+e},AN={serialize:TN,parse:function(e){try{return JSON.parse(e)}catch(t){}}},RN={callbacks:{},count:0,send:function(t){var n=this,r=pi.DOM,o=t.count!==undefined?t.count:n.count,i="tinymce_jsonp_"+o;n.callbacks[o]=function(e){r.remove(i),delete n.callbacks[o],t.callback(e)},r.add(r.doc.body,"script",{id:i,src:t.url,type:"text/javascript"}),n.count++}},_N={send:function(e){var t,n=0,r=function(){!e.async||4===t.readyState||1e4<n++?(e.success&&n<1e4&&200===t.status?e.success.call(e.success_scope,""+t.responseText,t,e):e.error&&e.error.call(e.error_scope,1e4<n?"TIMED_OUT":"GENERAL",t,e),t=null):setTimeout(r,10)};if(e.scope=e.scope||this,e.success_scope=e.success_scope||e.scope,e.error_scope=e.error_scope||e.scope,e.async=!1!==e.async,e.data=e.data||"",_N.fire("beforeInitialize",{settings:e}),t=mh()){if(t.overrideMimeType&&t.overrideMimeType(e.content_type),t.open(e.type||(e.data?"POST":"GET"),e.url,e.async),e.crossDomain&&(t.withCredentials=!0),e.content_type&&t.setRequestHeader("Content-Type",e.content_type),e.requestheaders&&Yt.each(e.requestheaders,function(e){t.setRequestHeader(e.key,e.value)}),t.setRequestHeader("X-Requested-With","XMLHttpRequest"),(t=_N.fire("beforeSend",{xhr:t,settings:e}).xhr).send(e.data),!e.async)return r();setTimeout(r,10)}}};Yt.extend(_N,gp);var DN,BN,ON,PN,LN=Yt.extend,IN=function(e){this.settings=LN({},e),this.count=0};IN.sendRPC=function(e){return(new IN).send(e)},IN.prototype={send:function(n){var r=n.error,o=n.success;(n=LN(this.settings,n)).success=function(e,t){void 0===(e=AN.parse(e))&&(e={error:"JSON Parse error."}),e.error?r.call(n.error_scope||n.scope,e.error,t):o.call(n.success_scope||n.scope,e.result)},n.error=function(e,t){r&&r.call(n.error_scope||n.scope,e,t)},n.data=AN.serialize({id:n.id||"c"+this.count++,method:n.method,params:n.params}),n.content_type="application/json",_N.send(n)}};try{DN=window.localStorage}catch(VN){BN={},ON=[],PN={getItem:function(e){var t=BN[e];return t||null},setItem:function(e,t){ON.push(e),BN[e]=String(t)},key:function(e){return ON[e]},removeItem:function(t){ON=ON.filter(function(e){return e===t}),delete BN[t]},clear:function(){ON=[],BN={}},length:0},Object.defineProperty(PN,"length",{get:function(){return ON.length},configurable:!1,enumerable:!1}),DN=PN}var MN,FN=sN,zN={geom:{Rect:bN},util:{Promise:me,Delay:ve,Tools:Yt,VK:mv,URI:xw,Class:NN,EventDispatcher:fp,Observable:gp,I18n:oh,XHR:_N,JSON:AN,JSONRequest:IN,JSONP:RN,LocalStorage:DN,Color:function(e){var n={},u=0,s=0,c=0,t=function(e){var t;return"object"==typeof e?"r"in e?(u=e.r,s=e.g,c=e.b):"v"in e&&function(e,t,n){var r,o,i,a;if(e=(parseInt(e,10)||0)%360,t=parseInt(t,10)/100,n=parseInt(n,10)/100,t=SN(0,EN(t,1)),n=SN(0,EN(n,1)),0!==t){switch(r=e/60,i=(o=n*t)*(1-Math.abs(r%2-1)),a=n-o,Math.floor(r)){case 0:u=o,s=i,c=0;break;case 1:u=i,s=o,c=0;break;case 2:u=0,s=o,c=i;break;case 3:u=0,s=i,c=o;break;case 4:u=i,s=0,c=o;break;case 5:u=o,s=0,c=i;break;default:u=s=c=0}u=kN(255*(u+a)),s=kN(255*(s+a)),c=kN(255*(c+a))}else u=s=c=kN(255*n)}(e.h,e.s,e.v):(t=/rgb\s*\(\s*([0-9]+)\s*,\s*([0-9]+)\s*,\s*([0-9]+)[^\)]*\)/gi.exec(e))?(u=parseInt(t[1],10),s=parseInt(t[2],10),c=parseInt(t[3],10)):(t=/#([0-F]{2})([0-F]{2})([0-F]{2})/gi.exec(e))?(u=parseInt(t[1],16),s=parseInt(t[2],16),c=parseInt(t[3],16)):(t=/#([0-F])([0-F])([0-F])/gi.exec(e))&&(u=parseInt(t[1]+t[1],16),s=parseInt(t[2]+t[2],16),c=parseInt(t[3]+t[3],16)),u=u<0?0:255<u?255:u,s=s<0?0:255<s?255:s,c=c<0?0:255<c?255:c,n};return e&&t(e),n.toRgb=function(){return{r:u,g:s,b:c}},n.toHsv=function(){return e=u,t=s,n=c,o=0,(i=EN(e/=255,EN(t/=255,n/=255)))===(a=SN(e,SN(t,n)))?{h:0,s:0,v:100*(o=i)}:(r=(a-i)/a,{h:kN(60*((e===i?3:n===i?1:5)-(e===i?t-n:n===i?e-t:n-e)/((o=a)-i))),s:kN(100*r),v:kN(100*o)});var e,t,n,r,o,i,a},n.toHex=function(){var e=function(e){return 1<(e=parseInt(e,10).toString(16)).length?e:"0"+e};return"#"+e(u)+e(s)+e(c)},n.parse=t,n}},dom:{EventUtils:ke,Sizzle:kt,DomQuery:pn,TreeWalker:ro,DOMUtils:pi,ScriptLoader:Ci,RangeUtils:dN,Serializer:cy,ControlSelection:hy,BookmarkManager:my,Selection:Yy,Event:ke.Event},html:{Styles:oi,Entities:$o,Node:Kb,Schema:ni,SaxParser:Nv,DomParser:oy,Writer:al,Serializer:ul},ui:{Factory:CN},Env:de,AddOnManager:Ni,Annotator:qc,Formatter:Lb,UndoManager:Hv,EditorCommands:cp,WindowManager:th,NotificationManager:eh,EditorObservable:_p,Shortcuts:Lp,Editor:Mw,FocusManager:Vw,EditorManager:sN,DOM:pi.DOM,ScriptLoader:Ci.ScriptLoader,PluginManager:Ni.PluginManager,ThemeManager:Ni.ThemeManager,trim:Yt.trim,isArray:Yt.isArray,is:Yt.is,toArray:Yt.toArray,makeMap:Yt.makeMap,each:Yt.each,map:Yt.map,grep:Yt.grep,inArray:Yt.inArray,extend:Yt.extend,create:Yt.create,walk:Yt.walk,createNS:Yt.createNS,resolve:Yt.resolve,explode:Yt.explode,_addCacheSuffix:Yt._addCacheSuffix,isOpera:de.opera,isWebKit:de.webkit,isIE:de.ie,isGecko:de.gecko,isMac:de.mac},UN=FN=Yt.extend(FN,zN);MN=UN,window.tinymce=MN,window.tinyMCE=MN,function(e){if("object"==typeof module)try{module.exports=e}catch(t){}}(UN)}();