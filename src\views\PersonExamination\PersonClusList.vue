<template>
  <div>
    <div class="clusDiv" v-show="!editFlag">
      <div style="width: 100%">套餐列表</div>
      <div class="clusTop">
        <el-input
          placeholder="套餐名称(为空时默认加载全部)" 
          style="width: 240px"
          v-model="ClusList.clus_Name"
          size="small"
        ></el-input>
        <el-button
          type="primary"
          icon="el-icon-search"
          @click="GetNewData"
          style="margin-left: 10px"
          size="small"
          >查询</el-button
        >
        <!-- <el-button
          type="success"
          icon="el-icon-plus"
          @click="showAddExistingDialog()"
          size="small"
          >添加现有套餐</el-button
        > -->
        <el-button
          type="success"
          icon="el-icon-plus"
          @click="creatClus()"
          size="small"
          >新增套餐</el-button
        >

        <el-button
          type="danger"
          icon="el-icon-delete"
          @click="DeleteClus()"
          size="small"
          >批量删除</el-button
        >
      </div>

      <div class="clusMid">
        <el-table
          :data="tableData"
          v-loading="loading"
          element-loading-text="拼命加载中"
          border
          stripe
          :fit="true"
          row-key="id"
          @selection-change="handleSelectionChangePeople"
          :height="height"
        >
          <el-table-column
            type="selection"
            width="55"
            align="center"
            :reserve-selection="true"
          ></el-table-column>
          <el-table-column
            prop="clus_Code"
            label="套餐编码"
            align="center"
            sortable
          ></el-table-column>
          <el-table-column
            prop="clus_Name"
            label="套餐名称"
            align="center"
            sortable
          ></el-table-column>
          <el-table-column
            prop="clus_Note"
            label="套餐简介"
            align="center"
            sortable>
            <template slot-scope="{ row }">
              <div 
                class="ellipsis-column"
                v-bind:title="row.clus_Note"
              >
                {{ row.clus_Note }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="price"
            label="价格"
            align="center"
            sortable
          ></el-table-column>
          <el-table-column
            prop="clus_sex"
            label="性别"
            align="center"
            sortable
          ></el-table-column>
          <el-table-column
            prop="clusType"
            label="分类"
            align="center"
            sortable
          ></el-table-column>
          <el-table-column
            prop="state"
            label="状态"
            align="center"
            sortable
          ></el-table-column>
          <el-table-column label="操作" width="300" align="center">
            <template slot-scope="scope">
              <el-button
                @click="editClick(scope.row)"
                type="primary"
                plain
                >编辑</el-button
              >
              <el-button @click="DeleteClus(scope.row.id)" type="danger" plain
                >删除</el-button
              >
              <!--单个删除-->
            </template>
          </el-table-column>
        </el-table>
        <div class="pageNation">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="index"
            :page-sizes="[50, 100, 200, 300, 500]"
            :page-size="size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableCopyTableList.length"
          ></el-pagination>
        </div>
      </div>
    </div>
    <EditClus v-if="editFlag" :lnc_Code.sync="lnc_Code" ref="editClus_ref" />
  </div>
</template>
  
  <script>
import temporaryData from "../../common/temporaryData.js"; //号源临时假数据
import { ajax } from "../../common/ajax";
import apiUrls from "../../config/apiUrls";
import { storage } from "@/common";
import EditClus from "./editClus.vue";
export default {
  name: "BookTj",
  components: {
    EditClus
  },
  data() {
    return {
      lnc_Code: "0451",
      lnc_Name: "",
      checkedAllCities: [],
      checkedCities: [],
      selectCombList: [],
      ids: "", //id集合 用于批量删除或单个删除
      tableData: [], //表数据
      tableCopyTableList: [], //表数据集合
      tableConstData: [], //存放数据 用于筛选数据
      index: 1, //当前页数
      size: 50, //页码
      editFlag: false,
      //套餐模型
      ClusList: {
        id: "",
        clus_Code: "",
        clus_Name: "",
        state: "",
        price: "",
        isModify: "",
        // maxPrice: 0,
        clus_sex: "",
        ageIimit: "",
        maxAge: "",
        minAge: "",
        clus_Note: "",
        clusType: "",
        category: "",
        business: "",
      },
      height: "calc( 100vh - 250px)",
    };
  },
  created() {
    // this.loadBtn();
    // var lncList = JSON.parse(storage.session.get("lncList"));
    // if (lncList != null) {
    //   this.lnc_Code = lncList.lnc_Code;
    //   this.lnc_Name = lncList.lnc_Name;
    // }
    this.lnc_Code = "0451";
    this.GetClusList();
    // this.GetAllClusItemComb();
    //this.dateEvent();
  },
  methods: {
    load() {},
    //获取选中行id
    handleSelectionChangePeople(rows) {
      this.ids = rows.map((row) => row.id);
    },
    handleSelectionChangeids(rows) {
      this.clusIds = rows.map((row) => row.id);
    },
    // checkSaveClusInfo() {
    //   if (!this.ClusList.clus_Name) {
    //     this.$message.warning("请输入套餐名称");
    //     return false;
    //   }
    //   if (!this.ClusList.price) {
    //     this.$message.warning("请输入套餐价格");
    //     return false;
    //   }
    //   if (this.checkedCities.length == 0) {
    //     this.$message.warning("请输入套餐项目");
    //     return false;
    //   }
    //   if (this.ClusList.isModify == "N" && this.allowAddComb.length == 0) {
    //     this.$message.warning("请选择加项项目");
    //     return false;
    //   }
    //   if (!this.ClusList.clus_sex) {
    //     this.$message.warning("请输入性别");
    //     return false;
    //   }
    //   if (!this.ClusList.category) {
    //     this.$message.warning("请选择类别");
    //     return false;
    //   }
    //   if (!this.ClusList.business) {
    //     this.$message.warning("请选择业务类型");
    //     return false;
    //   }
    //   if (this.ClusList.ageIimit=="T") {
    //       if  (!this.ClusList.minAge||!this.ClusList.maxAge) {
    //         this.$message.warning("请输入限制范围");
    //         return false;
    //       }
    //       if (parseInt(this.ClusList.minAge)>parseInt(this.ClusList.maxAge)) {
    //         this.$message.warning("最小年龄大于最大年龄，请重新输入");
    //         return false;
    //       }
    //     }
    //   if (!this.ClusList.clusType) {
    //     this.$message.warning("请选择套餐分类");
    //     return false;
    //   } else {
    //     switch (this.ClusList.clusType) {
    //       case "个人健康体检":
    //         this.ClusList.clusType = "01";
    //         break;
    //       case "入职及其他":
    //         this.ClusList.clusType = "06";
    //         break;
    //       default:
    //         this.$message.warning("套餐分类未识别");
    //         break;
    //     }
    //   }
    //   return true;
    // },
    // saveClusInfo() {
    //   this.overallLoading = this.$loading({
    //     lock: true,
    //     text: "正在保存中",
    //     spinner: "el-icon-loading",
    //     background: "rgba(0, 0, 0, 0.7)",
    //   });
    //   //参数验证
    //   if (!this.checkSaveClusInfo()) {
    //     this.overallLoading.close();
    //     return;
    //   }
    //   var addOrEdit = ""; //请求变量 新增/编辑 api地址不一样
    //   if (this.BusinessType == "Edit") {
    //     addOrEdit = apiUrls.UpdateClusInfoAndComb;
    //   } else {
    //     this.ClusList.id = 0;
    //     addOrEdit = apiUrls.AddClusInfoAndComb;
    //   }
    //   var pData = {
    //     data: {
    //       lnc_Code: this.lnc_Code,
    //     },
    //     clusterHelper: {
    //       cluster: this.ClusList,
    //       combList: this.checkedCities,
    //       addCombList: this.allowAddComb,
    //     },
    //   };
    //   ajax
    //     .post(addOrEdit, pData)
    //     .then((r) => {
    //       this.overallLoading.close();
    //       if (!r.data.success) {
    //         alert(r.data.returnMsg);
    //         return;
    //       }
    //       this.$message.success("保存成功");
    //       this.isUpdate = false; //成功后关闭对话框
    //       this.GetClusList(); //重新加载
    //     })
    //     .catch((err) => {
    //       this.overallLoading.close();
    //       this.$message.error("系统繁忙！请稍后再试");
    //     });
    // },
    // showAddExistingDialog() {
    //   this.screenClusData(this.lnc_Code);
    //   this.dialogVisible = true;
    // },
    // //显示模态框
    // showAddorEditDialog(row) {
    //   console.log(row);
    //   if (row == undefined) {
    //     this.dialogTitle = "新增套餐";
    //     this.BusinessType = "Add";
    //   } else {
    //     this.dialogTitle = "查看/编辑套餐";
    //     this.BusinessType = "Edit";
    //     this.GetClusItemComb(row.clus_Code);
    //   }
    //   this.allowAddComb = [];
    //   this.checkedCities = [];
    //   this.price = 0;
    //   this.ClusList.id = row ? row.id : "";
    //   this.ClusList.clus_Code = row ? row.clus_Code : "";
    //   this.ClusList.clus_Name = row ? row.clus_Name : "";
    //   this.ClusList.category = row ? row.category : "01";
    //   this.ClusList.business = row ? row.business : "01";
    //   this.ClusList.clusType = row ? row.clusType : "";
    //   this.ClusList.clus_sex = row
    //     ? row.clus_sex != "不限"
    //       ? row.clus_sex == "男"
    //         ? "1"
    //         : "0"
    //       : "%"
    //     : "%";
    //   this.ClusList.isModify = row
    //     ? row.isModify != "不允许加项"
    //       ? row.isModify == "允许加指定项目"
    //         ? "N"
    //         : "T"
    //       : "F"
    //     : "F";
    //   this.ClusList.price = row ? row.price : "";
    //   this.ClusList.clus_Note = row ? row.clus_Note : "";
    //   this.ClusList.ageIimit =
    //     (row ? row.ageIimit : "启用") == "启用" ? "T" : "F";
    //   this.ClusList.minAge = row ? row.minAge : "";
    //   this.ClusList.maxAge = row ? row.maxAge : "";
    //   this.ClusList.state = (row ? row.state : "启用") == "启用" ? "T" : "F";
    //   this.activeName = "first";
    //   this.isUpdate = true;
    // },
    // 页数改变事件
    handleSizeChange(size) {
      this.size = size;
      this.tableData = this.paging(size, this.index);
    },
    // 页码改变事件
    handleCurrentChange(current) {
      this.index = current;
      this.tableData = this.paging(this.size, current);
    },
    // 本地分页的方法
    paging(size, current) {
      const tableList = JSON.parse(JSON.stringify(this.tableCopyTableList));
      const tablePush = [];
      tableList.forEach((item, index) => {
        if (size * (current - 1) <= index && index <= size * current - 1) {
          tablePush.push(item);
        }
      });
      return tablePush;
    },
    //获取所有套餐信息
    GetClusList() {
      var that = this;
      that.loading = false;
      ajax
        .post(apiUrls.GetAllClusList)
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          // 初始化数据

          let tableCopyTableList = r.data.returnData.map((val) => {
            switch (val.clus_sex) {
              case "1":
                val.clus_sex = "男";
                break;
              case "0":
                val.clus_sex = "女";
                break;
              case "%":
                val.clus_sex = "不限";
                break;
              default:
                break;
            }
            switch (val.clusType) {
              case "01":
                val.clusType = "个人健康体检";
                break;
              case "06":
                val.clusType = "入职及其他";
                break;
              default:
                break;
            }
            return val;
          });
          that.tableAllClusData = tableCopyTableList;
          that.tableData = that.tableAllClusData.filter((item) => {
            //筛选
            return (
              item.lnc_Code.includes(this.lnc_Code) && item.clusType != null
            );
          });
          that.tableConstData=that.tableData;
          // this.screenClusData(this.lnc_Code);
        })
        .catch((err) => {
          alert("获取套餐失败,请稍后重试");
        });
    },
    AddcClus() {
      this.isUpdate = !this.isUpdate;
      return;
      this.loading = true;
      ajax.post(apiUrls.SyncClus).then((r) => {
        if (r.data.success) {
          this.$message.success(r.data.returnMsg);

          this.GetClusList();
        }
      });
    },
    //删除套餐
    DeleteClus(ids) {
      let idArr = [];
      if (ids) {
        idArr = [ids];
      } else {
        idArr = this.ids;
      }
      if (idArr.length == 0) {
        this.$message.warning("请选择套餐");
        return;
      }

      this.$confirm("确定删除此套餐吗, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "error",
      }).then(() => {
        let pData = {
          data: {
            ids: idArr,
          },
        };
        ajax
          .post(apiUrls.DeleteClusById, pData)
          .then((r) => {
            if (!r.data.success) {
              this.$message.error(r.data.returnMsg);
              return;
            }
            this.$message.success("删除成功");
            this.GetClusList();
          })
          .catch((err) => {
            console.log(err);
            this.$message.error("系统繁忙！请稍后再试");
          });
      });
    },
    //检查输入的参数
    checkAdminInfo() {
      if (!this.ClusList.clus_Code) {
        this.$message.warning("请输入套餐编码");
        return false;
      }
      if (!this.ClusList.clus_Name) {
        this.$message.warning("请输入套餐名称");
        return false;
      }
      if (this.ClusList.clus_sex == "男") {
        this.ClusList.clus_sex = "1";
      } else if (this.ClusList.clus_sex == "女") {
        this.ClusList.clus_sex = "0";
      } else {
        this.ClusList.clus_sex = "%";
      }
      return true;
    },
    //新增或者修改套餐
    addOrEditClus() {
      //参数验证
      if (!this.checkAdminInfo()) {
        return;
      }
      var pData = {
        cluster: this.ClusList,
      };
      ajax
        .post(apiUrls.UpdateClus, pData)
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          this.$message.success("操作成功");
          // this.dialogVisible = false; //成功后关闭对话框
          this.GetClusList(); //重新加载
        })
        .catch((err) => {
          console.log(err);
          this.$message.error("系统繁忙！请稍后再试");
        });
    },
    //表格筛选
    GetNewData() {
      this.tableCopyTableList = this.tableConstData.filter((item) => {
        //筛选
        return (
          !this.ClusList.clus_Name ||
          item.clus_Name.includes(this.ClusList.clus_Name)
        );
      });
      this.tableData = this.paging(this.size, this.index);
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          done();
        })
        .catch((_) => {});
    },
    // querys() {
    //   this.loadBtn();
    // },
    // // 点击选择单位
    // drawerBtn(index) {
    //   this.darwerValue = this.drawerData[index].lnc_Name;
    //   this.lnc_Code = this.drawerData[index].lnc_Code;
    //   storage.session.set(
    //     "lncList",
    //     JSON.stringify({ lnc_Name: this.lnc_Name, lnc_Code: this.lnc_Code })
    //   );
    //   this.drawer = false;
    // },
    // loadBtn() {
    //   var pData = {
    //     data: {
    //       kw: this.drawerIpnut,
    //     },
    //   };
    //   ajax
    //     .post(apiUrls.GetGetlncmenu, pData)
    //     .then((r) => {
    //       if (!r.data.success) {
    //         this.$message.error(r.data.returnMsg);
    //         return;
    //       }
    //       this.drawerData = r.data.returnData;
    //     })
    //     .catch((err) => {
    //       this.$message.error("获取单位失败,请联系管理员");
    //     });
    // },
    //获取套餐项目
    // GetClusItemComb(clus_Code) {
    //   var pData = {
    //     data: {
    //       clus_Code: clus_Code,
    //     },
    //   };
    //   ajax
    //     .post(apiUrls.GetClusItemCombByCode, pData)
    //     .then((r) => {
    //       if (!r.data.success) {
    //         alert(r.data.returnMsg);
    //         return;
    //       }
    //       let data = r.data.returnData;
    //       // 初始化数据
    //       this.checkedCities = data.clusComb;
    //       for (let i = 0; i < data.clusComb.length; i++) {
    //         this.price += data.clusComb[i].comb_Price;
    //       }
    //       this.allowAddComb = data.clusCombAdd;
    //     })
    //     .catch((err) => {
    //       alert("获取项目失败,请稍后重试");
    //     });
    // },
    //获取所有项目信息
    // GetAllClusItemComb() {
    //   var that = this;
    //   that.loading = false;
    //   ajax
    //     .post(apiUrls.GetAddClusItemList, {})
    //     .then((r) => {
    //       if (!r.data.success) {
    //         alert(r.data.returnMsg);
    //         return;
    //       }
    //       // 初始化数据
    //       that.CombAllData = JSON.parse(r.data.returnData);
    //       that.CombData = that.CombAllData;
    //     })
    //     .catch((err) => {
    //       alert("获取项目失败,请稍后重试");
    //     });
    // },
    // 编辑的回调
    editClick(row) {
      this.editFlag = true;
      this.$nextTick(() => {
        this.$refs.editClus_ref.showAddorEditDialog(row);
      });
    },
    // 新建
    creatClus() {
      this.editFlag = true;
      this.$nextTick(() => {
        this.$refs.editClus_ref.showAddorEditDialog();
      });
    },
  },
};
</script>
  
  <style  scoped>
.clusDiv {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 96%;
  margin: 20px auto;
}
.clusDiv .clusTop {
  width: 100%;
  margin-top: 10px;
  display: flex;
}
.clusMid {
  margin-top: 20px;
  width: 100%;
}
.clusMid .pageNation {
  margin-top: 10px;
}
.pageNation .el-pagination {
  display: flex;
  justify-content: flex-end;
}
.haoyuan-red {
  color: red;
}
.haoyuan-bg {
  background-color: baga(250, 250, 250, 0.3);
  color: #ccc;
  cursor: default !important;
}
.main_item {
  widows: 100%;
  height: 4.18rem;
  display: flex;
  align-items: center;
  padding: 0.4rem 0.2rem;
  background-color: #ffffff;
  font-size: 15px;
  box-shadow: 5px 5px 3px #909399;
  margin-bottom: 0.3rem;
}

.el-footer {
  background-color: #b3c0d1;
  color: #333;
  text-align: center;
  line-height: 60px;
}
.el-header {
  width: 100%;
  height: auto !important;
  background-color: #b3c0d1;
}
.container-header {
  width: 100%;
}
.container-header .container-header-form {
  width: 82%;
  display: inline-table;
}
/* .container-header .divRight {
  height: 100px;
  display: flex;
  align-items: center;
} */
.container-header .el-form-item {
  margin-bottom: 0.3rem;
}

.CombHeader {
  width: 100%;
  height: 80px;
  border-bottom: 1px solid #ccc;
  margin-top: 2.28rem;
  margin-left: 1.28rem;
  display: flex;
  align-items: center;
}
.textDivItem {
  width: 100%;
  height: 30px;
  line-height: 30px;
  text-align: left;
  font-size: 18px;
  font-family: "Microsoft YaHei", "微软雅黑", "SimHei", "黑体", "Hei",
    sans-serif;
  padding-left: 15px;
}
.CombAll {
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  margin-top: 1.18rem;
  margin-left: 2.18rem;
}
.CombAllDiv {
  /* width: 220px !important; */
  display: flex !important;
  line-height: 40px !important;
  margin-left: 10px;
  align-items: left !important;
  font-size: 15px !important;
  box-shadow: 5px 5px 3px #909399 !important;
  margin-bottom: 1.18rem !important;
}
.CombItem {
  /* width: 220px; */
  height: 40px;
  float: left;
  clear: none;
  margin-right: 10px;
  background-color: #ffffff !important;
  /* flex-wrap: wrap; */
  /* margin-right: 1rem !important; */
}
.CombItem:nth-child(6n + 1) {
  clear: left;
}
.haoyuanCCC {
  background-color: #909399 !important;
  color: #000000 !important;
}
.allowAddCombCCC {
  background-color: #7199e9 !important;
  color: #000000 !important;
}
/* .CombItem el-checkbox {
  width: 100%;
  height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
} */
.CombItemStyle {
  /* width: 220px; */
  text-align: left;
  display: flex;
  align-items: center;
}
.CombItemStyle .checkeDiv {
  width: 15px;
  height: 15px;
  margin-left: 0.2rem;
  border: 1px solid black;
  display: flex;
  align-items: center;
}
.checkeDiv .el-icon-check {
  color: #ffffff;
}
.CombItemStyle .CombItemName {
  width: 120px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-left: 0.6rem;
}
.CombItemStyle .CombItemPrice {
  width: 70px;
  font-size: 15px;
  text-align: center;
}
.asideACombItem {
  width: 350px !important;
  height: 80px !important;
  display: flex !important;
  line-height: 80px !important;
  /* align-items: center; */
  align-items: center !important;
  padding: 0.4rem 0.2rem !important;
  background-color: #ffffff !important;
  font-size: 14px !important;
  box-shadow: 5px 5px 3px #909399 !important;
  margin-bottom: 1.18rem !important;
  /* margin-right: 1rem !important; */
  margin-bottom: 0rem !important;
  border-bottom: 1px solid #ccc;
}
.asideAll {
  height: 50%;
}
.asideSelect {
  margin-top: 1.28rem;
}
.asideTitle {
  /* width: 320px !important; */
  display: flex !important;
  /* align-items: center; */
  align-items: center !important;
  padding: 0.4rem 0.2rem !important;
  background-color: #ffffff !important;
  font-size: 14px !important;
  box-shadow: 5px 5px 3px #909399 !important;
  margin-bottom: 1.18rem !important;
  /* margin-right: 1rem !important; */
  margin-bottom: 0rem !important;
  border-bottom: 1px solid #ccc;
  height: 40px !important;
  line-height: 40px !important;
}
.asideTitle .asideMaLeft {
  margin-left: 14px;
}
.asideAStyle {
  /* width: 350px; */
  display: flex;
  text-align: left;
}
.asideAStyle .asideAName {
  width: 170px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-left: 14px;
}
.asideAStyle .asideAPrice {
  width: 40px;
  text-align: right;
}
.asideAStyle .asideDelete {
  width: 15px;
  height: 15px;
  margin-left: 2.8rem;
}
.asideAStyle .asideicon-view {
  width: 15px;
  height: 15px;
  margin-left: 0.8rem;
}
.viewContainer {
  width: 15px;
  height: 15px;
}

.el-icon-view {
  position: relative;
  z-index: 2;
}
.asideicon-onView {
  width: 15px;
  height: 15px;
  position: absolute;
  top: 0;
  left: 0;
  background: linear-gradient(
    to bottom right,
    transparent 0%,
    transparent calc(50% - 1px),
    #000000 50%,
    transparent calc(50% + 1px),
    transparent 100%
  );
}
.el-textarea {
  width: 520px;
}
.button-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.button-container .el-button {
  margin-left: 0px;
  margin-top: 5px;
}
.combNameDialog {
  border-radius: 5px;
  margin: 5px auto;
  color: red;
  margin-left: 10px;
}
.ellipsis-column {
  max-width: 150px; /* 根据需要调整最大宽度 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ellipsis-column:hover {
  white-space: normal;
  overflow: visible;
  z-index: 9999; /* 可选：调整悬停时的层级 */
  background-color: #fff; /* 可选：调整悬停时的背景颜色 */
}
</style>
  