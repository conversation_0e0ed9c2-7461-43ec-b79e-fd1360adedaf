<template>
  <div class="quill-wrapper">
    <quill-editor v-model="Notice" :options="editorOptions" style="height: auto;"></quill-editor>
  </div>
</template>

<script>
import 'quill/dist/quill.snow.css';
import { quillEditor } from 'vue-quill-editor';

export default {
  components: {
    quillEditor
  },
  props: {
    value: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      Notice: this.value,
      editorOptions: {
        modules: {
          toolbar: [
            ['bold', 'italic', 'underline', 'strike'],
            [{ color: [] }, { background: [] }],
            ['clean']
          ]
        },
        placeholder: '请输入内容'
      }
    };
  },
  watch: {
    value(newValue) {
      this.Notice = newValue;
    },
    Notice(value) {
      this.$emit('input', value);
    }
  }
};
</script>

<style>
/* .quill-wrapper{
  height: auto;
} */
.ql-editor{
  height: 190px;
}
</style>