<template>
  <div>
    <div class="lncDiv">
      <div style="width: 100%">项目列表</div>
      <div class="lncTop">
        <el-input
          placeholder="分类名称(为空时默认加载全部)"
          style="width: 240px"
          v-model="combName"
          size="small"
        ></el-input>
        <el-button
          type="primary"
          icon="el-icon-search"
          @click="GetNewData"
          style="margin-left: 10px"
          size="small"
          >查询</el-button
        >
        <el-button
          type="success"
          icon="el-icon-plus"
          @click="showAddClassificationDialog()"
          size="small"
          >添加</el-button
        >
        <el-button
          type="danger"
          icon="el-icon-delete"
          @click="DeleteClassification()"
          size="small"
          >批量删除</el-button
        >
      </div>

      <div class="lncMid">
        <el-table
          :data="tableData"
          v-loading="loading"
          element-loading-text="拼命加载中"
          border
          stripe
          :fit="true"
          row-key="id"
          @selection-change="handleSelectionChangePeople"
          :height="height"
        >
          <el-table-column
            type="selection"
            width="55"
            align="center"
            :reserve-selection="true"
          ></el-table-column>
          <el-table-column
            prop="sort_Code"
            label="分类编码"
            width="200"
            align="center"
            sortable
          ></el-table-column>
          <el-table-column
            prop="sort_Name"
            label="分类名称"
            align="center"
            sortable
          ></el-table-column>
          <el-table-column
            prop="createTime"
            label="添加时间"
            align="center"
            sortable
          ></el-table-column>
          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <el-button
                @click="showAddorEditDialog(scope.row)"
                type="primary"
                plain
                >编辑</el-button
              >
              <el-button @click="DeleteClassification(scope.row.id)" type="danger" plain
                >删除</el-button
              >
              <!--单个删除-->
            </template>
          </el-table-column>
        </el-table>
        <div class="pageNation">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="index"
            :page-sizes="[50, 100, 200, 300, 500]"
            :page-size="size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableCopyTableList.length"
          ></el-pagination>
        </div>

        <!--新增/编辑对话框-->
        <el-dialog :title="dialogTitle" :visible.sync="dialogVisible">
          <!-- <el-tabs v-model="activeName" @tab-click="handleCombClick">
            <el-tab-pane label="项目信息" name="first"></el-tab-pane>
            <el-tab-pane label="项目关系" name="second"></el-tab-pane>
          </el-tabs> -->
              <el-form :label-position="'right'" label-width="80px">
                <el-form-item label="分类编码">
                  <el-input
                    v-model="Classification.sort_Code"
                    placeholder="请输入分类编码"
                    size="small"
                    readonly
                  ></el-input>
                </el-form-item>
                <el-form-item label="分类名称">
                  <el-input
                    v-model="Classification.sort_Name"
                    placeholder="请输入分类名称"
                    size="small"
                  ></el-input>
                </el-form-item>
              </el-form>
          <div slot="footer">
            <el-button type="danger" size="small" @click="dialogVisible = false"
              >取消</el-button
            >
            <el-button type="primary" size="small" @click="addOrEditLnc"
              >保存</el-button
            >
          </div>
        </el-dialog>
        <!-- 关联项目 -->
        <el-dialog
          title="关联项目"
          :visible.sync="AdddialogVisible"
          width="50%"
          :close-on-click-modal="false"
        >
          <el-form :label-position="'right'" label-width="80px">
                <el-form-item label="分类名称">
                  <el-input
                    v-model="Classification.sort_Name"
                    placeholder="请输入分类名称"
                    size="small"
                  ></el-input>
                </el-form-item>
              </el-form>
          <div slot="footer">
            <el-button type="danger" size="small" @click="AdddialogVisible = false"
              >取消</el-button
            >
            <el-button type="primary" size="small" @click="addOrClassification"
              >保存</el-button
            >
          </div>
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
import { ajax, storage } from "../../common";
// import{Toast} from 'vant';
import apiUrls from "../../config/apiUrls";
export default {
  name: "Lnclist",
  data() {
    return {
      ids: "", //id集合 用于批量删除或单个删除
      height: "calc( 100vh - 250px)",
      tableData: [], //表数据
      tableCopyTableList: [], //表数据集合
      tableConstData: [], //存放数据 用于筛选数据
      index: 1, //当前页数
      size: 50, //页码
      dialogVisible: false, //新增编辑对话框是否显示
      dialogTitle: "", //对话框的标题
      loading: false,
      //项目模型
      Classification: {
        id: "",
        sort_Code: "",
        sort_Name: "",
      },
      AdddialogVisible: false,
      itemValue: [],
      itemData: [],
      itemAllData: [],
      activeName: "first",
      firtItemName: "",
      combSwitch: false,
      activeNames: ["1", "2", "3", "4"],
      bdData: "",
      tzData: "",
      meData: "",
      combName:"",
    };
  },
  created() {
    this.GetClassificationList();
  },
  methods: {
    //获取选中行id
    handleSelectionChangePeople(rows) {
      this.ids = rows.map((row) => row.id);
    },
    showAddClassificationDialog(row){
      this.Classification.id = row ? row.id : 0;
      this.Classification.sort_Name = row ? row.sort_Name : "";
      this.AdddialogVisible = true;
    },
    //显示模态框
    showAddorEditDialog(row) {
      if (row == undefined) {
        this.dialogTitle = "新增项目";
      } else {
        this.dialogTitle = "查看/编辑项目";
      }
      this.Classification.id = row ? row.id : "";
      this.Classification.sort_Code = row ? row.sort_Code : "";
      this.Classification.sort_Name = row ? row.sort_Name : "";
      this.dialogVisible = true;
    },
    handleChangeSwitch() {
      this.Classification.combSwitch = this.combSwitch;
    },
    // 页数改变事件
    handleSizeChange(size) {
      this.size = size;
      this.tableData = this.paging(size, this.index);
    },
    // 页码改变事件
    handleCurrentChange(current) {
      this.index = current;
      this.tableData = this.paging(this.size, current);
    },
    // 本地分页的方法
    paging(size, current) {
      const tableList = JSON.parse(JSON.stringify(this.tableCopyTableList));
      const tablePush = [];
      tableList.forEach((item, index) => {
        if (size * (current - 1) <= index && index <= size * current - 1) {
          tablePush.push(item);
        }
      });
      return tablePush;
    },
    //获取所有项目信息
    GetClassificationList() {
      var that = this;
      that.loading = false;
      ajax
        .post(apiUrls.GetClassificationList)
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          // 初始化数据
          this.itemAllData = [];
          that.tableConstData = r.data.returnData;
          that.tableCopyTableList = r.data.returnData;
          this.tableConstData.forEach((item) => {
            this.itemAllData.push({
              key: item.comb_Code,
              label: item.comb_Name,
            });
          });
          this.GetNewData();
          that.tableData = that.paging(that.size, that.index);
        })
        .catch((err) => {
          alert("获取项目失败,请稍后重试");
        });
    },
    //删除项目
    DeleteClassification(ids) {
      let idArr = [];
      if (ids) {
        idArr = [ids];
      } else {
        idArr = this.ids;
      }
      if (idArr.length == 0) {
        this.$message.warning("请选择项目");
        return;
      }

      this.$confirm("确定删除此项目吗, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "error",
      }).then(() => {
        let pData = {
          data: {
            ids: idArr,
          },
        };
        ajax
          .post(apiUrls.DeleteClassificationById, pData)
          .then((r) => {
            if (!r.data.success) {
              this.$message.error(r.returnMsg);
              return;
            }
            this.$message.success("删除成功");
            this.GetClassificationList();
          })
          .catch((err) => {
            console.log(err);
            this.$message.error("系统繁忙！请稍后再试");
          });
      });
    },
    //检查输入的参数
    checkAdminInfo() {
      if (!this.Classification.sort_Code) {
        this.$message.warning("请输入分类编码");
        return false;
      }
      if (!this.Classification.sort_Name) {
        this.$message.warning("请输入分类名称");
        return false;
      }
      return true;
    },
    //添加项目分类
    addOrClassification(){
      // if (this.Classification.id) {
      //   addOrEdit = apiUrls.UpdateClassification;
      // } else {
      //   this.Classification.id = "0";
      //   addOrEdit = apiUrls.AddClassification;
      // }
      if (!this.Classification.sort_Name) {
        this.$message.warning("请输入分类名称");
        return;
      }
      this.Classification.id=0;
      var pData = {
        classification: this.Classification,
      };
      ajax
        .post(apiUrls.AddClassification, pData)
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          this.$message.success("操作成功");
          this.AdddialogVisible = false; //成功后关闭对话框
          this.GetClassificationList(); //重新加载
        })
        .catch((err) => {
          console.log(err);
          this.$message.error("系统繁忙！请稍后再试");
        });
    },
    //新增或者修改项目
    addOrEditLnc() {
      //参数验证
      if (!this.checkAdminInfo()) {
        return;
      }

      var addOrEdit = ""; //请求变量 新增/编辑 api地址不一样
      if (this.Classification.id) {
        addOrEdit = apiUrls.UpdateClassification;
      } else {
        this.Classification.id = "0";
        addOrEdit = apiUrls.AddClassification;
      }
      var pData = {
        classification: this.Classification,
      };
      ajax
        .post(addOrEdit, pData)
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          this.$message.success("操作成功");
          this.dialogVisible = false; //成功后关闭对话框
          this.GetClassificationList(); //重新加载
        })
        .catch((err) => {
          console.log(err);
          this.$message.error("系统繁忙！请稍后再试");
        });
    },
    //表格筛选
    GetNewData() {
      this.tableCopyTableList = this.tableConstData.filter((item) => {
        //筛选
        return (
          !this.combName ||
          item.sort_Name.includes(this.combName)
        );
      });
      this.tableData = this.paging(this.size, this.index);
    },
    handleCombClick() {
      // if (this.activeName == "second") {
      //   this.getCombRelation();
      // }
    },
    getCombRelation() {
      // let a = this.itemAllData.filter((item) => {
      //   return item.key == this.Classification.TZ;
      // });
      // console.log(a);
      // this.meData = this.itemAllData.filter((item) => {
      //   return item.key == this.Classification.ME;
      // });
      // this.tzData = this.itemAllData.filter((item) => {
      //   return item.key == this.Classification.TZ;
      // });
      // this.bdData = this.itemAllData.filter((item) => {
      //   return item.key == this.Classification.BD;
      // });
      // console.log(this.tzData);
      if (!this.Classification.sort) {
        this.$message.warning("未识别到分类");
      }
      // if (!this.Classification.relation) {
      //   this.$message.warning("获取项目关系出错");
      // }
      if (!this.Classification.comb_Code) {
        this.$message.warning("未识别到项目编码");
      }
      var pData = {
        combRin: {
          sort: this.Classification.sort,
          comb_Code: this.Classification.comb_Code,
        },
      };
      ajax
        .post(apiUrls.getCombRelation, pData)
        .then((r) => {
          this.meData = r.data.returnData.me;
          this.tzData = r.data.returnData.tz;
          this.bdData = r.data.returnData.bd;
          // this.$message.success("更新成功");
          this.visibleItemList = false;
        })
        .catch((err) => {
          console.log(err);
          this.$message.error("系统繁忙！请稍后再试");
        });
    },
    //关联项目
    connectItems(firtName) {
      storage.session.set("value", this.Classification.comb_Code);
      this.firtItemName = firtName;
      this.itemValue = [];
      this.itemData = this.itemAllData.filter(
        (item) => item.key !== this.Classification.comb_Code
      );
      if (firtName == "ME") {
        this.itemValue = this.meData.map((i) => i.comb_Code);
      } else if (firtName == "BD") {
        this.itemValue = this.bdData.map((i) => i.comb_Code);
      } else if (firtName == "TZ") {
        this.itemValue = this.tzData.map((i) => i.comb_Code);
      } else {
        this.$message.error("系统繁忙！请稍后再试");
      }
      this.visibleItemList = true;
    },
    //关联项目确认
    confirmConnectItem() {
      var that = this;
      let value = storage.session.get("value");
      let code = this.firtItemName;
      if (!this.Classification.sort) {
        this.$message.warning("未识别到分类");
      }
      if (!code) {
        this.$message.warning("获取项目关系出错");
      }
      if (!value) {
        this.$message.warning("未识别到项目编码");
      }
      this.combs = this.itemValue.join(",");
      var pData = {
        combRin: {
          sort: this.Classification.sort,
          comb_Code: value,
          relation: code,
          sonComb: this.combs,
        },
      };
      ajax
        .post(apiUrls.UpdateCombRelation, pData)
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          this.$message.success("更新成功");
          this.getCombRelation();
        })
        .catch((err) => {
          console.log(err);
          this.$message.error("系统繁忙！请稍后再试");
        });
    },
    transferChange(value, direction, moveKeys) {},
  },
};
</script>

<style lang="scss">
.lncDiv {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 96%;
  margin: 20px auto;
  .lncTop {
    width: 100%;
    margin-top: 10px;
  }
  .lncMid {
    margin-top: 20px;
    width: 100%;
    .pageNation {
      margin-top: 10px;
      .el-pagination {
        display: flex;
        justify-content: flex-end;
      }
    }
  }
}
.custom-transfer {
  width: 100% !important;
}
.custom-transfer .el-transfer-panel,
.custom-transfer .el-transfer-panel:last-child {
  width: 40% !important;
  height: 500px;
}
.el-transfer-panel,
.custom-transfer .el-transfer-panel:last-child {
  height: 500px;
}
.el-checkbox-group el-transfer-panel__list is-filterable {
  width: 100%;
  height: 500px;
}
.el-transfer-panel__body {
  height: 100%;
}
.el-transfer-panel__list.is-filterable {
  height: 80%;
}
.el-collapse-item {
  margin-top: 20px;
  border-bottom: 1px solid #ebeef5;
}
.el-collapse-item__header {
  height: 20px;
}
.el-collapse-item__content {
  padding-bottom: 0px;
}
.el-collapse-item__content_lay {
  height: 35px;
}
</style>
