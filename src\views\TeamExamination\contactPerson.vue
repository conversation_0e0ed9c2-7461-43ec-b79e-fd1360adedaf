<template>
    <!--单位联系人对话框-->
    <el-dialog title="单位联系人" :visible.sync="contactPersonDialogVisible">
        <el-form :label-position="'right'" label-width="80px" size="small">
            <el-form-item label="姓名">
                <el-input v-model="LncList.conName" placeholder="请输入姓名" size="small"></el-input>
            </el-form-item>
            <el-form-item label="联系电话">
                <el-input v-model="LncList.conTel" placeholder="请输入联系电话" size="small"></el-input>
            </el-form-item>
        </el-form>
        <div slot="footer">
            <el-button type="danger" size="small" @click="contactPersonDialogVisible = false">取消</el-button>
            <el-button type="primary" size="small" @click="EditLnc">确定</el-button>
        </div>
    </el-dialog>
</template>
  
<script>
import { ajax } from "../../common";
import apiUrls from "../../config/apiUrls";
import { storage} from "@/common";
export default {
    name: "contactPerson",
    // props: {
    //     lnc_Code: {
    //         type: String, // 注意这里是 Array 而不是 List  
    //         default: '' // 使用箭头函数作为默认值  
    //     }
    // },
    components: {},
    data() {
        return {
            //单位模型
            // LncList: {
            //     id: "",
            //     lnc_Code: "",
            //     lnc_Name: "",
            //     lnc_State: "",
            //     Notice: "",
            //     conName: "",
            //     conTel: "",
            //     specifyDate: "",
            //     isAllBook: "",
            // },
            // lnc_Code:this.lnc_Code,
            LncList: [], // 使用 this.LncList 作为内部响应式数据的来源 
            contactPersonDialogVisible: false,
        };
    },
    methods: {
        //显示模态框
        showAddorEditDialog(row) {
            this.LncList = row ? row : []
            this.contactPersonDialogVisible = true;
        },
        //检查输入的参数
        checkAdminInfo() {
            if (!this.LncList.lnc_Code) {
                this.$message.warning("请输入单位编码");
                return false;
            }
            if (!this.LncList.lnc_Name) {
                this.$message.warning("请输入单位名称");
                return false;
            }
            if (!this.LncList.conName) {
                this.$message.warning("请输入姓名");
                return false;
            }
            if (!this.LncList.conTel) {
                this.$message.warning("请输入手机号码");
                return false;
            }
            return true;
        },
        EditLnc() {
            //参数验证
            if (!this.checkAdminInfo()) {
                return;
            }
            var pData = {
                tjlnc: {
                    lnc_Code: this.LncList.lnc_Code,
                    conName: this.LncList.conName,
                    conTel: this.LncList.conTel,
                }
            };
            ajax
                .post(apiUrls.UpdateLncContact, pData)
                .then(r => {
                    if (!r.data.success) {
                        alert(r.data.returnMsg);
                        return;
                    }
                    this.$message.success("操作成功");
                    storage.session.set("lncList",JSON.stringify(this.LncList));
                    this.contactPersonDialogVisible = false; //成功后关闭对话框
                    // this.GetLncList(); //重新加载
                })
                .catch(err => {
                    console.log(err);
                    this.$message.error("系统繁忙！请稍后再试");
                });
        },
    }
};
</script>
  
<style lang="scss">
.lncDiv {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 96%;
    margin: 20px auto;

    .lncTop {
        width: 100%;
        margin-top: 10px;
    }

    .lncMid {
        margin-top: 20px;
        width: 100%;

        .pageNation {
            margin-top: 10px;

            .el-pagination {
                display: flex;
                justify-content: flex-end;
            }
        }
    }
}
</style>
  