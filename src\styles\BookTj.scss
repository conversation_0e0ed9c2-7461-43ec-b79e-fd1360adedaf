@mixin flexBasic {
  display: flex;
  justify-content: center;
  align-items: center;
}
@mixin flexLeft {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.TjBox {
  width: 100%;
  // height: calc(100vh - 88px);
  margin-top: 10px;
  background: white;
  display: flex;
  align-items:center;
  flex-direction: column;
  // border: 1px solid teal;
  // 单位
  #company{
    height: 50px !important;
    display: flex;
    justify-content: space-between;
  }
  .company {
    width: calc(  100% - 20px);
    height: 80px !important;
    @include flexLeft;
    // flex-direction: column;
    margin-top: 10px;
    border-bottom: 1px solid #e5e5e5;
    // 单位切换
    .companySwitch {
      width: 100%;
      height: 50px;
      line-height: 50px;
      .companylnc_Name{
        font-size: 20px;
      }
      // 抽屉
      .drawerAll{
        width: 100%;
        height: 100vh;
        padding: 10px;
        display: flex;
        align-items: center;
        flex-direction: column;
        background: white !important;
        box-sizing: border-box;
        .drawerTop{
          width: 100%;
          height: 40px;
          margin: 0;
          @include flexLeft;
          border-bottom:1px solid #e5e5e5;
        }
        .drawerSearch{
          width:100%;
          height: 60px;
          @include flexBasic;
          .searchTwo{
            width: 100%;
            height: 40px;
            @include flexBasic;
            .el-input{
              width: 240px !important;
            }
            .el-button{
              margin-left: 10px;
            }
          }
        }
        .drawerList{
          width:100%;
          height: calc( 100vh - 120px);
          // margin-top: 10px;
          overflow-y: auto;
          border-top: 1px solid #e5e5e5;
          .drawerTr1{
            width: 100%;
            height: 35px;
            @include flexBasic;
            border-bottom: 1px solid #e5e5e5;
            background-color: #b6acac;
            font-weight: 600;
            .drawerTd1{
              width: 30%;
              height: 100%;
              @include flexLeft;
              border-left: 1px solid #e5e5e5;
              border-right: 1px solid #e5e5e5;
              span{
                margin-left: 10px;
              }
            }
            .drawerTd2{
              width: 70%;
              height: 100%;
              @include flexLeft;
              border-right: 1px solid #e5e5e5;
              span{
                margin-left: 10px;
              }
            }
          }
          .drawerTr2{
            width: 100%;
            height: 35px;
            @include flexBasic;
            border-bottom: 1px solid #e5e5e5;
            background-color:#F5F5F6;
            cursor: pointer !important;
            .drawerTd3{
              width: 30%;
              height: 100%;
              @include flexLeft;
              border-left: 1px solid #e5e5e5;
              border-right: 1px solid #e5e5e5;
              span{
                margin-left: 10px;
              }
            }
            .drawerTd4{
              width: 70%;
              height: 100%;
              @include flexLeft;
              border-right: 1px solid #e5e5e5;
              span{
                margin-left: 10px;
              }
            }
          }
          // v-for循环 hover样式
          .hoverIndex{
            width: 100%;
            height: 35px;
            @include flexBasic;
            border-bottom: 1px solid #e5e5e5;
            background-color:#d9d9f7;
            cursor: pointer !important;
            .drawerTd3{
              width: 30%;
              height: 100%;
              @include flexLeft;
              border-left: 1px solid #e5e5e5;
              border-right: 1px solid #e5e5e5;
              span{
                margin-left: 10px;
              }
            }
            .drawerTd4{
              width: 70%;
              height: 100%;
              @include flexLeft;
              border-right: 1px solid #e5e5e5;
              span{
                margin-left: 10px;
              }
            }
          }

        }

      }
    }
    .companyNow {
      width: 100%;
      height: 40px;
      display: flex;
      align-items: flex-end;
      // 单位日期
      .companyDate {
        width: 80%;
        height: 30px;
      }
      // 单位文字
      .companySpan {
        width: 50%;
        height: 30px;
        line-height: 30px;
        text-align: right;
        font-size: 18px;

        color: #ed5565;
        span {
          margin-right: 80px;
        }
      }
    }
  }
// 号源
.sourceAll{
  width: calc(  100% - 20px);
  margin-top: 10px;
  // height:  calc(  100% - 100px);
  .sourceName{
    width: 100%;
    height: 40px;
    .sourceNow{
      display: inline-block;
    border: 1px solid #e7eaec;
    border-bottom-color: transparent;
    background: white;
    font-weight: 600;
    padding: 10px 20px 10px 25px;
    border-radius: 4px 4px 0 0;
    }
  }
  .sourceTable{
    width: 100%;
    padding: 10px;
    border: 1px solid #e7eaec;
    .sourceList{
      width: 100%;
      height: 300px;
      // border: 1px solid teal;
      @include flexLeft;
      border-bottom: 1px solid #dddee1;
      .sourceEvery{
        flex-direction: column;
        height: 300px;
        .sourceDiv1{
          width: 100%;
          height: 60px;
          background: #abd6e7;
          @include flexBasic;
          border: 1px solid #dddee1;
          border-bottom: 1px solid transparent;
        }
        .sourceDiv{
          width: 100%;
          height: 60px;
          @include flexBasic;
          border: 1px solid #dddee1;
          border-bottom: 1px solid transparent;
          cursor: pointer;
        }
      }
    }
  }
}



}
.el-select-dropdown__item.hover,
.el-select-dropdown__item:hover {
  background: #409eff !important;
  color: white !important;
}

// 人数弹窗 表格
.nowNumber{
  width: 100%;
  height: 143px;
  border: 1px solid #4a4a4a;
  // border-bottom: 0;
  margin-top: 10px;
  border-radius: 5px;
  .nowNumberOL{
    width: 100%;
    height: 35px;
    border-bottom: 1px solid #4a4a4a;
    display: flex;
    .nowDivL{
      width: 25%;
      height: 100%;
    border-right: 1px solid #4a4a4a;
      @include flexBasic;
      background:-webkit-gradient(linear,left bottom,left top,color-stop(0,#eee),color-stop(1,#fff));
    }
    .nowDivR{
      width: 75%;
      height: 100%;
      @include flexLeft;
      span{
      margin-left: 15px;
      }
      .el-input__inner{
        height: 34px !important;
        line-height: 34px !important;
        border: 0;
      }
    }
  }
}
