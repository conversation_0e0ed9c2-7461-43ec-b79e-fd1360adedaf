<template>
  <div>
    <tinymce v-model="Model.content"/>
    <div class="careBtn">
      <el-button type="primary" @click="SaveContent" icon="el-icon-folder-checked">立即保存</el-button>
    </div>
  </div>
</template>


<script>
import Tinymce from "@/components/Tinymce";
import { ajax } from "../../common";
import apiUrls from "../../config/apiUrls";
export default {
  name: "NoteIndex",
  components: { Tinymce },
  data() {
    return {
      Model: {
        id: 0, //医院介绍id
        titleName: "体检须知", //标题名称
        titleCode: "", //标题编码 保存数据后台所用
        content: "", //富文本框的内容
      },
    };
  },
  mounted() {
   
  },
  created() {
    this.GetHistoryData();
  },
  methods: {
    //通过标题查询历史数据显示
    GetHistoryData() {
      var that = this;
      var pData = {
        titleName: that.Model.titleName,
      };
      ajax
        .post(apiUrls.GetNoticeInfo, pData,{nocrypt:true})
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          that.Model = r.data.returnData;
          //设置富文本框的值
          //获取内容：tinyMCE.activeEditor.getContent()
          //设置内容：tinyMCE.activeEditor.setContent(“需要设置的编辑器内容”)
         
          tinyMCE.editors[0].setContent(that.Model.content);
        })
        .catch((err) => {
          console.log(err);
        });
    },

    //保存富文本框内容
    SaveContent() {
      var that = this;
      // var user = JSON.parse(sessionStorage.getItem("user"));
      // //获取操作人员
      // that.HospitalIntroduce.operator = user.admin_Name;
      var pData = that.Model;
      ajax
        .post(apiUrls.UpdateNoticeInfo, pData,{nocrypt:true})
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
           this.$message.success("保存成功");
        })
        .catch((err) => {
          console.log(err);
        });
    },
  },
};
</script>
<style lang="scss"  scoped>
@mixin toCenter($justify, $items) {
  display: flex;
  justify-content: $justify;
  align-items: $items;
}
.mce-panel {
  box-sizing: border-box;
}

.careBtn {
  flex: 1;
  height: 10%;
  @include toCenter(flex-end, center);
}
</style>
