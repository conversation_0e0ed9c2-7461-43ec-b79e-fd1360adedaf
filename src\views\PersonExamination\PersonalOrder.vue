<template>
  <div>
    <div class="ordDiv">
      <div style="width: 100%">订单列表</div>
      <div class="lncTop">
        <el-form :inline="true" :model="filters" size="mini" style="margin-bottom: 0px;">
          <el-form-item label="姓名">
            <el-input placeholder="姓名(为空时默认加载全部)" v-model="filters.name" size="small"></el-input>
          </el-form-item>
          <el-form-item label="体检日期">
            <el-date-picker v-model="filters.beginTime" type="daterange" align="right" unlink-panels range-separator="至"
              start-placeholder="开始日期" end-placeholder="结束日期" format="yyyy 年 MM 月 dd 日" :picker-options="pickerOptions"
              size="small" @change="handlebeginTimeChange"></el-date-picker>
          </el-form-item>
          <el-form-item label="套餐名称">
            <el-input placeholder="套餐名称" v-model="filters.clus_Name" size="small"></el-input>
          </el-form-item>
        </el-form>
        <!-- <div style="display: flex;">
          <div class="getNew">
            <el-input placeholder="姓名(为空时默认加载全部)" v-model="Orderlist.name" size="small"></el-input>
          </div>
          <div class="getSele">
            <el-select v-model="Orderlist.state" placeholder="全部" size="small">
              <el-option label="全部" value="全部"></el-option>
              <el-option label="已预约" value="已预约"></el-option>
              <el-option label="已撤销" value="已撤销"></el-option>
              <el-option label="已导出" value="已导出"></el-option>
            </el-select>
          </div>
          <div class="getPicker">
            <el-date-picker v-model="beginTime" type="daterange" align="right" unlink-panels range-separator="至"
              start-placeholder="开始日期" end-placeholder="结束日期" format="yyyy-MM-dd " :picker-options="pickerOptions"
              size="small" @change="handlebeginTimeChange"></el-date-picker>
          </div>
          <div class="getNew" style="margin-left: 10px;display: flex;align-items: center;">
            <el-checkbox v-model="StateChecked" style="font-size: 16px;">仅查看有效订单</el-checkbox>
          </div>
          <el-button type="primary" icon="el-icon-search" @click="GetOrderList" size="small"
            style="margin-left: 10px">查询</el-button>
        </div> -->
      </div>
      <div class="lncTop table_top">
        <div>
          <el-form :inline="true" :model="filters" size="mini" style="margin-bottom: 0px;">
            <el-form-item label="体检类型">
              <el-select v-model="filters.type" placeholder="全部" size="small">
                <el-option label="全部" value="全部"></el-option>
                <el-option label="个人体检" value="个人体检"></el-option>
                <el-option label="入职体检" value="入职体检"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="状态">
              <el-select v-model="filters.state" placeholder="状态" size="small">
                <el-option label="全部" value="全部"></el-option>
                <el-option label="已预约" value="已预约"></el-option>
                <el-option label="已撤销" value="已撤销" :disabled="StateChecked == true ? true : false"></el-option>
                <el-option label="已导出" value="已导出"></el-option>
                <el-option label="异常订单" value="异常订单" :disabled="StateChecked == true ? true : false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="是否包含彩超">
              <el-select v-model="filters.colorDoppler" placeholder="状态" size="small">
                <el-option label="全部" value="全部"></el-option>
                <el-option label="包含彩超" value="包含彩超"></el-option>
                <el-option label="不包含彩超" value="不包含彩超"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <div class="getSeleState">
                <el-checkbox v-model="StateChecked" style="font-size: 16px;">仅查看有效订单</el-checkbox>
              </div>
            </el-form-item>
          </el-form>
        </div>
        <div style="margin-right: 20px;">
          <el-button type="primary" @click="GetOrderList">查询</el-button>
        </div>
      </div>
      <div class="lncMid" style="border: 1px solid rgb(197, 197, 197);">
        <div class="lncMid_divRight">
          <div>
            <div class="toggleSelection" style="margin-left: 10px;">
              <div style="display: flex;">
                <el-button @click="toggleSelection(tableData)" size="medium">全选</el-button>
                <el-button @click="toggleSelection()" size="medium">取消全选</el-button>
                <div class="toggleSelectionCon">共选择（<span style="color:rgb(204, 162, 23);">{{ ids.length }}</span>）条</div>
              </div>
              <div></div>
            </div>
          </div>
          <div class="divRight">
            <el-button icon="el-icon-bell" @click="SyncOrderByOrderIds()" size="small">导入内院</el-button>
            <el-button icon="el-icon-bell" @click="showSMSTemplate()" size="small">发送短信</el-button>
            <!-- <el-button icon="el-icon-files" @click="exportExcel" size="small"
              style="margin-left: 10px">Excel导出</el-button> -->
              <el-popover placement="bottom" title="Excle导出" width="300" trigger="hover">
              <div style="display: flex;">
                <el-button icon="el-icon-files" type="success" @click="exportExcel" size="small"
                  style="margin-left: 10px">Excel导出</el-button>
                <el-button icon="el-icon-files" type="success" @click="ExportOrderExcel" size="small"
                  style="margin-left: 10px">Excel导出订单详细</el-button>
              </div>
              <!-- <el-button slot="reference">Excel导出</el-button> -->
              <el-button icon="el-icon-files" slot="reference" size="small"
                style="margin-left: 10px">Excel导出</el-button>
            </el-popover>
            <el-button icon="el-icon-delete" @click="CancelOrder()" size="small">批量撤销</el-button>
          </div>
        </div>
        <el-table ref="tableData" :data="tableData" border stripe :default-sort="{ prop: 'id', order: 'descending' }"
          @selection-change="handleSelectionChangePeople" id="ordertable" :height="height" row-key="id" :fit="true">
          <el-table-column type="selection" width="55" :reserve-selection="true"></el-table-column>

          <el-table-column :prop="item.propValue" :label="item.label" :width="item.width" align="center"
            v-for="(item, index) in propData" :key="index"></el-table-column>

          <el-table-column label="操作" fixed="right" align="center" width="150">
            <template slot-scope="scope">
              <el-button @click="updateBeginTime(scope.row)" type="text" plain
                v-if="scope.row.state == '待支付' || scope.row.state == '已预约'">改期</el-button>
              <el-button @click="SyncOrderByOrderIds(scope.row.id)" type="text"
                v-if="scope.row.state == '待支付' || scope.row.state == '已预约'" plain>导入内网</el-button>
              <el-button type="text" @click="CancelOrder(scope.row.id)" plain
                v-if="scope.row.state != '已撤销' && scope.row.state != '已导出'">撤销</el-button>
              <el-button type="text" @click="showViewOrder(scope.row)" plain>查看</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pageNation">
          <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="index"
            :page-sizes="[50, 100, 200, 300, 500]" :page-size="size" layout="total, sizes, prev, pager, next, jumper"
            :total="tableCopyTableList.length"></el-pagination>
        </div>
        <div class="lncMid" id="lncMid">
          <div class="pageNation"></div>
          <el-drawer title="订单详情" :visible.sync="drawerOrder">
            <div class="lncMid" id="lncMid">
              <div class="pageNation">订单信息</div>
              <div class="OrderDe">
                <div class="OrderDein">
                  <span>预约号:</span><span>{{ Order.regno }}</span>
                </div>
                <div class="OrderDein">
                  <span>姓名:</span><span>{{ Order.name }}</span>
                </div>
                <div class="OrderDein">
                  <span>证件号:</span><span>{{ Order.idCard }}</span>
                </div>
                <div class="OrderDein">
                  <span>联系电话:</span><span>{{ Order.tel }}</span>
                </div>
                <div class="OrderDein">
                  <span>套餐名称:</span><span>{{ Order.clus_Name }}</span>
                </div>
                <div class="OrderDein">
                  <span>订单状态:</span><span>{{ Order.state }}</span>
                </div>
                <div class="OrderDein">
                  <span>体检时间:</span><span>{{ Order.begin_Time }}</span>
                </div>
                <div class="OrderDein">
                  <span>预约时间段:</span><span>{{ Order.sumtime_Name }}</span>
                </div>
                <div class="OrderDein">
                  <span>预约时间:</span><span>{{ Order.created_Time }}</span>
                </div>
                <div class="OrderDein" v-if="Order.company_Name">
                  <span>单位名称:</span><span>{{ Order.company_Name }}</span>
                </div>
                <div class="OrderDein">
                  <span>流水号:</span><span>{{ Order.out_trade_no }}</span>
                </div>
                <div class="OrderDein">
                  <span>体检类型:</span><span>{{ Order.type }}</span>
                </div>
                <div class="OrderDein">
                  <span>异常描述:</span><span>{{ Order.errorMsg }}</span>
                </div>
              </div>
            </div>
            <div class="lncMid" id="lncMid" v-if="CombData.length != 0">
              <div class="pageNation">预约项目</div>
              <el-table :data="CombData" v-loading="loading" element-loading-text="拼命加载中" height="calc( 100vh - 450px)">
                <el-table-column prop="comb_Name" label="项目名称" align="center"></el-table-column>
                <el-table-column prop="comb_Price" label="价格" align="center"></el-table-column>
              </el-table>
            </div>
          </el-drawer>
        </div>
        <el-dialog title="发送短信" :visible.sync="SMSdialogVisible" width="80%" id="smsSMSdialogVisible" top="5vh">
          <div :style="{ height: dialogHeight }">
            <span>用户数量：</span><span style="font-size: 18px;color: rgb(219, 33, 0);">{{ ids.length }}个</span>
            <div class="textareaDiv">
              <div class="textareaCon">
                <div style="margin-top: 20px; margin-bottom: 20px; height: 10px">
                  <el-form :model="SMSTemplateList">
                    <!-- <el-form-item label="模板名称" label-width="80px">
                      <el-select v-model="SMSTemplateName" placeholder="请选择" @change="selectSMSTemplateTask">
                        <el-option v-for="(item, index) in tableSMSConstData" :key="index" :label="item.sms_Name"
                          :value="item.sms_Code">
                        </el-option>
                      </el-select>
                    </el-form-item> -->
                    <el-form-item>
                      <template>
                        <div class="textareaDiv">
                          <div class="textareaCon">
                            <div style="margin-top: 20px; margin-bottom: 20px; height: 10px">
                              <div id="div-template" class="templet-header">
                                <div class="keywords">
                                  <div class="detail" id="detail">
                                    <span>关键字：</span>
                                    <ul id="memberInfo">
                                      <li isFocus="true" v-for="(nounId, index) in SMSnounIds" :key="index"
                                        @click="selectDetail">{{
                                          nounId.name }}</li>
                                    </ul>
                                  </div>
                                </div>

                                <div class="templet-input">
                                  <div><span class="title">模板内容：</span>
                                    <div class="text">
                                      <textarea class="infoText" style="height: 148px;" v-model="SMSContent"
                                        @click="handleTextareaClick" @keyup="handleKeyup" @keydown="handleKeydown"
                                        ref="textarea" :isFocus="true" :rows="5"></textarea>
                                    </div>
                                  </div>
                                  <div><span class="title">模板预览：</span>
                                    <div class="text">
                                      <div class="textareaCon">
                                        <!-- <span>预览：</span> -->
                                        <div class="infoText" v-html="SMSPreview" style="background-color: aliceblue;">
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div></div>
                            </div>
                          </div>
                        </div>
                      </template>
                    </el-form-item>
                    <!-- <el-form-item label-width="80px">
                      <template>
                        <el-button
                          class="textareaNou"
                          v-for="(nounId, index) in SMSnounIds"
                          :key="index"
                          @click="textareaAdd(nounId)"
                          type="blue"
                          size="small"
                          >{{ nounId.name }}</el-button
                        >
                      </template>
                    </el-form-item>
                    <el-form-item label-width="80px">
                      <div></div>
                      <div
                        id="SMSContentTextArea"
                        class="infoText"
                        ref="SMSContentTextArea"
                        :contenteditable="true"
                        v-html="SMSContent"
                        @input="btnHandelClick"
                      ></div>
                    </el-form-item>
                    <el-form-item label-width="80px">
                      <div class="textareaCon">
                        <span>短信预览：</span>
                        <div class="infoText" v-html="SMSPreview"></div>
                      </div>
                    </el-form-item> -->
                  </el-form>
                  <br />
                  <div></div>
                </div>
              </div>
            </div>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button @click="SMSdialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="SendReminderMessages">确 定</el-button>
          </span>
        </el-dialog>
        <el-dialog :title="updateTimeDialogTitle" :visible.sync="updateTimedialogVisible" width="40%" top="5vh">
          <div class="DYYDiv" id="updateTimeDialog" :style="{ height: dialogHeight }">
            <div class="selectTimeDiv">
              <div class="DYConfirmDiv" style="display: flex;"><span>1、选择时间</span>
                <div style="margin-left: 10px;"><el-checkbox v-model="bookChecked" style="width: 16px;height: 16px;"
                    @change="changeBookChecked">占用全局号源</el-checkbox></div>
              </div>
              <div id="all" style="margin: auto;" v-if="bookChecked">
                <div id="calendar" style="height: 80%;">
                  <div class="month">
                    <div class="prevMonth" @click="prevMonth" v-show="pre">
                      上一月
                    </div>
                    <div class="prevMonth_" v-show="pre_"></div>
                    <div class="year-month">
                      <span class="choose-year">{{ currentDateStr }}</span>
                    </div>
                    <div class="nextMonth" @click="nextMonth">下一月</div>
                  </div>
                  <div class="weekdays">
                    <div class="week-item" v-for="item of weekList" :key="item">
                      {{ item }}
                    </div>
                  </div>
                  <div class="calendar-inner">
                    <div class="calendar-item" v-for="(item, index) of calendarList" :key="index"
                      v-bind:class="[item.disable ? 'disabled' : '']">
                      <div @click="
                        item.Thing === ''
                          ? ''
                          : matchDate(item.ThingName)
                            ? dayClick(item.date, item.ThingName, index)
                            : ''
                        " :class="ClassKey === calendarList[index].value
    ? 'chooseDay'
    : ''
    ">
                        <div class="calendarDate" v-bind:class="switchHaoyuanClass(item.ThingName)">
                          {{ item.date }}
                        </div>
                        <div class="calendarThing" v-bind:class="switchHaoyuanClass(item.ThingName)">
                          {{ item.Thing }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="sumtime" v-show="stateShow">
                  <div :class="items.team_Surplus === 0 ? 'timeTwo' : 'timese'" v-for="(items, index) in sumtimeList"
                    :key="index" @click="timeBtn(items, index)">
                    <span :class="TeamSpans === index ? 'TeamSpan' : 'TeamSpanTwo'
                      ">
                      {{ items.sumtime_Name }}
                    </span>
                  </div>
                </div>
              </div>
              <div id="all" style="margin: auto;width: 80%;" v-else>
                <div id="calendar">
                  <div class="month">
                    <div class="prevMonth" @click="prevMonth" v-show="pre">
                      上一月
                    </div>
                    <div class="prevMonth_" v-show="pre_"></div>
                    <div class="year-month">
                      <span class="choose-year">{{ currentDateStr }}</span>
                    </div>
                    <div class="nextMonth" @click="nextMonth">下一月</div>
                  </div>
                  <div class="weekdays">
                    <div class="week-item" v-for="item of weekList" :key="item">
                      {{ item }}
                    </div>
                  </div>
                  <div class="calendar-inner">
                    <div class="calendar-item" v-for="(item, index) of calendarList" :key="index"
                      v-bind:class="[item.disable ? 'disabled' : '']">
                      <div @click="daybookCheckedClick(item.date, item.ThingName, index)"
                        :class="ClassKey === calendarList[index].value ? 'chooseDay' : ''">
                        <div class="calendarDate" v-bind:class="switchHaoyuanClass(item.ThingName)">
                          {{ item.date }}
                        </div>
                        <div class="calendarThing" v-bind:class="switchHaoyuanClass(item.ThingName)">{{ item.Thing }}
                        </div>
                        <!-- <div class="calendarThing" v-bind:class="switchHaoyuanClass(item.ThingName)">
                          {{ item.Thing }}
                        </div> -->
                      </div>
                    </div>
                  </div>
                </div>
                <div class="sumtime" v-show="stateShow">
                  <div class="timese" v-for="(items, index) in sumtimeList" :key="index"
                    @click="timeBookCheckedBtn(items, index)">
                    <span :class="TeamSpans === index ? 'TeamSpan' : 'TeamSpanTwo'">
                      {{ items.sumtime_Name }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div slot="footer">
            <el-button type="danger" size="small" @click="updateTimedialogVisible = false">取消</el-button>
            <el-button type="success" size="small" @click="updateTimeConfirm">确定</el-button>
          </div>
        </el-dialog>
      </div>
    </div>
  </div>
</template>
  
<script>
import temporaryData from "../../common/temporaryData.js"; //号源临时假数据
import { ajax } from "../../common/ajax";
import apiUrls from "../../config/apiUrls";
import { toolsUtils } from "../../common/toolsUtils";
import Vue from "vue";
import { storage } from "@/common";
export default {
  name: "BookTj",
  data() {
    return {
      baseData: Vue.prototype.baseData,
      drawer: false, //切换号源抽屉
      lnc_Name: "", //号源值
      drawerIpnut: "", //单位编码名称
      hoverIndex: -1, //表示当前hover的是第几个div 初始为 -1 或 null 不能为0 0表示第一个div
      sourceValue: "总号源",
      everyWidth: "width:calc( 100% / 16)",
      lnc_Code: "0451",
      dialogVisible: false, //弹窗
      timeValue: "", //选择日期
      checked: false, //是否设置休息日
      appointments: 0, //预约人数
      appointments2: 0,
      surplus: 0, //剩余人数
      inputNumber: 10, //人数
      inputNumbe2: 0,
      surplus2: 0,
      Timeslott: [],
      personSumData: [],
      yearOptions: [], //年份下拉
      yearValue: "",
      monthOptions: [], //月份下拉
      monthValue: "",
      drawerData: [], //单位列表
      everyAll: [
        {
          everyData: [],
        },
        {
          everyData: [],
        },
      ], //日期数据
      ids: "", //id集合 用于批量删除或单个删除
      height: "calc( 100vh - 250px)",
      tableData: [], //表格数据源
      lnclist: [], //单位下拉框
      tableCopyTableList: [], //保存数据 用于分页
      tableConstData: [], //存放数据 用于筛选数据
      index: 1, //默认在第一页
      size: 100, //默认一页50条数据
      Height: "calc( 100vh - 250px)",
      dialogHeight: "calc( 100vh - 350px)",
      //订单模型
      Orderlist: {
        name: "", //姓名,
        company_Name: [], //单位名称
        state: "", //订单状态
        type: "", //订单类型
      },
      //日期控件的属性
      pickerOptions: {
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      propData: [
        //width  有需要就加，其余的自适应
        {
          propValue: "name",
          label: "姓名",
        },
        {
          propValue: "idCard",
          label: "身份证",
          width: "180",
        },
        {
          propValue: "tel",
          label: "联系电话",
        },
        {
          propValue: "type",
          label: "订单类型",
        },
        {
          propValue: "regno",
          label: "预约编号",
        },
        {
          propValue: "clus_Name",
          label: "套餐名称",
        },
        {
          propValue: "begin_Time",
          label: "体检时间",
        },
        {
          propValue: "sumtime_Name",
          label: "体检时间段",
        },
        {
          propValue: "price",
          label: "价格",
        },
        {
          propValue: "state",
          label: "订单状态",
        },
        {
          propValue: "operator",
          label: "操作员",
        },
        {
          propValue: "repNotif",
          label: "报告状态",
        },
      ],
      drawerOrder: false,
      CombAllData: [],
      CombData: [],
      Order: {
        id: "",
        name: "",
        idCard: "",
        tel: "",
        clus_Name: "",
        state: "",
        begin_Time: "",
        created_Time: "",
        regno: "",
        company_Name: "",
        out_trade_no: "",
        sumtime_Name: "",
        type: "",
        errorMsg: "",
      },
      SMSdialogVisible: false,
      SMSContent: "",
      SMSParameter: "",
      SMSPreview: "",
      SMSTemplateList: {
        id: "",
        sms_Code: "",
        sms_Name: "",
        sms_Content: "",
        Preview: "",
        nounId: "",
        state: "",
        parameter: "",
        updateTime: "",
      },
      SMSNounList: [],
      SMSnounIds: [],
      userName: "",

      overallLoading: "",
      expireTime: "",
      stateShow: false,
      ClassKey: "", //点击选择日期
      years: "", //年
      months: "", //月
      pre: false, //上一月
      pre_: true, //上一月代替
      week: 0, //周几
      current: {}, //当前时间
      calendarList: [], //用于遍历显示
      shareDate: new Date(), //享元模式，用来做优化的,
      weekList: ["日", "一", "二", "三", "四", "五", "六"], // 新增
      CardData: [
        {
          CardText: "入职套餐（不限性别）",
        },
      ],
      sumtimeList: [], //时段数据
      TeamSpans: "", //样式判断
      sumtime_Code: "", //时段编码
      sumtime_Name: "", //时段名称
      searchClus_Name: "",
      CurrentClus: [],
      teamName: "",
      ValidityPeriod: false,
      updateTimedialogVisible: false,
      ClassKey: "", //点击选择日期
      updateTimeDialogTitle: "修改体检时间",
      orderId: '',
      allKeyWords: [],
      keyWordsJson: [],
      lastKeyCode: 0,
      type: "",
      StateChecked: true,
      bookChecked: true,
      filters: {
        name: "",
        clus_Name: "",
        state: "",
        beginTime: [],
        company_Name: "",
        department: "",
        type:"全部",
        colorDoppler:""
      },
    };
  },
  created() {
    this.setDate();
    this.GetOrderList();
    this.GetAllClusItemComb();
  },
  computed: {
    // 显示当前时间
    currentDateStr() {
      let { year, month } = this.current;
      return `${year}-${this.pad(month + 1)}`;
    },
  },
  methods: {
    //设置日期
    setDate() {
      var date = new Date();
      date.setDate(date.getDate()); //获取近3天
      //js计算今天的日期
      this.filters.beginTime[0] = new Date().toLocaleDateString();
      this.filters.beginTime[1] = date.toLocaleDateString();

    },
    // 页数改变事件
    handleSizeChange(size) {
      this.size = size;
      this.tableData = this.paging(size, this.index);
    },
    // 页码改变事件
    handleCurrentChange(current) {
      this.index = current;
      this.tableData = this.paging(this.size, current);
    },
    // 本地分页的方法
    paging(size, current) {
      const tableList = JSON.parse(JSON.stringify(this.tableCopyTableList));
      const tablePush = [];
      tableList.forEach((item, index) => {
        if (size * (current - 1) <= index && index <= size * current - 1) {
          tablePush.push(item);
        }
      });
      return tablePush;
    },
    //获取所有团体订单
    GetOrderList() {
      var that = this;
      if (that.filters.beginTime == null) {
        location.reload();
        return;
      }
      var pData = {
        startDate: that.filters.beginTime[0],
        endDate: that.filters.beginTime[1],
        code:that.filters.colorDoppler
      };
      // var pData = {
      //   lnc_Code: that.lnc_Code,

      // };
      ajax
        .post(apiUrls.GetPersonOrder, pData, { nocrypt: true })
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }

          var data = r.data.returnData;
          if (data.length == 0) {
            that.tableCopyTableList =[];
            that.tableData = this.paging(this.size, this.index);
            return;
          }
          let returnMap = r.data.returnData;
          returnMap = returnMap.sort(function (a, b) {
            return a.id < b.id ? 1 : -1;
          });

          let returnArray = returnMap.map((val) => {
            val.begin_Time = val.begin_Time.substr(0, 10);
            switch (val.type) {
              case "person":
                val.type = "个人体检";
                break;
              case "healthcard":
                val.type = "健康证体检";
                break;
              case "staff":
                val.type = "入职体检";
                break;
              case "nucleicAcid":
                val.type = "核酸体检";
                break;
              case "group":
                val.type = "团体体检";
                break;
              default:
                break;
            }

            switch (val.state) {
              case "F":
                val.state = "已预约";
                break;
              case "C":
                val.state = "已撤销";
                break;
              case "export":
                val.state = "已导出";
                break;
              case "E":
                val.state = "异常订单";
                break;
              case "Refund":
                val.state = "已退费";
                break;
              case "T":
                val.state = "已超时";
                break;
              case "S":
                val.state = "已预约";
                break;
              case "W":
                val.state = "已锁定";
              default:
                break;
            }
            switch (val.repNotif) {
              case "":
                val.repNotif = "未出";
                break;
              case "C":
                val.repNotif = "已出";
                break;
              default:
                val.repNotif = "未出";
                break;
            }
            return val;
          });

          // 初始化数据
          that.tableConstData = returnArray;
          that.tableCopyTableList = returnArray;
          this.GetNewData();
          // that.tableData = this.paging(this.size, this.index);
        })
        .catch((err) => {
          alert("获取订单失败,请稍后重试");
        });
    },
    //撤销订单
    CancelOrder(ids) {
      let idArr = [];
      if (ids) {
        idArr = [ids];
      } else {
        idArr = this.ids;
      }
      if (idArr.length == 0) {
        this.$message.warning("请选择订单");
        return;
      }

      this.$confirm("确定撤销此订单吗, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "error",
      }).then(() => {
        let pData = {
          data: {
            ids: idArr,
          },
        };

        ajax
          .post(apiUrls.CancelPerOrder, pData)
          .then((r) => {
            if (!r.data.success) {
              alert(r.data.returnMsg);
              return;
            }
            this.$message.success("撤销成功");
            this.GetOrderList();
          })
          .catch((err) => {
            alert("获取订单失败,请稍后重试");
          });
      });
    },
    //表格的事件
    handleSelectionChangePeople(rows) {
      this.ids = rows.map((row) => row.id);
    },
    //导出表格
    exportExcel() {
      require.ensure([], () => {
        const { export_json_to_excel } = require("../../common/Export2Excel"); //js存放的位置
        const tHeader = [
          "姓名",
          "身份证",
          "电话号码",
          "单位名称",
          "套餐名称",
          "体检时间",
          "体检时间段",
          "价格",
          "订单状态",
          "订单类型",
        ]; //生成Excel表格的头部标题栏
        const filterVal = [
          "name",
          "idCard",
          "tel",
          "company_Name",
          "clus_Name",
          "begin_Time",
          "sumtime_Name",
          "price",
          "state",
          "type",
        ]; //生成Excel表格的内容栏（根据自己的数据内容属性填写）
        const list = this.tableData; //需要导出Excel的数据
        const data = this.formatJson(filterVal, list);
        export_json_to_excel(tHeader, data, "个检订单表格"); //这里可以定义你的Excel表的默认名称
      });
    },
    //导出套餐
    ExportOrderExcel() {
      // let ids = [this.ClusList.id];
      let ids = this.tableData.map(x => x.id);
      // return;
      if (ids.length == 0) {
        this.$message.warning("未获取到订单");
        return;
      }
      var pData = {
        ids: ids,
      };
      // var pdfData = toolsUtils.encrypt(this.ClusList.id);
      var pdfData = encodeURIComponent(
        toolsUtils.encrypt(JSON.stringify(pData) + "/" + "excelOrder@2023")
      );
      var excelUrl =this.baseData.apiHost + "/home/<USER>" + pdfData;
      setTimeout(function () {
        //原因是window.open会中断正在执行的进程，这样能保证其它代码执行完成再执行这个。
        window.location.href = excelUrl; //改变页面的location  
      }, 300);
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map((v) => filterVal.map((j) => v[j]));
    },
    handlebeginTimeChange(val) {
      const time = ["00:00:00", "23:59:59"];
      for (let i = 0; i < val.length; i++) {
        const date = new Date(val[i]);
        const year = date.getFullYear();
        const month = date.getMonth() + 1;
        const day = date.getDate();

        val[i] = `${year}-${month}-${day} ` + time[i];
      }
      this.filters.beginTime = val;
    },
    //表格筛选
    GetNewData() {
      this.tableCopyTableList = this.tableConstData.filter((item) => {
        // if (this.StateChecked) {
        //   return (!this.filters.name || item.name.includes(this.filters.name)) && (!this.filters.clus_Name || item.clus_Name.includes(this.filters.clus_Name)) && 
        //   (!this.filters.state || item.state == this.filters.state) && (item.state == "已导出" || item.state == "已预约")&&  ((this.filters.type || "全部") == "全部" || item.type == this.filters.type);
        // } else {
        //   return (!this.filters.name || item.name.includes(this.filters.name)) && (!this.filters.clus_Name || item.clus_Name.includes(this.filters.clus_Name)) && 
        //   ((!this.filters.state || "全部") == "全部" || item.state == this.filters.state) &&  ((this.filters.type || "全部") == "全部" || item.type == this.filters.type);
        // }
        if (this.StateChecked) {
          return (!this.filters.name || item.name.includes(this.filters.name)) && (!this.filters.company_Name || (item.company_Name && item.company_Name.includes(this.filters.company_Name))) &&
            (!this.filters.clus_Name || item.clus_Name.includes(this.filters.clus_Name)) && ((this.filters.state || "全部") == "全部" ? (item.state == "已导出" || item.state == "已预约" || item.state == "已锁定") : item.state == this.filters.state)
            && (!this.filters.department || (item.department && item.department.includes(this.filters.department)))&& ((this.filters.type || "全部") == "全部" || item.type == this.filters.type);
        } else {
          // console.log((item.department && item.department.includes(this.filters.department)));
          return (!this.filters.name || item.name.includes(this.filters.name)) && (!this.filters.company_Name || (item.company_Name && item.company_Name.includes(this.filters.company_Name))) &&
            (!this.filters.clus_Name || item.clus_Name.includes(this.filters.clus_Name)) && ((this.filters.state || "全部") == "全部" || item.state == this.filters.state)
            && (!this.filters.department || (item.department && item.department.includes(this.filters.department)))&& ((this.filters.type || "全部") == "全部" || item.type == this.filters.type);
        }
      });
      this.tableData = this.paging(this.size, this.index);
    },
    showViewOrder(row) {
      this.CombData = [];
      if (row.choose_comb_code) {
        let CombData = [];
        let combs = row.choose_comb_code.split(",");
        this.CombAllData.forEach((item) => {
          for (let i = 0; i < item.children.length; i++) {
            for (let z = 0; z < combs.length; z++) {
              if (item.children[i].comb_Code == combs[z]) {
                CombData.push(item.children[i]);
              }
            }
          }
        });
        this.CombData = CombData;
      }
      this.Order.id = row ? row.id : "";
      this.Order.name = row ? row.name : "";
      this.Order.idCard = row ? row.idCard : "";
      this.Order.tel = row ? row.tel : "";
      this.Order.clus_Name = row ? row.clus_Name : "";
      this.Order.begin_Time = row ? row.begin_Time : "";
      this.Order.state = row ? row.state : "";
      this.Order.created_Time = row ? row.created_Time : "";
      this.Order.regno = row ? row.regno : "";
      this.Order.company_Name = row ? row.company_Name : "";
      this.Order.out_trade_no = row ? row.out_trade_no : "";
      this.Order.sumtime_Name = row ? row.sumtime_Name : "";
      this.Order.type = row ? row.type : "";
      this.Order.errorMsg = row ? row.errormsg : "";
      this.drawerOrder = true;
    },
    //获取所有项目信息
    GetAllClusItemComb() {
      var that = this;
      that.loading = false;
      ajax
        .post(apiUrls.GetAddClusItemList, {})
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          // 初始化数据
          that.CombAllData = JSON.parse(r.data.returnData);
        })
        .catch((err) => {
          alert("获取项目失败,请稍后重试");
        });
    },
    showSMSTemplate() {
      this.GetAllSMSTemplateList();
      let idArr = this.ids;
      if (idArr.length == 0) {
        this.$message.warning("请选择客户信息");
        return;
      }
      // let tableData = this.tableData;
      // let userName = [];
      // for (let i = 0; i < tableData.length; i++) {
      //   for (let z = 0; z < idArr.length; z++) {
      //     if (tableData[i].id == idArr[z]) {
      //       userName.push(tableData[i].name);
      //     }
      //   }
      // }
      // this.userName = userName.join("、");
      this.SMSdialogVisible = true;
    },
    SendReminderMessages() {
      let idArr = this.ids;
      if (idArr.length == 0) {
        this.$message.warning("请选择客户信息");
        return;
      }
      let confirmMsg =
        "确定给（" + idArr.length + "）个用户发送短信通知吗, 是否继续?";
      this.$confirm(confirmMsg, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "error",
      }).then(() => {
        let pData = {
          data: {
            ids: idArr,
          },
          sMSTemplate: this.SMSTemplateList,
        };
        ajax
          .post(apiUrls.SendSMSMessagesByOrder, pData)
          .then((r) => {
            if (!r.data.success) {
              this.$message.error(r.data.returnMsg);
              return;
            }
            this.$message.success(r.data.returnMsg);
            this.SMSdialogVisible = false;
          })
          .catch((err) => {
            console.log(err);
            this.$message.error("系统繁忙！请稍后再试");
          });
      });
    },
    //短信模板
    GetAllSMSTemplateList() {
      var that = this;
      that.loading = false;
      var pData = {
        data: {
          code: "1005",
        },
      };
      ajax
        .post(apiUrls.GetAllSMSTemplateByCode, pData)
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          // 初始化数据
          let tableConstData = r.data.returnData.st;
          that.SMSNounList = r.data.returnData.sn;
          this.SMSTemplateTask(tableConstData);
        })
        .catch((err) => {
          alert("获取短信模板失败,请稍后重试");
        });
    },
    SMSTemplateTask(SMSTemplate) {
      console.log(SMSTemplate);
      this.SMSTemplateList = SMSTemplate;
      this.SMSPreview = "";
      this.SMSParameter = SMSTemplate.parameter;
      var nounId = SMSTemplate.nounId.split(",");
      var SMSnounIds = [];
      for (let i = 0; i < nounId.length; i++) {
        for (let z = 0; z < this.SMSNounList.length; z++) {
          if (nounId[i] == this.SMSNounList[z].code) {
            SMSnounIds.push(this.SMSNounList[z]);
          }
        }
      }
      this.SMSnounIds = SMSnounIds;
      this.SMSPreview = this.formatString(
        SMSTemplate.preview,
        SMSTemplate.parameter
      );
      this.SMSContent = this.formatContent(
        this.SMSTemplateList.preview,
        this.SMSTemplateList.parameter
      );
    },
    formatString(formatted, parameter) {
      if (parameter) {
        var parameterList = parameter.split(",");
        var args = [];
        for (let i = 0; i < parameterList.length; i++) {
          for (let z = 0; z < this.SMSNounList.length; z++) {
            if (parameterList[i] == this.SMSNounList[z].code) {
              args.push(
                "<span id=" +
                this.SMSNounList[z].code +
                " style='color: blue;'>" +
                this.SMSNounList[z].smSdefault +
                "</span>"
              );
            }
          }
        }
        for (let i = 0; i < args.length; i++) {
          let regexp = new RegExp("\\{" + i + "\\}", "gi");
          formatted = formatted.replace(regexp, args[i]);
        }
      }
      return formatted;
    },
    formatContent(formatted, parameter) {
      if (parameter) {
        var parameterList = parameter.split(",");
        var args = [];
        // var spanElement = [];
        for (let i = 0; i < parameterList.length; i++) {
          for (let z = 0; z < this.SMSNounList.length; z++) {
            if (parameterList[i] == this.SMSNounList[z].code) {
              args.push(
                "【" +
                this.SMSNounList[z].name +
                "】"
              );
              // spanElement.push(this.SMSNounList[z].code +i);
            }
          }
        }
        for (let i = 0; i < args.length; i++) {
          let regexp = new RegExp("\\{" + i + "\\}", "gi");
          formatted = formatted.replace(regexp, args[i]);
        }
      }


      // this.spanElementList=spanElement.join(",");
      return formatted;
    },
    handleTextareaClick() {
      var obj = document.querySelector('textarea');
      this.cursorIndex = this.getFocus(obj);
      this.dealFocusExtend(obj, this.cursorIndex);
    },
    handleKeyup(e) {
      //每次在文本域中输入的时候都要获取其光标位置，以便于其他操作
      var obj = document.querySelector('textarea');
      this.cursorIndex = this.getFocus(obj);

      //由于我们是禁止输入中文中括号的，而中文中括号输入左右情况不同，需要分别处理
      // console.log(e.keyCode);
      if (e.keyCode == 219) {
        e.preventDefault();
        //这里获取到光标左侧的内容
        var leftChar = obj.value.slice(this.cursorIndex - 1, this.cursorIndex);

        //只有输入结束的是右中括号，而且它的前一个字符是左中括号才把它删除，防止把关键字删除掉
        if (/\】/g.test(leftChar) && obj.value.charAt(this.cursorIndex - 2) === '【') {
          obj.value = obj.value.slice(0, this.cursorIndex - 2) + obj.value.slice(this.cursorIndex, obj.value.length);
        }

      } else if (e.keyCode == 221) {
        e.preventDefault();
        //右中括号就好办多了，因为它不会自动带出左中括号
        var leftChar = obj.value.slice(this.cursorIndex - 1, this.cursorIndex);
        if (/\】/g.test(leftChar)) {
          obj.value = obj.value.slice(0, this.cursorIndex - 1) + obj.value.slice(this.cursorIndex, obj.value.length);
        }
      }
      //防止上下左右键移动光标进入关键字中
      if ((e.keyCode == 37 || e.keyCode == 39 || e.keyCode == 38 || e.keyCode == 40) && this.lastKeyCode !== 219) {
        dealFocusMove(obj, this.cursorIndex);
      } else if (e.keyCode == 8) {
        //backspace删除的时候删除整个关键字
        // console.log(obj, this.cursorIndex, this.allKeyWords);
        this.dealFocusL(obj, this.cursorIndex, this.allKeyWords);
      } else if (e.keyCode == 46) {
        //delete删除的时候也是删除整个关键字
        this.dealFocusR(obj, this.cursorIndex, this.allKeyWords)
      }
      if (e.keyCode !== 37 && e.keyCode !== 39) {
        //这里防止手动按得左右键影响左中括号判断
        this.lastKeyCode = e.keyCode;
      }
      this.updateSMSPreview()

    },
    handleKeydown(e) {
      if (e.keyCode == 221 || e.keyCode == 219) {
        e.preventDefault();
      }
      if ((e.keyCode == 37 || e.keyCode == 39) && this.this.lastKeyCode === 219) {
        e.preventDefault();
      }
    },

    // initializeKeyWordsJson() {
    //   const newData = this.data1.concat(this.data2).concat(this.data3);
    //   for (let i = 0; i < newData.length; i++) {
    //     if (this.keyWordsJson[newData[i].name] !== null) {
    //       this.keyWordsJson[newData[i].name] = newData[i].id;
    //     }
    //   }
    // },
    // getFocus() {
    //   return this.$refs.textarea.selectionStart;
    // },
    //处理删除关键字
    dealFocusL(obj, index, allKeyWords) {
      var text = obj.value.slice(0, index);
      var resL, resR, i = 0, j = 0;
      var lastIndex = 0;
      var regL = /\【/g;
      var regR = /\】/g;
      while (resL = regL.exec(text)) {
        i++;
        //获取左中括号位置
        lastIndex = regL.lastIndex;
      }
      while (resR = regR.exec(text)) {
        j++;
      }
      if (i != j) {
        var textAll = obj.value;
        obj.value = textAll.substring(0, lastIndex - 1) + textAll.substring(index, textAll.length);
        this.SMSContent = obj.value;
        // console.log(obj.value);
        // allKeyWords.splice(i - 1, 1);
        obj.setSelectionRange(lastIndex - 1, lastIndex - 1);
        // 移动光标后触发点击事件，使光标闪烁，保持焦点
        obj.click();
      }

    },
    //delete关键字
    dealFocusR(obj, index, allKeyWords) {
      var text = obj.value.slice(index, obj.value.length);
      text = text.split('').reverse().join('');
      var resL, resR, i = 0, j = 0;
      var lastIndex = 0;
      var regL = /\【/g;
      var regR = /\】/g;
      while (resL = regL.exec(text)) {
        i++;
      }
      while (resR = regR.exec(text)) {
        j++;
        lastIndex = regR.lastIndex;
      }
      if (i != j) {
        //获取右中括号位置
        var textAll = obj.value;
        lastIndex = index + text.length - lastIndex + 1;
        allKeyWords.splice(j - 1, 1);
        obj.value = textAll.substring(0, index) + textAll.substring(lastIndex, textAll.length);
        this.SMSContent = obj.value;
        obj.setSelectionRange(index, index);
        // 移动光标后触发点击事件，使光标闪烁，保持焦点
        obj.click();
      }
    },
    //处理光标上下左右移动
    dealFocusMove(obj, index) {
      var text = obj.value.slice(0, index);
      var resL, resR, i = 0, j = 0;
      var lastIndex = 0;
      var _lastIndex = 0;
      var regL = /\【/g;
      var regR = /\】/g;
      while (resL = regL.exec(text)) {
        i++;
        lastIndex = regL.lastIndex;
      }
      while (resR = regR.exec(text)) {
        j++;
      }
      if (i != j) {
        if (index == lastIndex) {
          var rightText = regR.exec(obj.value.slice(index, obj.value.length));
          _lastIndex = rightText['index'];
          index = _lastIndex + index + 1;
        } else {
          index = lastIndex - 1;
        }
        obj.selectionStart = index;
        obj.selectionEnd = index;
      }
    },
    //处理鼠标定位光标
    dealFocusExtend(obj, index) {
      var text = obj.value.slice(index, obj.value.length);
      // var text = obj.value;
      // console.log(text);
      var resL, resR, i = 0, j = 0;
      var lastIndex = 0;
      var firstRightBracketIndex = -1; // 记录第一个右中括号的索引
      var regL = /\【/g;
      var regR = /\】/g;
      while (resL = regL.exec(text)) {
        i++;

      }
      while (resR = regR.exec(text)) {
        j++;
        if (firstRightBracketIndex === -1) {
          firstRightBracketIndex = resR.index + index + 1; // 记录第一个右中括号的索引
        }
        lastIndex = regR.index;
      }
      // console.log(index);
      if (i != j) {
        // var text = obj.value;
        index = obj.value.length;
        obj.selectionStart = firstRightBracketIndex;
        obj.selectionEnd = firstRightBracketIndex;
      }
    },
    //获取光标位置
    getFocus(elem) {
      var index = 0;
      if (document.selection) { // IE Support 
        elem.focus();
        var Sel = document.selection.createRange();
        if (elem.nodeName === 'TEXTAREA') { //textarea 
          var Sel2 = Sel.duplicate();
          Sel2.moveToElementText(elem);
          var index = -1;
          while (Sel2.inRange(Sel)) {
            Sel2.moveStart('character');
            index++;
          };
        } else if (elem.nodeName === 'INPUT') { // input 
          Sel.moveStart('character', -elem.value.length);
          index = Sel.text.length;
        }
      } else if (elem.selectionStart || elem.selectionStart == '0') { // Firefox support
        index = elem.selectionStart;
      }
      return (index);
    },
    selectDetail(e) {
      var obj = document.querySelector('textarea');
      this.cursorIndex = this.getFocus(obj);
      //首先判断是否有光标，这样我们的光标位置是不存在的
      if (this.cursorIndex !== null) {
        //这里判断是否是我们要点击的是不是关键字
        if (e.target.tagName !== "TEXTAREA" && e.target.getAttribute('isFocus')) {
          //要添加东西当然要先放入光标了，这里会记住之前的光标位置，所以直接focus即可
          obj.focus();
          this.cursorIndex = this.getFocus(obj);
          var text = obj.value;
          //文本中关键字以中括号包裹的形式显示
          var textNode = text.substring(0, this.cursorIndex) + '【' + e.target.innerHTML + '】' + text.substring(this.cursorIndex, text.length);
          this.allKeyWords.push(e.target.innerHTML);
          obj.value = textNode;
          //添加完之后我们要刷新光标位置
          this.SMSContent = obj.value;
          this.updateSMSPreview();
          this.cursorIndex = this.cursorIndex + e.target.innerHTML.length + 2;
          obj.selectionStart = this.cursorIndex;
          obj.selectionEnd = this.cursorIndex;

        } else if (e.target.tagName == "TEXTAREA" && e.target.getAttribute('isFocus')) {
          //点击文本区域操作
          this.cursorIndex = getFocus(obj);
        } else {
          //点击其他地方要将光标位置置空，防止点击关键字添加
          this.cursorIndex = null;
          console.log(1);
        }
      }
    },
    updateSMSPreview() {
      var obj = document.querySelector('textarea');
      // var templatetypename = document.querySelector('[data-type="templateName"]').value||"";
      //模板原始内容
      var templatename = obj.value || "";
      var regex = /\【([^\【\】]+)\】/g;
      let placeholders = [];
      this.SMSTemplateList.preview = templatename.replace(regex, (match) => {
        placeholders.push(`{${placeholders.length}}`);
        return placeholders[placeholders.length - 1];
      });
      this.SMSTemplateList.parameter = this.getParameter(templatename);
      // console.log(placeholders);
      // console.log(this.SMSTemplateList.parameter);
      console.log(this.SMSTemplateList.preview,
        this.SMSTemplateList.parameter);
      this.SMSPreview = this.formatPreviewString(
        this.SMSTemplateList.preview,
        this.SMSTemplateList.parameter
      );
      // console.log(this.SMSPreview);
    },
    formatPreviewString(formatted, parameter) {
      // console.log(formatted, parameter);
      if (parameter) {
        var parameterList = parameter.split(",");
        var args = [];
        for (let i = 0; i < parameterList.length; i++) {
          for (let z = 0; z < this.SMSNounList.length; z++) {
            if (parameterList[i] == this.SMSNounList[z].code) {
              args.push(
                "<span id=" +
                this.SMSNounList[z].code +
                " style='color: blue;'>" +
                this.SMSNounList[z].smSdefault +
                "</span>"
              );
            }
          }
        }
        for (let i = 0; i < args.length; i++) {
          let regexp = new RegExp("\\{" + i + "\\}", "gi");
          formatted = formatted.replace(regexp, args[i]);
        }
      }
      return formatted;
    },
    getParameter(message) {
      // console.log(message);
      // 使用正则表达式提取<span>标签内容
      var regEx = /\【([^\【\】]+)\】/g;
      var spanContents = [];
      var match;

      while ((match = regEx.exec(message))) {
        // 获取匹配到的<span>标签内容
        var spanContent = match[1];
        spanContents.push(spanContent);
      }
      // console.log(spanContents);
      // console.log(spanContents,this.SMSnounIds);
      let nounIds = [];
      for (var i = 0; i < spanContents.length; i++) {
        for (let j = 0; j < this.SMSnounIds.length; j++) {
          // console.log(this.allKeyWords[i]);
          if (spanContents[i] == this.SMSnounIds[j].name) {
            nounIds.push(this.SMSnounIds[j].code);
          }
        }
        // keywords.push(this.keyWordsJson[this.allKeyWords[i]]);
      }
      // console.log(nounIds);
      return nounIds.join(",");
    },
    SyncOrderByOrderIds(ids) {
      // let idArr = this.ids;
      let idArr = [];
      if (ids) {
        idArr = [ids];
      } else {
        idArr = this.ids;
      }
      if (idArr.length == 0) {
        this.$message.warning("请选择客户信息");
        return;
      }
      let confirmMsg =
        "导入内院后，用户将无法取消订单, 是否继续?";
      this.$confirm(confirmMsg, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "error",
      }).then(() => {
        let pData = {
          data: {
            ids: idArr,
          }
        };
        ajax
          .post(apiUrls.SyncOrderByOrderIds, pData)
          .then((r) => {
            if (!r.data.success) {
              this.$message.error(r.data.returnMsg);
              return;
            }
            this.GetOrderList();
            this.$message.success("同步到内院成功!");
            this.SMSdialogVisible = false;
          })
          .catch((err) => {
            console.log(err);
            this.$message.error("系统繁忙！请稍后再试");
          });
      });
    },
    //改期
    updateBeginTime(item) {
      this.orderId = item.id;
      if (!this.orderId) {
        this.$message.warning("请选择客户信息");
        return;
      }
      this.lnc_Code = "0451";
      this.stateShow = false;
      this.ClassKey = "";
      this.sumtime_Name = "";
      this.CurrentClus = [];
      this.TeamSpans = "";
      // let tableData = this.tableData;
      // let teamName = [];
      // for (let i = 0; i < tableData.length; i++) {
      //   for (let z = 0; z < this.ids.length; z++) {
      //     if (tableData[i].id == this.ids[z]) {
      //       teamName.push(tableData[i].name);
      //     }
      //   }
      // }
      switch (item.type) {
        case "个人体检":
          this.type = "person";
          break;
        case "健康证体检":
          this.type = "healthcard";
          break;
        case "入职体检":
          this.type = "staff";
          break;
        case "核酸体检":
          this.type = "nucleicAcid";
          break;
        case "团体体检":
          this.type = "group";
          break;
        default:
          break;
      }
      this.init();
      this.teamName = item.name;
      this.bookChecked = true;
      this.updateTimedialogVisible = true;
    },
    //检查输入的参数
    checkupdateTimeConfirm() {
      if (this.orderId.length <= 0) {
        this.$message.warning("客户名单为空");
        return false;
      }
      if (!this.ClassKey) {
        this.$message.warning("请选择体检时间");
        return false;
      }
      if (!this.sumtime_Code || !this.sumtime_Name) {
        this.$message.warning("请选择体检时间段");
        return false;
      }
      return true;
    },
    updateTimeConfirm() {
      this.overallLoading = this.$loading({
        lock: true,
        text: "正在修改中",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      if (!this.checkupdateTimeConfirm()) {
        this.overallLoading.close();
        return;
      }
      let pData = {
        data: {
          id: this.orderId,
          code: this.bookChecked ? "1" : "0",
        },
        OrdData: {
          sumtime_Code: this.sumtime_Code,
          sumtime_Name: this.sumtime_Name,
          begin_Time: this.ClassKey,
        },
      };
      ajax
        .post(apiUrls.updateBeginTimeConfirm, pData)
        .then((r) => {
          this.overallLoading.close();
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          this.$message.success(r.data.returnMsg);
          this.updateTimedialogVisible = false; //成功后关闭对话框
          // this.GetTeamList(); //重新加载
          this.GetOrderList();
        })
        .catch((err) => {
          console.log(err);
          this.$message.error("系统繁忙！请稍后再试");
        });
    },
    //时间段选择判断
    timeBtn(items, index) {
      var that = this;
      if (items.team_Surplus <= 0) {
        Toast("该时段已无号源！请选择其他时段");
        return;
      }
      that.TeamSpans = index;
      that.sumtime_Name = items.sumtime_Name;
      that.sumtime_Code = items.sumtime_Code;
    },
    init() {
      // this.overallLoading = this.$loading({
      //   lock: true,
      //   text: "正在加载中",
      //   spinner: "el-icon-loading",
      //   background: "rgba(0, 0, 0, 0.7)",
      // });
      // 初始化当前时间
      this.setCurrent();
      this.calendarCreator();
      // this.switchHaoyuanClass();
    },
    // 判断当前月有多少天
    getDaysByMonth: function (year, month) {
      return new Date(year, month + 1, 0).getDate();
    },
    getFirstDayByMonths: function (year, month) {
      return new Date(year, month, 1).getDay();
    },
    getLastDayByMonth: function (year, month) {
      return new Date(year, month + 1, 0).getDay();
    },
    // 对小于 10 的数字，前面补 0
    pad: function (str) {
      return str < 10 ? `0${str}` : str;
    },
    // 点击上一月
    prevMonth: function () {
      this.current.month--;

      // 因为 month的变化 会超出 0-11 的范围， 所以需要重新计算
      this.correctCurrent();
      // 生成新日期
      this.calendarCreator();
    },
    // 点击下一月
    nextMonth: function () {
      this.current.month++;
      // 因为 month的变化 会超出 0-11 的范围， 所以需要重新计算
      this.correctCurrent();
      // 生成新日期
      this.calendarCreator();
    },
    // 格式化时间，与主逻辑无关
    stringify: function (year, month, date) {
      let str = [year, this.pad(month + 1), this.pad(date)].join("-");
      return str;
    },
    // 设置或初始化 current
    setCurrent: function (d = new Date()) {
      let year = d.getFullYear();
      let month = d.getMonth();
      let date = d.getDate();
      this.current = {
        year,
        month,
        date,
      };
    },
    // 修正 current
    correctCurrent: function () {
      let { year, month, date } = this.current;

      let maxDate = this.getDaysByMonth(year, month);
      // 预防其他月跳转到2月，2月最多只有29天，没有30-31
      date = Math.min(maxDate, date);

      let instance = new Date(year, month, date);
      this.setCurrent(instance);
    },
    // 生成日期
    calendarCreator: function () {
      // 一天有多少毫秒
      const oneDayMS = 24 * 60 * 60 * 1000;

      let list = [];
      let { year, month } = this.current;

      // 当前月份第一天是星期几, 0-6
      let firstDay = this.getFirstDayByMonths(year, month);
      // 填充多少天 firstDay-1则为周一开始，
      let prefixDaysLen = firstDay === 0 ? 0 : firstDay;
      // 毫秒数
      let begin = new Date(year, month, 1).getTime() - oneDayMS * prefixDaysLen;

      // 当前月份最后一天是星期几, 0-6
      let lastDay = this.getLastDayByMonth(year, month);
      // 填充多少天， 和星期的排放顺序有关
      let suffixDaysLen = lastDay === 0 ? 0 : 6 - lastDay;
      // 毫秒数
      let end =
        new Date(year, month + 1, 0).getTime() + oneDayMS * suffixDaysLen;

      while (begin <= end) {
        // 享元模式，避免重复 new Date
        this.shareDate.setTime(begin);
        let year = this.shareDate.getFullYear();
        let curMonth = this.shareDate.getMonth();
        let date = this.shareDate.getDate();
        let week = this.shareDate.getDay(); // 当前周几
        list.push({
          year: year,
          month: curMonth,
          date: date,
          Thing: "待开",
          week: week,
          ThingName: "",
          disable: curMonth !== month,
          value: this.stringify(year, curMonth, date),
        });

        begin += oneDayMS;
      }
      this.calendarList = list;
      // this.judgeHaoyuan();
      if (this.type == "staff") {
        this.judgeHaoyuanPer();
      } else {
        this.judgeHaoyuan();
      }
    },

    // 号源显示
    judgeHaoyuan: function () {
      var that = this;

      if (!this.lnc_Code) {
        // console.log(this.lnc_Code);
        // this.$message.warning("lnc_Code为NULL!请稍后再试");
        // return;
        this.lnc_Code = "0451";
      }
      // 当前时间
      var nowDate = this.stringify(
        new Date().getFullYear(),
        new Date().getMonth(),
        new Date().getDate()
      );

      var pData = {
        lnccode: this.lnc_Code,
        start: "0",
        end: "30",
      };
      ajax
        .post(apiUrls.GetTeamSumList, pData, { nocrypt: true })
        .then((r) => {
          // this.overallLoading.close();
          var TeamList = r.data.returnData;
          for (var i = 0; i < that.calendarList.length; i++) {
            that.prevMonthIconShow();
            if (nowDate > that.calendarList[i].value) {
              that.calendarList[i].Thing = "";
              continue;
            }
            for (var j = 0; j < TeamList.length; j++) {
              if (that.calendarList[i].value == TeamList[j].team_Date) {
                if (TeamList[j].team_Flag == "T") {
                  that.calendarList[i].Thing = "休假";
                  that.calendarList[i].ThingName = "休假";
                  break;
                }
                var team_Surplus = TeamList[j].team_Surplus;
                if (TeamList[j].team_Sum <= 0) {
                  that.calendarList[i].Thing = "待开";
                  that.calendarList[i].ThingName = "待开";
                  break;
                }
                if (team_Surplus > 10) {
                  that.calendarList[i].Thing = "余" + team_Surplus + "人";
                  that.calendarList[i].ThingName = "充足";
                } else if (team_Surplus == 0) {
                  that.calendarList[i].Thing = "约满";
                  that.calendarList[i].ThingName = "约满";
                } else if (team_Surplus <= 10) {
                  that.calendarList[i].Thing = "余" + team_Surplus + "人";
                  that.calendarList[i].ThingName = "紧张";
                }
              }
            }
          }
          // console.log(that.calendarList);
        })
        .catch((e) => {
          alert("服务异常！请稍等");
          return;
        });
    },
    judgeHaoyuanPer: function () {
      var that = this;
      var nowDate = this.stringify(
        new Date().getFullYear(),
        new Date().getMonth(),
        new Date().getDate()
      );
      var pData = {
        type: that.type,
        start: "0",
        end: "30",
      };
      ajax
        .post(apiUrls.GetPersonSumList, pData, { nocrypt: true })
        .then((r) => {
          var personList = r.data.returnData;
          for (var i = 0; i < that.calendarList.length; i++) {
            that.prevMonthIconShow();
            if (nowDate > that.calendarList[i].value) {
              that.calendarList[i].Thing = "";
              continue;
            }
            for (var j = 0; j < personList.length; j++) {
              if (that.calendarList[i].value == personList[j].person_Date) {
                if (personList[j].person_Flag == "T") {
                  that.calendarList[i].Thing = "休假";
                  that.calendarList[i].ThingName = "休假";
                  break;
                }
                var person_Surplus = personList[j].person_Surplus;
                if (personList[j].person_Sum <= 0) {
                  that.calendarList[i].Thing = "待开";
                  that.calendarList[i].ThingName = "待开";
                  break;
                }
                if (person_Surplus > 10) {
                  that.calendarList[i].Thing = "余" + person_Surplus + "人";
                  that.calendarList[i].ThingName = "充足";
                } else if (person_Surplus == 0) {
                  that.calendarList[i].Thing = "约满";
                  that.calendarList[i].ThingName = "约满";
                } else if (person_Surplus <= 10) {
                  that.calendarList[i].Thing = "余" + person_Surplus + "人";
                  that.calendarList[i].ThingName = "紧张";
                }
              }
            }
          }
        })
        .catch((e) => {
          alert("服务异常！请稍等");
          return;
        });
    },
    // 根据日期显示添加类名
    switchHaoyuanClass: function (value) {
      switch (value) {
        case "紧张":
          return "haoyuan-green";
          break;
        case "约满":
          return "haoyuan-red";
          break;
        case "休假":
          return "haoyuan-red";
          break;
        case "充足":
          return "haoyuan-adequate";
          break;
        case "":
          return "";
          break;
      }
    },

    // 号源匹配触发点击
    matchDate: function (date) {
      if (date == "充足" || date == "紧张") {
        return true;
      } else if (date === "约满" || "待开" || "") {
        return false;
      }
    },

    // 当前年月之前不能跳转回去
    prevMonthIconShow: function () {
      if (this.current.year == new Date().getFullYear()) {
        if (this.current.month > new Date().getMonth()) {
          this.pre = true;
          this.pre_ = false;
        } else {
          this.pre = false;
          this.pre_ = true;
        }
      } else if (this.current.year > new Date().getFullYear()) {
        this.pre = true;
        this.pre_ = false;
      }
    },
    // 日期点击事件
    dayClick: function (date, key, index) {

      if (key == "充足" || key == "紧张") {
        this.years = this.current.year;
        this.months = this.current.month + 1;
        this.ClassKey =
          this.years +
          "-" +
          (this.months < 10 ? "0" + this.months : this.months) +
          "-" +
          (date < 10 ? "0" + date : date);
        this.week = this.calendarList[index].week;
        let apiUrl = "";
        var pData = {};
        console.log(this.type);
        if (this.type == "staff") {
          apiUrl = apiUrls.GetSumTimeList;
          pData = {
            date_Time: this.ClassKey,
            type: this.type,
            combs: this.combs,
          };
        } else {
          apiUrl = apiUrls.GetTeamSumTimeList;
          pData = {
            date_Time: this.ClassKey,
            lnccode: this.lnc_Code,
          };
        }
        // var pData = {
        //   date_Time: this.ClassKey,
        //   type: this.type,
        //   combs: this.combs,
        // };
        console.log(apiUrls);
        //不需要号源时段的项目注释掉
        this.TimeSpan = "";
        var that = this;
        ajax
          .post(apiUrl, pData, { nocrypt: true })
          .then((r) => {
            if (r.data.success) {
              // that.state = true;
              that.stateShow = true;
              var List = r.data.returnData;
              let sumList = [];
              for (let i = 0; i < List.length; i++) {
                sumList.push({
                  sumtime_BegTime: List[i].sumtime_BegTime,
                  sumtime_Code: List[i].sumtime_Code,
                  sumtime_EndTime: List[i].sumtime_EndTime,
                  sumtime_Name: List[i].sumtime_Name,
                  team_Already: List[i].person_Already,
                  team_Sum: List[i].person_Sum,
                  team_Surplus: List[i].person_Surplus
                });

              }
              that.sumtimeList = sumList;
              console.log(that.sumtimeList);
            }
          })
          .catch((e) => {
            Toast("系统异常！请联系管理员");
            return;
          });
        //
        return true;
      } else {
        this.ClassKey = false;
        return false;
      }
    },
    daybookCheckedClick: function (date, key, index) {
      // 判断能点击的日期是否为号源充足和紧张,休假，待开，""都不可点击
      if (key != "待开") {
        this.years = this.current.year;
        this.months = this.current.month + 1;
        this.ClassKey = this.years + "-" + this.months + "-" + date;
        this.week = this.calendarList[index].week;
        var pData = {
          date_Time: this.ClassKey,
          lnccode: this.lnc_Code,
        };
        //不需要号源时段的项目注释掉
        this.TeamSpans = "";
        var that = this;
        ajax
          .post(apiUrls.GetTeamSumTimeList, pData, { nocrypt: true })
          .then((r) => {
            if (r.data.success) {
              that.stateShow = true;
              this.TeamSpans = "";
              var List = r.data.returnData;
              that.sumtimeList = List;
            }
          })
          .catch((e) => {
            alert("系统异常！请联系管理员");
            return;
          });
        //
        return true;
      } else {
        this.ClassKey = false;
        this.TeamSpans = "";
        this.sumtime_Name = "";
        this.stateShow = false;
        return false;
      }
    },
    changeBookChecked() {
      this.ClassKey = "";
      this.sumtime_Name = "";
      // this.CurrentClus = [];
      // this.handleCurrentChangeClus();
      // this.teamName = "";
      this.TeamSpans = "";
      this.stateShow = false;
    },
    timeBookCheckedBtn(items, index) {
      var that = this;
      // if (items.team_Surplus <= 0) {
      //   alert("该时段已无号源！请选择其他时段");
      //   return;
      // }
      that.TeamSpans = index;
      that.sumtime_Name = items.sumtime_Name;
      that.sumtime_Code = items.sumtime_Code;
    },
    //全选
    toggleSelection(rows) {
      if (rows) {
        // rows.forEach(row => {
        //   this.$refs.tableData.toggleRowSelection(row);
        // });
        this.$refs.tableData.toggleAllSelection() //全选
      } else {
        this.$refs.tableData.clearSelection();
      }
    },
  },
};
</script>
  
<style lang="scss" >
.ordDiv {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 96%;
  margin: 20px auto;

  .lncTop {
    width: 100%;
    margin-top: 10px;
    display: flex;

    // justify-content: space-between;
    .getNew {
      width: 180px;

      .el-input__inner {
        padding: 0 5px !important;
        text-align: center;
        width: 180px !important;
      }
    }

    .getSele {
      width: 100px;
      margin-left: 10px;

      .el-input__inner {
        padding: 0 10px !important;
        width: 100px !important;
      }
    }

    .getPicker {
      width: 300px;
      margin-left: 10px;

      .el-range-editor.el-input__inner {
        padding: 3px 6px !important;
        width: 300px;
        display: flex;
        justify-content: space-between;
      }

      .el-date-editor .el-range-separator {
        padding: 0 !important;

        margin-left: 5px;
        margin-right: 5px;
      }

      .el-date-editor .el-range-input {
        width: 50% !important;
      }
    }
  }

  .lncMid {
    margin-top: 20px;
    width: 100%;

    .pageNation {
      margin-top: 10px;

      .el-pagination {
        display: flex;
        justify-content: flex-end;
      }
    }
  }
}

#lncMid {
  width: 100%;
  margin-top: 0px !important;
  font-size: 16px;
}

.pageNation {
  margin-top: 20px;
  font-family: "Times New Roman", Times, serif;
}

.pageNation .el-pagination {
  display: flex;
  justify-content: flex-end;
}

.OrderDe span:nth-child(2) {
  color: blue;
}

.OrderDe .OrderDein {
  margin-top: 8px;
  margin-left: 20px;
  font-family: "Avenir", Helvetica, Arial, sans-serif;
  display: flex;
}

.OrderDe span:first-child {  
  /* 你的样式 */  
  width:80px !important;
}

.haoyuan-red {
  color: red;
}

.haoyuan-bg {
  background-color: baga(250, 250, 250, 0.3);
  color: #ccc;
  cursor: default !important;
}

.TjBox .company {
  height: 40px;
}

#updateTimeDialog {
  width: 100%;
  display: flex;
  justify-content: center;
}

#updateTimeDialog .selectTimeDiv {
  width: 100%;
}


.DYConfirmDiv {
  font-family: "Lucida Sans", "Lucida Sans Regular", "Lucida Grande",
    "Lucida Sans Unicode", Geneva, Verdana, sans-serif;
  margin-top: 13px;
  margin-left: 13px;
  font-size: 18px;
}

.DYConfirmSpan {
  margin-left: 8px;
  color: rgb(224, 103, 22);
}

.DYConfirmDiv div {
  margin-top: 3px;
  font-size: 16px;
}

#smsSMSdialogVisible .el-dialog__body {
  overflow: auto;
}
</style>
  