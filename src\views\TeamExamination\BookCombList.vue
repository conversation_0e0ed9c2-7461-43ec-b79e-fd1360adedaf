<template>
  <div style="height: 100%">
    <div class="TeamPOP_page" v-show="!editFlag">
      <div class="TjBox">
        <!-- 单位日期 -->
        <div class="company" id="company">
          <!--单位选择 -->
          <div class="companySwitch">
            <el-button @click="drawer = true" type="primary" size="medium">
              切换单位
            </el-button>
            <span
              >单位：<strong class="companylnc_Name">{{
                lnc_Name
              }}</strong></span
            >
            <span class="contactPersonSpan" @click="contactPersonClick"
              >(单位联系人)</span
            >
            <input type="hidden" v-model="lnc_Code" />
            <!--抽屉 -->
            <el-drawer
              title="单位号源"
              :visible.sync="drawer"
              :with-header="false"
            >
              <div class="drawerAll">
                <div class="drawerTop">单位列表</div>
                <div class="drawerSearch">
                  <!-- <div class="searchTwo">体检单位预约列表</div> -->
                  <div class="searchTwo">
                    <el-input
                      placeholder="单位编码/单位名称"
                      prefix-icon="el-icon-search"
                      v-model="drawerIpnut"
                      size="mini"
                    ></el-input>
                    <el-button type="primary" plain size="mini" @click="querys"
                      >查询</el-button
                    >
                  </div>
                </div>
                <div class="drawerList">
                  <div class="drawerTr1">
                    <div class="drawerTd1">
                      <span>单位编码</span>
                    </div>
                    <div class="drawerTd2">
                      <span>单位名称</span>
                    </div>
                  </div>
                  <div
                    v-for="(item, index) in drawerData"
                    :key="index"
                    class="drawerTr2"
                    :class="{ hoverIndex: index == hoverIndex }"
                    @mouseover="hoverIndex = index"
                    @mouseout="hoverIndex = -1"
                    @click="drawerBtn(index)"
                  >
                    <div class="drawerTd3">
                      <!-- <span>{{ item.drawerCode }}</span> -->
                      <span>{{ item.lnc_Code }}</span>
                    </div>
                    <div class="drawerTd4">
                      <!-- <span>{{ item.drawerName }}</span>-->
                      <span>{{ item.lnc_Name }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </el-drawer>
          </div>
          <div style="margin-right: 65px">
            <el-button @click="winOpenGroupOrder" size="medium">
              单位订单
            </el-button>
          </div>
        </div>
      </div>
      <div class="content_wrap">
        <!-- 套餐列表 -->
        <el-tabs
          class="combo_wrap"
          stretch
          v-model="comboActive"
          @tab-click="tabClick"
        >
          <el-tab-pane label="单位套餐" name="unitCombo">
            <!-- <div class="combo_search">
              <div class="combo_setting">
                <i
                  class="el-icon-setting"
                  title="新建套餐"
                  @click="AllClusdialogVisible = true"
                ></i>
              </div>
            </div> -->
            <div class="combo_search">
              <el-input
                v-model="searchVal"
                @input="searchCombo"
                style="flex: 1; flex-shrink: 0"
                size="small"
                placeholder="请输入内容"
              ></el-input>
              <!-- <i class="el-icon-plus" title="新建套餐" @click="creatClus"></i> -->
              <!-- <i class="el-icon-folder-add" title="导入套餐" @click="creatClus" v-if="!enabled"></i> -->
              <!-- <i
                class="el-icon-setting"
                title="新建套餐"
                @click="showAllClusdialogVisible()"
              ></i> -->
              <span @click="sortClick">{{
                enabled ? "取消排序" : "排序"
              }}</span>
              <!-- <span @click="showsortClickDialogVisible('O')">排序</span> -->
            </div>
            <ul class="combo_list">
              <Vuedraggable
                :move="checkMove"
                animation="500"
                handle=".sort_p"
                :list="unitComboList"
                :disabled="!enabled"
                class="list-group"
                ghost-class="ghost"
                @start="sortStart"
                @end="sortEnd"
              >
                <li
                  @click="clusClick(item)"
                  :class="{ active: checkClus.id == item.id }"
                  v-for="(item, idx) in unitComboList"
                  :key="idx"
                >
                  <div style="display: flex; align-items: center">
                    <div class="sexImg" v-if="item.clus_sex == '男'">
                      <img
                        style="width: 28px; height: 28px"
                        src="../../assets/man.png"
                        alt
                      />
                    </div>
                    <div class="sexImg" v-else-if="item.clus_sex == '女'">
                      <img
                        style="width: 28px; height: 28px"
                        src="../../assets/woman.png"
                        alt
                      />
                    </div>
                    <div class="sexImg" v-else-if="item.clus_sex == '不限'">
                      <img
                        style="width: 28px; height: 28px"
                        src="../../assets/detailsSex01.png"
                        alt
                      />
                    </div>
                    <div class="sexImg" v-else>
                      <img
                        style="width: 28px; height: 28px"
                        src="../../assets/NODetailsSex.png"
                        alt
                      />
                    </div>
                    <!-- <img class="sexImg" src="../../assets/detailsMan.png" alt /> -->
                    <!-- <i style="font-size: 22px;" class="el-icon-male"></i> -->
                    <div class="combo_name">
                      <h1>{{ item.clus_Name }}</h1>
                      <h2 v-if="!enabled" style="">￥{{ item.price }}</h2>
                    </div>
                  </div>
                  <!-- <div class="combo_name">
                    <h1>{{ item.clus_Name }}</h1>
                    <h2 v-if="!enabled" style="">￥{{ item.price }}</h2>
                  </div> -->
                  <div class="line-container">
                    <el-popover
                      :ref="'popover' + item.id"
                      placement="top"
                      trigger="click"
                      width="200"
                      v-model="item.visible"
                      v-if="item.isError == 'T'"
                    >
                      <!-- <span style="font-size: 16px">{{ item.errorMsg }}</span> -->
                      <div style="font-size: 16px" v-html="item.errorMsg"></div>
                      <div
                        style="
                          display: flex;
                          justify-content: center;
                          align-items: center;
                        "
                      >
                        <el-button
                          type="warning"
                          round
                          size="small"
                          @click="ClusGotItClick(item.id)"
                          >知道了</el-button
                        >
                      </div>
                      <div class="line-container-combo" slot="reference">
                        <span><i class="el-icon-warning-outline"></i></span>
                      </div>
                    </el-popover>
                    <!-- <div v-if="item.showFlag" class="line-container-combo"><span><i class="el-icon-warning-outline"></i></span></div> -->
                    <div class="combo_operate" v-if="!enabled">
                      <span @click.stop="editClick(item)"
                        ><i class="el-icon-edit-outline"></i> 编辑</span
                      >
                      <span @click.stop="copyClick(item)"
                        ><i class="el-icon-copy-document"></i> 复制</span
                      >
                      <span @click.stop="DeleteClus(item.id)"
                        ><i class="el-icon-delete"></i> 删除</span
                      >
                    </div>
                  </div>
                  <p class="el-icon-sort sort_p" v-if="enabled"></p>
                </li>
              </Vuedraggable>
            </ul>
          </el-tab-pane>
          <el-tab-pane label="官方套餐" name="officialCombo">
            <div class="combo_search">
              <el-input
                v-model="searchVal"
                @input="searchCombo"
                style="flex: 1; flex-shrink: 0"
                size="small"
                placeholder="请输入内容"
              ></el-input>
              <i class="el-icon-plus" title="新建套餐" @click="creatClus"></i>
              <i
                class="el-icon-setting"
                title="新建套餐"
                @click="showAllClusdialogVisible('O')"
              ></i>
              <span @click="sortClick">{{
                enabled ? "取消排序" : "排序"
              }}</span>
            </div>
            <ul class="combo_list">
              <Vuedraggable
                :move="checkMove"
                animation="500"
                handle=".sort_p"
                :list="officialComboList"
                :disabled="!enabled"
                class="list-group"
                ghost-class="ghost"
                @start="sortStart"
                @end="sortEnd"
              >
                <li
                  @click="clusClick(item)"
                  :class="{ active: checkClus.id == item.id }"
                  v-for="(item, idx) in officialComboList"
                  :key="idx"
                >
                  <div style="display: flex; align-items: center">
                    <div class="sexImg" v-if="item.clus_sex == '男'">
                      <img
                        style="width: 28px; height: 28px"
                        src="../../assets/man.png"
                        alt
                      />
                    </div>
                    <div class="sexImg" v-else-if="item.clus_sex == '女'">
                      <img
                        style="width: 28px; height: 28px"
                        src="../../assets/woman.png"
                        alt
                      />
                    </div>
                    <div class="sexImg" v-else-if="item.clus_sex == '不限'">
                      <img
                        style="width: 28px; height: 28px"
                        src="../../assets/detailsSex01.png"
                        alt
                      />
                    </div>
                    <div class="sexImg" v-else>
                      <img
                        style="width: 28px; height: 28px"
                        src="../../assets/NODetailsSex.png"
                        alt
                      />
                    </div>
                    <!-- <img class="sexImg" src="../../assets/detailsMan.png" alt /> -->
                    <!-- <i style="font-size: 22px;" class="el-icon-male"></i> -->
                    <div class="combo_name">
                      <h1>{{ item.clus_Name }}</h1>
                      <h2 v-if="!enabled" style="">￥{{ item.price }}</h2>
                    </div>
                  </div>
                  <!-- <div class="combo_name">
                    <h1>{{ item.clus_Name }}</h1>
                    <h2 v-if="!enabled">￥{{ item.price }}</h2>
                  </div> -->
                  <div class="line-container">
                    <el-popover
                      :ref="'popover' + item.id"
                      placement="top"
                      trigger="click"
                      width="200"
                      v-model="item.visible"
                      v-if="item.isError == 'T'"
                    >
                      <div style="font-size: 16px" v-html="item.errorMsg"></div>
                      <div
                        style="
                          display: flex;
                          justify-content: center;
                          align-items: center;
                        "
                      >
                        <el-button
                          type="warning"
                          round
                          size="small"
                          @click="ClusGotItClick(item.id)"
                          >知道了</el-button
                        >
                      </div>
                      <div class="line-container-combo" slot="reference">
                        <span><i class="el-icon-warning-outline"></i></span>
                      </div>
                    </el-popover>
                    <!-- <div v-if="item.showFlag" class="line-container-combo"><span><i class="el-icon-warning-outline"></i></span></div> -->
                    <div class="combo_operate" v-if="!enabled">
                      <span @click.stop="editClick(item)"
                        ><i class="el-icon-edit-outline"></i> 编辑</span
                      >
                      <span @click.stop="copyClick(item)"
                        ><i class="el-icon-copy-document"></i> 复制</span
                      >
                      <span @click.stop="DeleteClus(item.id)"
                        ><i class="el-icon-delete"></i> 删除</span
                      >
                    </div>
                  </div>
                  <p class="el-icon-sort sort_p" v-if="enabled"></p>
                </li>
              </Vuedraggable>
            </ul>
          </el-tab-pane>
        </el-tabs>
        <div class="lncDiv table_wrap">
          <div class="lncTop table_top">
            <div style="margin-left: 20px">
              <!-- <el-input placeholder="客户名称(为空时默认加载全部)" style="width: 240px" v-model="UserName" size="small"></el-input>
              <el-button type="primary" icon="el-icon-search" @click="GetNewData" style="margin-left: 10px"
                size="small">查询</el-button> -->
              <el-button
                type="warning"
                icon="el-icon-plus"
                @click="DYTeamList()"
                size="small"
                v-if="activeName == 'first'"
                >直接预约</el-button
              >
              <el-button
                type="success"
                icon="el-icon-plus"
                @click="AtnTeamList()"
                size="small"
                v-if="activeName == 'first'"
                >激活</el-button
              >
              <el-button
                type="success"
                icon="el-icon-date"
                @click="showupdateValidityPeriod()"
                size="small"
                v-if="activeName == 'second'"
                >修改有效期</el-button
              >
              <el-button
                type="info"
                icon="el-icon-s-release"
                @click="RevokeAtnTeamList()"
                size="small"
                v-if="activeName == 'second'"
                >撤销激活</el-button
              >
            </div>
            <div class="divRight">
              <el-tooltip content="" placement="left-start" effect="light">
                <template #content>
                  <div class="button-container">
                    <el-button
                      type="primary"
                      size="small"
                      @click="showAddorEditDialog(undefined)"
                      >添加单个客户</el-button
                    >
                    <el-button
                      type="primary"
                      size="small"
                      @click="handleButton2Click"
                      >批量导入客户</el-button
                    >
                  </div>
                </template>
                <el-button type="primary" icon="el-icon-user" size="small"
                  >添加客户</el-button
                >
              </el-tooltip>
              <!-- <el-button
                icon="el-icon-bell"
                @click="showSMSTemplate()"
                size="small"
                v-if="activeName == 'second'"
                >发送短信</el-button
              > -->
              <el-button
                type="danger"
                icon="el-icon-delete"
                @click="DeleteTeamList()"
                size="small"
                >批量删除</el-button
              >
            </div>
          </div>
          <div class="lncMid">
            <el-tabs v-model="activeName" @tab-click="handleClick">
              <el-tab-pane label="未操作" name="first"></el-tab-pane>
              <el-tab-pane label="已操作" name="second"></el-tab-pane>

              <el-tab-pane label="导入异常" name="third">
                <span slot="label">
                  导入异常
                  <el-badge
                    :value="waitNumber"
                    v-if="waitNumber != 0"
                    :max="9999"
                  ></el-badge>
                </span>
              </el-tab-pane>
            </el-tabs>
            <div class="toggleSelection">
              <div style="display: flex">
                <el-button @click="toggleSelection(tableData)" size="medium"
                  >全选</el-button
                >
                <el-button @click="toggleSelection()" size="medium"
                  >取消全选</el-button
                >
                <div class="toggleSelectionCon">
                  共选择（<span style="color: rgb(204, 162, 23)">{{
                    ids.length
                  }}</span
                  >）条
                </div>
              </div>
              <div style="margin-right: 20px">
                <el-input
                  placeholder="姓名/手机号证件号"
                  style="width: 240px"
                  v-model="UserName"
                  size="small"
                  @input="GetNewData"
                ></el-input>
              </div>
            </div>
            <el-table
              :data="tableData"
              v-loading="loading"
              element-loading-text="拼命加载中"
              ref="tableData"
              border
              stripe
              :fit="true"
              row-key="id"
              @selection-change="handleSelectionChangePeople"
              :height="height"
            >
              <el-table-column
                type="selection"
                width="55"
                align="center"
                :reserve-selection="true"
              ></el-table-column>
              <!-- <el-table-column prop="lnc_Code" label="单位编码" width="200" align="center"></el-table-column> -->
              <el-table-column label="姓名" align="center">
                <template slot-scope="{ row }">
                  <div class="ellipsis-column_name" v-bind:title="row.name">
                    <el-button
                      @click="showAddorEditDialog(row)"
                      type="text"
                      plain
                      >{{ row.name }}</el-button
                    >
                  </div>
                </template>
              </el-table-column>
              <!-- <el-table-column prop="sex" label="性别" align="center"></el-table-column> -->
              <el-table-column align="center">
                <template slot="header" slot-scope="scope">
                  <span
                    :class="
                      sexCheckList.length > 0 ? 'table_column_header' : ''
                    "
                    >性别</span
                  >
                  <el-popover placement="bottom" width="200" trigger="click">
                    <el-checkbox-group
                      v-model="sexCheckList"
                      style="max-height: 60vh; overflow-x: auto"
                    >
                      <el-checkbox
                        v-for="(item, index) in sexFilters"
                        :key="index"
                        :label="item.text"
                        style="width: 160px; margin-right: 0px"
                        @change="depaFilterGroup"
                      ></el-checkbox
                      ><br />
                    </el-checkbox-group>
                    <i class="el-icon-arrow-down" slot="reference"></i>
                  </el-popover>
                </template>
                <template slot-scope="scope">
                  <span>{{ scope.row.sex }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="age"
                label="年龄"
                align="center"
              ></el-table-column>
              <el-table-column
                prop="tel"
                label="手机号码"
                align="center"
              ></el-table-column>
              <el-table-column
                prop="matrimony"
                label="婚姻状态"
                align="center"
              ></el-table-column>
              <el-table-column
                prop="idCard"
                label="证件号"
                align="center"
                width="220"
              ></el-table-column>
              <el-table-column
                prop="department"
                label="部门"
                align="center"
              ></el-table-column>
              <!-- <el-table-column prop="group" label="分组" :filters="filters" :filter-method="filterGroup" align="center"> -->
              <el-table-column align="center">
                <template slot="header" slot-scope="scope">
                  <span
                    :class="
                      groupCheckList.length > 0 ? 'table_column_header' : ''
                    "
                    >分组</span
                  >
                  <el-popover placement="bottom" width="200" trigger="click">
                    <el-input
                      placeholder="分组"
                      style="
                        padding: 0px;
                        width: 160px;
                        text-align: center;
                        margin-bottom: 5px;
                      "
                      v-model="filtertext"
                      size="small"
                    ></el-input>
                    <el-checkbox-group
                      v-model="groupCheckList"
                      style="max-height: 60vh; overflow-x: auto"
                    >
                      <el-checkbox
                        v-for="(item, index) in filters"
                        :key="index"
                        :label="item.text"
                        style="width: 160px; margin-right: 0px"
                        @change="depaFilterGroup"
                      ></el-checkbox
                      ><br />
                    </el-checkbox-group>
                    <i class="el-icon-arrow-down" slot="reference"></i>
                  </el-popover>
                </template>
                <template slot-scope="scope">
                  <span>{{ scope.row.group }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="position"
                label="职级"
                align="center"
              ></el-table-column>
              <!-- </el-table-column> -->
              <el-table-column
                prop="state"
                label="状态"
                align="center"
                width="220"
                v-if="activeName == 'second'"
              ></el-table-column>
              <el-table-column
                prop="errorMsg"
                label="错误提示"
                align="center"
                width="220"
                v-if="activeName == 'third'"
              ></el-table-column>
              <el-table-column
                prop="remark"
                label="备注"
                align="center"
              ></el-table-column>
              <!-- <el-table-column label="操作" align="center">
                <template slot-scope="scope">
                  <el-button @click="showAddorEditDialog(scope.row)" type="primary" plain>编辑</el-button>
                </template>
              </el-table-column> -->
            </el-table>
            <div class="pageNation"></div>
            <el-dialog
              :title="dialogTitleOne"
              :visible.sync="dialogVisibleOne"
              width="55%"
            >
              <div class="container">
                <!-- <div class="radio-container">
          <el-radio v-model="radio" label="1" @input="DocumentTypeSwitching"
            >身份证</el-radio
          >
          <el-radio v-model="radio" label="2" @input="DocumentTypeSwitching"
            >员工号</el-radio
          >
        </div> -->
                <div class="form-container">
                  <el-form
                    :inline="true"
                    :label-position="'center'"
                    :rules="rules"
                    :model="TeamList"
                    ref="MyTeamList"
                    label-width="80px"
                  >
                    <el-row>
                      <el-col :span="12">
                        <el-form-item label="姓名" prop="name">
                          <el-input v-model="TeamList.name"></el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="性别" prop="sex">
                          <!-- <el-input v-model="TeamList.sex"></el-input> -->
                          <el-select v-model="TeamList.sex">
                            <el-option label="男" value="1"></el-option>
                            <el-option label="女" value="0"></el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="12">
                        <el-form-item label="年龄" prop="age">
                          <el-input v-model="TeamList.age"></el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <!-- <el-form-item label="民族"> -->
                        <!-- <el-input v-model="TeamList.nation"></el-input> -->
                        <el-form-item label="民族" prop="nation">
                          <el-select v-model="TeamList.nation">
                            <el-option
                              v-for="(item, index) in columns"
                              :key="index"
                              :label="item"
                              :value="item"
                            ></el-option>
                          </el-select>
                          <!-- </el-form-item> -->
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="12">
                        <el-form-item label="手机号" prop="tel">
                          <el-input v-model="TeamList.tel"></el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item
                          label="出生日期"
                          prop="birthday"
                          label-width="100"
                        >
                          <!-- <el-input v-model="TeamList.birthday"></el-input> -->
                          <el-date-picker
                            v-model="TeamList.birthday"
                            type="date"
                            placeholder="选择日期"
                          >
                          </el-date-picker>
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="12">
                        <el-form-item label="证件类型" prop="employeeId">
                          <!-- <el-input v-model="TeamList.employeeId"></el-input> -->
                          <el-select v-model="TeamList.id_type">
                            <el-option
                              v-for="(item, index) in IdTypecolumns"
                              :key="index"
                              :label="item.text"
                              :value="item.value"
                            ></el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>

                      <el-col :span="12">
                        <el-form-item label="婚否" prop="matrimony">
                          <el-select v-model="TeamList.matrimony">
                            <el-option label="未婚" value="未婚"></el-option>
                            <el-option label="已婚" value="已婚"></el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="12">
                        <el-form-item label="证件号" prop="idCard">
                          <el-input
                            v-model="TeamList.idCard"
                            @input="parseIDCard"
                            maxlength="18"
                          ></el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="部门">
                          <el-input v-model="TeamList.department"></el-input>
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="12">
                        <el-form-item label="所在分组">
                          <!-- <el-input v-model="TeamList.group"></el-input> -->
                          <el-select
                            v-model="TeamList.group"
                            filterable
                            clearable
                          >
                            <el-option
                              v-for="(item, index) in fixed_unitComboList"
                              :key="index"
                              :label="item.clus_Name"
                              :value="item.clus_Name"
                            ></el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="职级">
                          <el-input v-model="TeamList.position"></el-input>
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="12">
                        <el-form-item label="客户等级">
                          <div style="font-size: 18px">
                            <el-radio size="medium " v-model="grade" label="1"
                              >普通客户</el-radio
                            >
                            <el-radio v-model="grade" label="2"
                              >VIP客户</el-radio
                            >
                          </div>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="地址">
                          <el-input v-model="TeamList.address"></el-input>
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </el-form>
                </div>
              </div>
              <div slot="footer">
                <el-button
                  type="danger"
                  size="small"
                  @click="dialogVisibleOne = false"
                  >取消</el-button
                >
                <el-button
                  type="primary"
                  size="small"
                  @click="addSingleCustomer"
                  >确定</el-button
                >
              </div>
            </el-dialog>
            <!--导入对话框-->
            <el-dialog
              :title="dialogTitle"
              :visible.sync="dialogVisible"
              width="80%"
            >
              <el-form :label-position="'right'" label-width="80px">
                <el-form-item label="客户名单">
                  <el-upload
                    class="upload-demo"
                    action=""
                    accept=".xls,.xlsx"
                    :http-request="upload"
                    :on-success="handleUploadSuccess"
                    :before-upload="beforeUploadFile"
                    :on-preview="handlePreview"
                    :on-remove="handleRemove"
                    :before-remove="beforeRemove"
                    :multiple="false"
                    :limit="1"
                    :file-list="fileList"
                    :on-exceed="handleExceed"
                  >
                    <el-button type="primary"
                      >上传<i class="el-icon-upload el-icon--right"></i
                    ></el-button>
                    <div
                      style="display: inline; margin-left: 2px"
                      slot="tip"
                      class="el-upload__tip"
                    >
                      只能上传xls/xlsx文件，且不超过4000行
                    </div>
                  </el-upload>
                </el-form-item>
                <el-table
                  v-if="importTableData.length != 0"
                  :data="importTableData"
                  v-loading="uploadLoading"
                  element-loading-text="拼命加载中"
                  border
                  stripe
                  :fit="true"
                  :height="dialogHeight"
                >
                  <el-table-column
                    prop="lnc_Code"
                    label="单位编码"
                    width="200"
                    align="center"
                  ></el-table-column>
                  <el-table-column
                    prop="name"
                    label="姓名"
                    align="center"
                  ></el-table-column>
                  <el-table-column
                    prop="sex"
                    label="性别"
                    align="center"
                  ></el-table-column>
                  <el-table-column
                    prop="age"
                    label="年龄"
                    align="center"
                  ></el-table-column>
                  <el-table-column
                    prop="tel"
                    label="手机号码"
                    align="center"
                  ></el-table-column>
                  <el-table-column
                    prop="matrimony"
                    label="婚姻状态"
                    align="center"
                  ></el-table-column>
                  <el-table-column
                    prop="idCard"
                    label="证件号"
                    align="center"
                    width="220"
                  ></el-table-column>
                  <el-table-column
                    prop="nation"
                    label="民族"
                    align="center"
                  ></el-table-column>
                  <el-table-column
                    prop="department"
                    label="部门"
                    align="center"
                  ></el-table-column>
                  <el-table-column
                    prop="group"
                    label="分组"
                    align="center"
                  ></el-table-column>
                  <el-table-column
                    prop="position"
                    label="职级"
                    align="center"
                  ></el-table-column>
                  <el-table-column
                    prop="remark"
                    label="备注"
                    align="center"
                  ></el-table-column>
                  <el-table-column
                    prop="profession"
                    label="工种"
                    align="center"
                  ></el-table-column>
                  <el-table-column
                    prop="InjuryDuration"
                    label="接害工龄"
                    align="center"
                  ></el-table-column>
                  <el-table-column
                    prop="seniority"
                    label="总工龄"
                    align="center"
                  ></el-table-column>
                  <el-table-column
                    prop="classification"
                    label="职业分类"
                    align="center"
                  ></el-table-column>
                  <el-table-column
                    prop="factors"
                    label="危害因素"
                    align="center"
                  ></el-table-column>
                  <el-table-column
                    prop="doctor"
                    label="开单医生"
                    align="center"
                  ></el-table-column>
                  <el-table-column
                    prop="expense"
                    label="费别"
                    align="center"
                  ></el-table-column>
                  <el-table-column
                    prop="medical"
                    label="医疗机构"
                    align="center"
                  ></el-table-column>
                </el-table>
              </el-form>
              <div slot="footer">
                <el-button
                  type="danger"
                  size="small"
                  @click="dialogVisible = false"
                  >取消</el-button
                >
                <el-button type="primary" size="small" @click="addTeamList"
                  >确定</el-button
                >
              </div>
            </el-dialog>
            <!--新增/编辑对话框-->
            <el-dialog
              :title="AtnDialogTitle"
              :visible.sync="AtnDialogVisible"
              :fullscreen="true"
              center
            >
              <div class="dialog-content">
                <el-row>
                  <el-col class="col-box" :span="12">
                    <!-- <el-card class="box-card">
                      <div slot="header" class="clearfix">
                        <span>基础设置</span>
                      </div>
                      <el-form>
                        <el-form-item label="卡余额"></el-form-item>
                      </el-form>
                    </el-card> -->
                    <el-card class="box-card">
                      <div slot="header" class="clearfix">
                        <span style="font-size: 18px">1、短信通知及有效期</span>
                      </div>
                      <div class="el-box-card-item-sms">
                        <div class="el-box-card-item-content">
                          <el-checkbox
                            v-model="smsChecked"
                            border
                            style="width: 200px"
                          >
                            短信通知
                          </el-checkbox>
                        </div>
                        <div class="box-card-Atn-cont" v-if="smsChecked">
                          <div
                            class="textareaDiv"
                            :style="{ height: dialogHeight, width: '99%' }"
                          >
                            <div class="textareaCon">
                              <div
                                style="
                                  margin-top: 20px;
                                  margin-bottom: 20px;
                                  height: 10px;
                                "
                              >
                                <el-form>
                                  <el-form-item label="可用模板:">
                                    <el-select
                                      v-model="SMSTemplateName"
                                      placeholder="请选择"
                                      @change="selectSMSTemplateTask"
                                    >
                                      <el-option
                                        v-for="(
                                          item, index
                                        ) in tableSMSConstData"
                                        :key="index"
                                        :label="item.sms_Name"
                                        :value="item.sms_Code"
                                      >
                                      </el-option>
                                    </el-select>
                                  </el-form-item>
                                  <el-form-item label="模板名称:">
                                    <div>
                                      <el-radio
                                        v-model="templateRadio"
                                        @input="selectLastSMSTemplateTask"
                                        label="1"
                                        border
                                        >使用上次的模板</el-radio
                                      >
                                      <el-radio
                                        v-model="templateRadio"
                                        @input="selectSMSTemplateTask('1004')"
                                        label="2"
                                        border
                                        >使用默认模板</el-radio
                                      >
                                    </div>
                                  </el-form-item>
                                  <el-form-item :label-width="formLabelWidth">
                                    <template>
                                      <div class="textareaDiv">
                                        <div class="textareaCon">
                                          <div
                                            style="
                                              margin-top: 20px;
                                              margin-bottom: 20px;
                                              height: 10px;
                                            "
                                          >
                                            <div
                                              id="div-template"
                                              class="templet-header"
                                            >
                                              <div class="keywords">
                                                <div class="detail" id="detail">
                                                  <span>关键字：</span>
                                                  <ul id="memberInfo">
                                                    <li
                                                      isFocus="true"
                                                      v-for="(
                                                        nounId, index
                                                      ) in SMSnounIds"
                                                      :key="index"
                                                      @click="selectDetail"
                                                    >
                                                      {{ nounId.name }}
                                                    </li>
                                                  </ul>
                                                </div>
                                              </div>

                                              <div class="templet-input">
                                                <div>
                                                  <span class="title"
                                                    >模板内容：</span
                                                  >
                                                  <div class="text">
                                                    <textarea
                                                      class="infoText"
                                                      style="height: 148px"
                                                      v-model="SMSContent"
                                                      @click="
                                                        handleTextareaClick
                                                      "
                                                      @keyup="handleKeyup"
                                                      @keydown="handleKeydown"
                                                      ref="textarea"
                                                      :isFocus="true"
                                                      :rows="5"
                                                    ></textarea>
                                                  </div>
                                                </div>
                                                <div>
                                                  <span class="title"
                                                    >模板预览：</span
                                                  >
                                                  <div class="text">
                                                    <div class="textareaCon">
                                                      <div
                                                        class="infoText"
                                                        v-html="SMSPreview"
                                                        style="
                                                          background-color: aliceblue;
                                                        "
                                                      ></div>
                                                    </div>
                                                  </div>
                                                </div>
                                              </div>
                                            </div>
                                            <div></div>
                                          </div>
                                        </div>
                                      </div>
                                    </template>
                                  </el-form-item>
                                </el-form>
                                <br />
                                <div></div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- <div class="el-box-card-item-content" style="margin-left: 80px;"><el-checkbox v-model="effeChecked">  即将过期提醒 </el-checkbox></div> -->
                      <div
                        class="el-box-card-item-time"
                        style="margin-top: 20px"
                      >
                        <el-form :label-position="'right'" label-width="80px">
                          <el-form-item>
                            <span>有效期</span>
                            <div class="block">
                              <el-date-picker
                                v-model="effectiveDate"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                value-format="yyyy-MM-dd"
                              >
                              </el-date-picker>
                            </div>
                          </el-form-item>
                          <el-form-item>
                            <div><span>过期提醒</span></div>
                            <div style="width: 160px; display: inline">
                              <el-select
                                v-model="selectedDay"
                                placeholder="请选择"
                              >
                                <el-option
                                  v-for="day in 15"
                                  :key="day"
                                  :value="day"
                                  :label="day + '天'"
                                ></el-option>
                              </el-select>
                            </div>
                            <span>天</span>
                            <el-time-picker
                              v-model="timeValue"
                              value-format="HH:mm:ss"
                              :picker-options="pickerOptions"
                              placeholder="任意时间点"
                            >
                            </el-time-picker>
                          </el-form-item>
                        </el-form>
                      </div>
                    </el-card>
                  </el-col>
                  <el-col :span="12">
                    <el-card class="box-card">
                      <div slot="header" class="clearfix">
                        <span style="font-size: 18px">2、选择套餐</span>
                      </div>
                      <el-table
                        ref="tableClusData"
                        :data="tableClusData"
                        v-loading="loading"
                        element-loading-text="拼命加载中"
                        border
                        stripe
                        :fit="true"
                        row-key="id"
                        @selection-change="handleSelectionChangeids"
                        height="400px"
                      >
                        <el-table-column
                          type="selection"
                          width="55"
                          align="center"
                          :reserve-selection="true"
                        ></el-table-column>
                        <el-table-column
                          prop="clus_Code"
                          label="套餐编码"
                          width="200"
                          align="center"
                        ></el-table-column>
                        <el-table-column
                          prop="clus_Name"
                          label="套餐名称"
                          align="center"
                        ></el-table-column>
                        <el-table-column
                          prop="price"
                          label="价格"
                          align="center"
                          sortable
                        ></el-table-column>
                        <el-table-column
                          prop="clus_sex"
                          label="性别"
                          align="center"
                          sortable
                        ></el-table-column>
                        <el-table-column
                          prop="state"
                          label="状态"
                          align="center"
                          sortable
                        ></el-table-column>
                      </el-table>
                    </el-card>
                    <el-card class="box-card">
                      <div slot="header" class="clearfix">
                        <span style="font-size: 18px">3、加项包</span>
                      </div>
                      <div class="el-box-card-item-sms">
                        <div class="el-box-card-item-content">
                          <el-checkbox
                            v-model="addPackageChecked"
                            border
                            style="width: 200px"
                          >
                            加项包
                          </el-checkbox>
                        </div>
                        <div v-show="addPackageChecked">
                          <el-table
                            ref="addPackageData"
                            :data="addPackageData"
                            v-loading="loading"
                            element-loading-text="拼命加载中"
                            border
                            stripe
                            :fit="true"
                            row-key="id"
                            @selection-change="handleaddPackageChangeids"
                            height="400px"
                          >
                            <el-table-column
                              type="selection"
                              width="55"
                              align="center"
                              :reserve-selection="true"
                            ></el-table-column>
                            <el-table-column
                              prop="item_Code"
                              label="组合编码"
                              width="200"
                              align="center"
                            ></el-table-column>
                            <el-table-column
                              prop="item_Name"
                              label="组合名称"
                              align="center"
                            ></el-table-column>
                            <el-table-column
                              prop="price"
                              label="价格"
                              align="center"
                              sortable
                            ></el-table-column>
                            <el-table-column
                              prop="state"
                              label="状态"
                              align="center"
                              sortable
                            ></el-table-column>
                          </el-table>
                        </div>
                      </div>
                    </el-card>
                  </el-col>
                </el-row>
              </div>

              <div slot="footer" class="button-area">
                <el-button
                  type="danger"
                  size="medium"
                  @click="cancelAtnDialogVisible"
                  >取消</el-button
                >
                <el-button type="success" size="medium" @click="showAtnTeaam"
                  >预览</el-button
                >
                <!-- <el-button type="success" size="medium" @click="activationTeaam">激活</el-button> -->
              </div>
            </el-dialog>
            <el-dialog
              title="激活预览"
              :visible.sync="showAtnDialogTitle"
              width="50%"
              top="5vh"
            >
              <div
                class="activatePreview"
                :style="{ height: 'calc( 100vh - 350px)' }"
              >
                <el-card class="box-card" style="width: 100%">
                  <div class="text item">
                    <div>
                      体检人(<span
                        style="font-size: 18px; color: rgb(255, 0, 0)"
                        >{{ ids.length }}</span
                      >)个：
                      <div class="textTable">
                        <span>{{ NameList }}</span>
                      </div>
                    </div>
                    <div>
                      分组：<span>{{ groupName }}</span>
                    </div>
                    <div class="textTable">
                      选择套餐({{ tableClusDatas.length }}个):
                    </div>
                    <el-table
                      :data="tableClusDatas"
                      v-loading="loading"
                      element-loading-text="拼命加载中"
                      border
                      stripe
                      :fit="true"
                      :height="AtndialogHeight"
                    >
                      <el-table-column
                        prop="clus_Name"
                        label="套餐名称"
                        align="center"
                      ></el-table-column>
                      <el-table-column
                        prop="price"
                        label="价格"
                        align="center"
                      ></el-table-column>
                    </el-table>
                    <div class="textTable">
                      加项包({{ addPackageDatas.length }}个):
                    </div>
                    <el-table
                      :data="addPackageDatas"
                      v-loading="loading"
                      element-loading-text="拼命加载中"
                      border
                      stripe
                      :fit="true"
                      :height="AtndialogHeight"
                    >
                      <el-table-column
                        prop="item_Name"
                        label="套餐名称"
                        align="center"
                      ></el-table-column>
                      <el-table-column
                        prop="price"
                        label="价格"
                        align="center"
                      ></el-table-column>
                    </el-table>
                  </div>
                  <div class="textTable">
                    <span>有效期：</span>
                    <span>{{ effectiveDate[0] }}</span>
                    <span>至</span>
                    <span>{{ effectiveDate[1] }}</span>
                  </div>
                  <div class="textTable">
                    <span>过期提醒:</span>
                    <span>{{ expireTime }}</span>
                  </div>
                  <div class="smsDiv">
                    <span>短信预览：</span>
                    <div v-if="smsChecked" v-html="SMSPreview"></div>
                    <div v-else>
                      <span style="font-family: Arial; font-weight: 700"
                        >不发送</span
                      >
                    </div>
                  </div>
                </el-card>
              </div>
              <div slot="footer">
                <el-button
                  type="danger"
                  size="small"
                  @click="showAtnDialogTitle = false"
                  >取消</el-button
                >
                <!-- <el-button type="success" size="small" @click="DYConfirm">确定</el-button> -->
                <el-button type="success" size="medium" @click="activationTeaam"
                  >激活</el-button
                >
              </div>
            </el-dialog>
            <el-dialog
              :title="DYYDialogTitle"
              :visible.sync="DYYdialogVisible"
              width="80%"
            >
              <div class="DYYDiv" :style="{ height: height }">
                <div class="selectTimeDiv">
                  <div class="DYConfirmDiv"><span>1、选择时间</span></div>
                  <div id="all">
                    <div id="calendar">
                      <div class="month">
                        <div class="prevMonth" @click="prevMonth" v-show="pre">
                          上一月
                        </div>
                        <div class="prevMonth_" v-show="pre_"></div>
                        <div class="year-month">
                          <span class="choose-year">{{ currentDateStr }}</span>
                        </div>
                        <div class="nextMonth" @click="nextMonth">下一月</div>
                      </div>
                      <div class="weekdays">
                        <div
                          class="week-item"
                          v-for="item of weekList"
                          :key="item"
                        >
                          {{ item }}
                        </div>
                      </div>
                      <div class="calendar-inner">
                        <div
                          class="calendar-item"
                          v-for="(item, index) of calendarList"
                          :key="index"
                          v-bind:class="[item.disable ? 'disabled' : '']"
                        >
                          <div
                            @click="
                              item.Thing === ''
                                ? ''
                                : matchDate(item.ThingName)
                                ? dayClick(item.date, item.ThingName, index)
                                : ''
                            "
                            :class="
                              ClassKey === calendarList[index].value
                                ? 'chooseDay'
                                : ''
                            "
                          >
                            <div
                              class="calendarDate"
                              v-bind:class="switchHaoyuanClass(item.ThingName)"
                            >
                              {{ item.date }}
                            </div>
                            <div
                              class="calendarThing"
                              v-bind:class="switchHaoyuanClass(item.ThingName)"
                            >
                              {{ item.Thing }}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div id="all" style="margin-top: 10px">
                    <!-- 套餐 -->
                    <div class="sumtime" v-show="stateShow">
                      <div
                        :class="items.team_Surplus === 0 ? 'timeTwo' : 'timese'"
                        v-for="(items, index) in sumtimeList"
                        :key="index"
                        @click="timeBtn(items, index)"
                      >
                        <span
                          :class="
                            TeamSpans === index ? 'TeamSpan' : 'TeamSpanTwo'
                          "
                        >
                          {{ items.sumtime_Name }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="selectTimeDiv">
                  <div class="DYConfirmDiv">
                    <span>2、选择套餐</span
                    ><span>(代预约只能选择单位套餐)</span>
                  </div>
                  <el-table
                    :data="
                      tableClusData.filter(
                        (data) =>
                          !searchClus_Name ||
                          data.clus_Name
                            .toLowerCase()
                            .includes(searchClus_Name.toLowerCase())
                      )
                    "
                    height="60%"
                    highlight-current-row
                    @current-change="handleCurrentChange"
                    style="width: 100%"
                  >
                    <el-table-column prop="clus_Code"> </el-table-column>
                    <el-table-column prop="clus_Name"> </el-table-column>
                    <el-table-column prop="price" align="center">
                      <template slot="header" slot-scope="scope">
                        <el-input
                          v-model="searchClus_Name"
                          size="mini"
                          placeholder="输入关键字搜索"
                        />
                      </template>
                    </el-table-column>
                  </el-table>
                  <div class="DYConfirmDiv">
                    <span>3、确认订单</span>
                    <div>
                      <span>体检人:</span
                      ><span class="DYConfirmSpan">{{ teamName }}</span>
                    </div>
                    <div>
                      <span>体检时间：</span
                      ><span class="DYConfirmSpan">{{ ClassKey }}</span>
                    </div>
                    <div>
                      <span>体检时段：</span
                      ><span class="DYConfirmSpan">{{ sumtime_Name }}</span>
                    </div>
                    <div>
                      <span>体检套餐：</span
                      ><span class="DYConfirmSpan">{{
                        CurrentClus.clus_Name
                      }}</span>
                    </div>
                    <div>
                      <span>订单总价：</span
                      ><span class="DYConfirmSpan">{{
                        CurrentClus.price
                      }}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div slot="footer">
                <el-button
                  type="danger"
                  size="small"
                  @click="DYYdialogVisible = false"
                  >取消</el-button
                >
                <el-button type="success" size="small" @click="DYConfirm"
                  >确定</el-button
                >
              </div>
            </el-dialog>
            <el-dialog
              title="修改有效期"
              :visible.sync="ValidityPeriod"
              width="30%"
            >
              <div style="display: flex">
                <div>
                  <el-form
                    :label-position="'right'"
                    :style="{ height: dialogHeight }"
                  >
                    <el-form-item>
                      <div>
                        <el-checkbox v-model="CYchecked">催约通知</el-checkbox>
                      </div>
                    </el-form-item>
                    <el-form-item>
                      <span>有效期</span>

                      <div class="block">
                        <el-date-picker
                          v-model="effectiveDate"
                          type="daterange"
                          range-separator="至"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"
                          value-format="yyyy-MM-dd"
                          @change="pickerHandleChange"
                        >
                        </el-date-picker>
                      </div>
                    </el-form-item>
                    <el-form-item>
                      <div><span>过期提醒</span></div>
                      <div style="width: 160px; display: inline">
                        <el-select
                          v-model="selectedDay"
                          placeholder="请选择"
                          @change="pickerHandleChange"
                        >
                          <el-option
                            v-for="day in 15"
                            :key="day"
                            :value="day"
                            :label="day + '天'"
                          ></el-option>
                        </el-select>
                      </div>
                      <span>天</span>
                      <el-time-picker
                        v-model="timeValue"
                        value-format="HH:mm:ss"
                        :picker-options="pickerOptions"
                        placeholder="任意时间点"
                        @change="pickerHandleChange"
                      >
                      </el-time-picker>
                    </el-form-item>
                    <el-form-item>
                      <div><span>短信提醒时间</span></div>
                      <el-input
                        v-model="expireTime"
                        placeholder="请选择有效期"
                        size="small"
                        readonly
                      ></el-input>
                    </el-form-item>
                  </el-form>
                </div>
              </div>

              <div slot="footer">
                <el-button
                  type="danger"
                  size="small"
                  @click="ValidityPeriod = false"
                  >取消</el-button
                >
                <el-button
                  type="success"
                  size="small"
                  @click="updateValidityPeriod"
                  >保存</el-button
                >
              </div>
            </el-dialog>
            <!--复制套餐对话框-->
            <el-dialog
              :title="dialogTitle"
              :visible.sync="copyClickDialogVisible"
            >
              <el-form :label-position="'right'" label-width="100px">
                <el-form-item label="套餐名称">
                  <el-input
                    v-if="clusInfo.clusName == ''"
                    placeholder="按原有套餐名称复制"
                    size="small"
                    style="width: 80%"
                    :disabled="true"
                  ></el-input>
                  <el-input
                    v-else
                    v-model="clusInfo.clusName"
                    placeholder="请输入套餐名称"
                    size="small"
                    style="width: 80%"
                  ></el-input>
                </el-form-item>
                <!-- <el-form-item label="复制为">
              <el-radio-group v-model="radio">
                <el-radio :label="3">备选项</el-radio>
                <el-radio :label="6">备选项</el-radio>
                <el-radio :label="9">备选项</el-radio>
              </el-radio-group>
            </el-form-item> -->
                <el-form-item label="复制到单位">
                  <div class="getSeleLnc">
                    <el-select
                      v-model="clusInfo.lnc_Code"
                      placeholder="请选择单位"
                      size="small"
                      filterable
                      clearable
                    >
                      <el-option label="官方套餐" value="*"></el-option>
                      <el-option
                        v-for="item in drawerData"
                        :key="item.lnc_Code"
                        :label="item.lnc_Name"
                        :value="item.lnc_Code"
                      ></el-option>
                    </el-select>
                  </div>
                </el-form-item>
              </el-form>
              <div slot="footer">
                <el-button
                  type="danger"
                  size="small"
                  @click="copyClickDialogVisible = false"
                  >取消</el-button
                >
                <el-button type="primary" size="small" @click="CopyClus()"
                  >确定</el-button
                >
              </div>
            </el-dialog>
            <!-- <el-dialog
              title="发送短信"
              :visible.sync="SMSdialogVisible"
              width="80%"
            >
              <div
                :style="{ height: dialogHeight }"
                style="margin-bottom: 10px"
              >
                <span>用户姓名：</span><span>{{ userName }}</span>
                <div class="textareaDiv">
                  <div class="textareaCon">
                    <div
                      style="
                        margin-top: 20px;
                        margin-bottom: 20px;
                        height: 10px;
                      "
                    >
                      <el-form>
                        <el-form-item label-width="80px">
                          <template>
                            <el-button
                              class="textareaNou"
                              v-for="(nounId, index) in SMSnounIds"
                              :key="index"
                              @click="textareaAdd(nounId)"
                              type="blue"
                              size="small"
                              >{{ nounId.name }}</el-button
                            >
                          </template>
                        </el-form-item>
                        <el-form-item label-width="80px">
                          <div></div>
                          <div
                            id="SMSContentTextArea"
                            class="infoText"
                            ref="SMSContentTextArea"
                            :contenteditable="true"
                            v-html="SMSContent"
                            @input="btnHandelClick"
                          ></div>
                        </el-form-item>
                        <el-form-item label-width="80px">
                          <div class="textareaCon">
                            <span>短信预览：</span>
                            <div class="infoText" v-html="SMSPreview"></div>
                          </div>
                        </el-form-item>
                      </el-form>
                      <br />
                      <div></div>
                    </div>
                  </div>
                </div>
              </div>
              <span slot="footer" class="dialog-footer">
                <el-button @click="SMSdialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="SendReminderMessages"
                  >确 定</el-button
                >
              </span>
            </el-dialog> -->

            <el-dialog
              title="单位套餐设置"
              :visible.sync="AllClusdialogVisible"
              width="50%"
            >
              <div style="margin-bottom: 10px">
                <div class="AllClusButtonTop">
                  <div style="width: 80%">
                    <el-input
                      v-model="CombName"
                      @input="searchCombSetting"
                      size="small"
                      placeholder="请输入套餐名称"
                      style="width: 50%"
                    ></el-input>
                  </div>
                  <div class="AllClusButtonDiv">
                    <div class="AllClusButton">
                      <!-- <div><span  @click.stop="copyClick(item)"><i class="el-icon-copy-document"></i> 一键复制</span></div> -->
                      <!-- <el-popover
                      placement="right"
                      width="400"
                      trigger="click"
                      title="复制到"
                    >
                      <div class="lnc_list">
                        <el-input
                          v-model="drawerIpnut"
                          @input="querys"
                          size="small"
                          placeholder="请输入单位名称"
                        ></el-input>
                        <p
                          @click="CopyClusList(lncItem.lnc_Code)"
                          v-for="lncItem in drawerData"
                          :key="lncItem.lnc_Code"
                        >
                          {{ lncItem.lnc_Name }}
                        </p>
                        <p @click="CopyClusList('*')">复制到 官方套餐</p>
                      </div>
                      <el-button type="primary" icon="el-icon-copy-document" slot="reference" size="small">一键复制</el-button
                      >
                    </el-popover> -->
                      <el-button
                        type="primary"
                        icon="el-icon-copy-document"
                        size="small"
                        @click="copyClick()"
                        >一键复制</el-button
                      >
                    </div>
                    <div class="AllClusButton">
                      <!-- <span @click.stop="DeleteClus(item.id)"><i class="el-icon-delete"></i> 删除</span> -->
                      <el-button
                        type="success"
                        icon="el-icon-download"
                        size="small"
                        @click="ExportExcel"
                        >一键导出</el-button
                      >
                    </div>
                    <div class="AllClusButton">
                      <!-- <el-button type="success" icon="el-icon-copy-document" size="small" @click="updateValidityPeriod">导入套餐</el-button> -->
                      <el-upload
                        class="avatar-uploader"
                        action=""
                        accept=".xls,.xlsx"
                        :http-request="beforeClusAvatarUpload"
                        :show-file-list="false"
                        :on-success="handleClusAvatarSuccess"
                      >
                        <!-- <i  class="el-icon-folder-add"></i> -->
                        <el-button
                          type="success"
                          icon="el-icon-folder-add"
                          size="small"
                          >导入套餐</el-button
                        >
                      </el-upload>
                    </div>
                  </div>
                </div>
                <div>
                  <el-table
                    ref="tableClusData"
                    :data="settingComboList"
                    v-loading="loading"
                    element-loading-text="拼命加载中"
                    border
                    stripe
                    :fit="true"
                    row-key="id"
                    @selection-change="handleSelectionChangeids"
                    :height="dialogHeight"
                  >
                    <el-table-column
                      type="selection"
                      width="55"
                      align="center"
                      :reserve-selection="true"
                    ></el-table-column>
                    <el-table-column
                      prop="clus_Code"
                      label="套餐编码"
                      width="200"
                      align="center"
                    ></el-table-column>
                    <el-table-column
                      prop="clus_Name"
                      label="套餐名称"
                      align="center"
                    ></el-table-column>
                    <el-table-column
                      prop="price"
                      label="价格"
                      align="center"
                      sortable
                    ></el-table-column>
                    <el-table-column
                      prop="clus_sex"
                      label="性别"
                      align="center"
                      sortable
                    ></el-table-column>
                    <el-table-column
                      prop="state"
                      label="状态"
                      align="center"
                      sortable
                    ></el-table-column>
                  </el-table>
                </div>
              </div>
            </el-dialog>
            <!--套餐排序对话框-->
            <el-dialog
              title="单位套餐排序"
              :visible.sync="sortClickDialogVisible"
              width="50%"
            >
              <div style="margin-bottom: 10px">
                <div>
                  <!-- <Vuedraggable v-model="settingComboList" :options="{animation: 150}" @end="sortEnd"> -->

                  <el-table style="width: 100%">
                    <Vuedraggable
                      :move="checkMove"
                      animation="500"
                      handle=".sort_p"
                      :list="settingComboList"
                      class="list-group"
                      ghost-class="ghost"
                      @start="sortStart"
                      @end="sortEnd"
                    >
                      <!-- propData -->
                      <el-table-column
                        :prop="item.propValue"
                        :label="item.label"
                        :width="item.width"
                        align="center"
                        sortable
                        v-for="(item, index) in propData"
                        :key="index"
                      ></el-table-column>
                      <el-table-column label="排序" align="center">
                        <template slot-scope="scope">
                          <p class="el-icon-sort sort_p" :id="scope.row.id"></p>
                        </template>
                      </el-table-column>
                    </Vuedraggable>
                  </el-table>
                </div>
              </div>
            </el-dialog>
            <!--排序套餐对话框-->
            <el-dialog
              title="套餐排序"
              :visible.sync="sortScopeClickDialogVisible"
            >
              <el-form :label-position="'right'" label-width="100px">
                <el-form-item label="套餐名称">
                  <el-input
                    v-if="clusInfo.clusName == ''"
                    placeholder="按原有套餐名称复制"
                    size="small"
                    style="width: 80%"
                    :disabled="true"
                  ></el-input>
                  <el-input
                    v-else
                    v-model="clusInfo.clusName"
                    placeholder="请输入套餐名称"
                    size="small"
                    style="width: 80%"
                  ></el-input>
                </el-form-item>
                <el-form-item label="状态">
                  <div class="getSeleLnc">
                    <el-select v-model="clusInfo.state" size="small">
                      <el-option label="启用" value="T"></el-option>
                      <el-option label="禁用" value="F"></el-option>
                    </el-select>
                  </div>
                </el-form-item>
                <el-form-item label="排序">
                  <el-input
                    v-model="clusInfo.sort"
                    placeholder="请输入序号"
                    size="small"
                    style="width: 80%"
                  ></el-input>
                </el-form-item>
              </el-form>
              <div slot="footer">
                <el-button
                  type="danger"
                  size="small"
                  @click="sortScopeClickDialogVisible = false"
                  >取消</el-button
                >
                <el-button
                  type="primary"
                  size="small"
                  @click="sortConfirmClus()"
                  >确定</el-button
                >
              </div>
            </el-dialog>
            <!--导入提示对话框-->
            <el-dialog title="提示" :visible.sync="TipsDialogVisible">
              <!-- <div style="height: 100px;"> -->
              <el-form
                :inline="true"
                :label-position="'right'"
                label-width="100px"
              >
                <el-form-item>
                  <div
                    style="
                      margin: auto 53px;
                      font-size: 100px;
                      line-height: 100px;
                    "
                  >
                    <el-icon
                      style="color: #18ab76"
                      class="el-icon-circle-check"
                    ></el-icon>
                  </div>
                </el-form-item>
                <el-form-item>
                  <div class="TipsContainer">
                    <div>
                      成功导入：<span>{{ TipsResult.success }}</span
                      >条<el-button
                        type="text"
                        @click="closeTipsDialogVisible('success')"
                        >查看</el-button
                      >
                    </div>
                    <div v-if="TipsResult.warning != '0'">
                      导入异常：<span>{{ TipsResult.warning }}</span
                      >条<el-button
                        type="text"
                        @click="closeTipsDialogVisible('warning')"
                        >查看</el-button
                      >
                    </div>
                    <!-- <div v-if="TipsResult.warning!='0'">导入失败：<span>{{TipsResult.error }}</span>条<el-button type="text" @click="closeTipsDialogVisible()">查看</el-button></div> -->
                  </div>
                </el-form-item>
              </el-form>
              <!-- </div> -->
              <!-- <div slot="footer">
                <el-button
                  type="danger"
                  size="small"
                  @click="sortScopeClickDialogVisible = false"
                  >取消</el-button
                >
                <el-button type="primary" size="small" @click="sortConfirmClus()"
                  >确定</el-button
                >
              </div> -->
            </el-dialog>
          </div>
        </div>
      </div>
    </div>
    <!-- 套餐编辑 -->
    <EditClus v-if="editFlag" :lnc_Code.sync="lnc_Code" ref="editClus_ref" />
    <contactPerson
      v-if="editCP"
      :lnc_Code.sync="lnc_Code"
      ref="contactPerson_ref"
    />
  </div>
</template>

<script>
import contactPerson from "../TeamExamination/contactPerson.vue";
import { ajax } from "../../common/ajax";
import apiUrls from "../../config/apiUrls";
import axios from "axios";
import { storage, dataUtils, toolsUtils } from "@/common";
import EditClus from "./editClus.vue";
import Vuedraggable from "vuedraggable";
import Vue from "vue";
export default {
  name: "BookTj",
  components: {
    EditClus,
    Vuedraggable,
    contactPerson,
  },
  data() {
    return {
      comboActive: "unitCombo",
      searchVal: "",
      fixed_unitComboList: [], //单位套餐列表所有数据
      unitComboList: [], //搜索后的单位套餐列表
      fixed_officialComboList: [], //官方套餐列表所有数据
      officialComboList: [], //搜索后的官方套餐列表
      editFlag: false,
      editCP: false,
      checkClus: {},
      enabled: false,
      dragging: false,
      moveCheck: {},
      indexHost: Vue.prototype.baseData.indexHost,
      apiHost: Vue.prototype.baseData.apiHost,
      overallLoading: "",
      drawer: false, //切换号源抽屉
      lnc_Name: "所有单位", //号源值
      drawerIpnut: "", //单位编码名称
      hoverIndex: -1, //表示当前hover的是第几个div 初始为 -1 或 null 不能为0 0表示第一个div
      sourceValue: "总号源",
      everyWidth: "width:calc( 100% / 16)",
      lnc_Code: "",
      drawerData: [], //单位列表
      LncList: [],
      //团检名单模型
      TeamList: {
        id: 0,
        name: "",
        birthday: "",
        sex: "",
        age: "",
        tel: "",
        matrimony: "",
        idCard: "",
        remark: "",
        nation: "",
        employeeId: "",
        department: "",
        group: "",
        position: "",
        address: "",
        id_type: "",
        profession: "",
        InjuryDuration: "",
        seniority: "",
        classification: "",
        factors: "",
        doctor: "",
        expense: "",
        medical: "",
      },
      ids: "", //id集合 用于批量删除或单个删除
      NameList: "",
      height: "calc( 100vh - 300px)",
      dialogHeight: "calc( 100vh - 450px)",
      AtndialogHeight: "calc( 100vh - 750px)",
      tableData: [], //表数据
      tableCopyTableList: [], //表数据集合
      tableConstData: [], //存放数据 用于筛选数据
      index: 1, //当前页数
      size: 500, //页码
      dialogVisible: false,
      dialogVisibleOne: false,
      DYYdialogVisible: false,
      dialogTitle: "", //对话框的标题
      dialogTitleOne: "",
      DYYDialogTitle: "代预约",
      loading: false,
      uploadLoading: false,
      fileList: [],
      activeName: "first",
      waitNumber: 0,
      TeamState: "0",
      uploadUrl: "",
      importTableData: [],
      groupName: "",
      radio: "1",
      grade: "",
      rules: {
        name: [{ required: true, message: "请输入姓名", trigger: "blur" }],
        tel: [{ required: true, message: "请输入手机号", trigger: "blur" }],
        matrimony: [
          { required: true, message: "请选择婚姻状况", trigger: "change" },
        ],
        idCard: [
          { required: true, message: "请输入身份证号", trigger: "blur" },
        ],
        birthday: [
          { required: true, message: "请选择出生日期", trigger: "blur" },
        ],
        age: [{ required: true, message: "请输入年龄", trigger: "blur" }],
        sex: [{ required: true, message: "请选择性别", trigger: "blur" }],
      },
      UserName: "",
      AtnDialogVisible: false,
      showAtnDialogTitle: false,
      AtnDialogTitle: "",
      activeClus: "firstClus",
      clusIds: "",
      tableAllClusData: [],
      tableClusData: [],
      tableClusDatas: [],
      effectiveDate: "",
      formLabelWidth: "240",
      SMSnounIds: [],
      SMSContent: "",
      SMSParameter: "",
      SMSPreview: "",
      SMSTemplateList: {
        id: "",
        sms_Code: "",
        sms_Name: "",
        sms_Content: "",
        Preview: "",
        nounId: "",
        state: "",
        parameter: "",
        updateTime: "",
      },
      tableSMSConstData: [], //存放消息模板
      SMSNounList: [],
      selectedDay: 5,
      timeValue: "09:00:00",
      pickerOptions: {
        start: "00:00",
        end: "23:59",
        step: "00:15",
        format: "HH:mm",
      },
      expireTime: "",
      stateShow: false,
      ClassKey: "", //点击选择日期
      years: "", //年
      months: "", //月
      pre: false, //上一月
      pre_: true, //上一月代替
      week: 0, //周几
      current: {}, //当前时间
      calendarList: [], //用于遍历显示
      shareDate: new Date(), //享元模式，用来做优化的,
      weekList: ["日", "一", "二", "三", "四", "五", "六"], // 新增
      CardData: [
        {
          CardText: "入职套餐（不限性别）",
        },
      ],
      sumtimeList: [], //时段数据
      TeamSpans: "", //样式判断
      sumtime_Code: "", //时段编码
      sumtime_Name: "", //时段名称
      searchClus_Name: "",
      CurrentClus: [],
      teamName: "",
      ValidityPeriod: false,
      columns: [
        "汉族",
        "蒙古族",
        "回族",
        "藏族",
        "维吾尔族",
        "苗族",
        "彝族",
        "壮族",
        "布依族",
        "朝鲜族",
        "满族",
        "侗族",
        "瑶族",
        "白族",
        "土家族",
        "哈尼族",
        "哈萨克族",
        "傣族",
        "黎族",
        "傈僳族",
        "佤族",
        "畲族",
        "高山族",
        "拉祜族",
        "水族",
        "东乡族",
        "纳西族",
        "景颇族",
        "柯尔克孜族",
        "土族",
        "达斡尔族",
        "仫佬族",
        "羌族",
        "布朗族",
        "撒拉族",
        "毛难族",
        "仡佬族",
        "锡伯族",
        "阿昌族",
        "普米族",
        "塔吉克族",
        "怒族",
        "乌孜别克族",
        "俄罗斯族",
        "鄂温克族",
        "德昂族",
        "保安族",
        "裕固族",
        "京族",
        "塔塔尔族",
        "独龙族",
        "鄂伦春族",
        "赫哲族",
        "门巴族",
        "珞巴族",
        "基诺族",
      ],
      IdTypecolumns: [
        { value: "01", text: "居民身份证" },
        { value: "02", text: "居民户口簿" },
        { value: "03", text: "护照" },
        { value: "04", text: "军官证" },
        { value: "05", text: "驾驶证" },
        { value: "06", text: "港澳居民来往内地通行证" },
        { value: "07", text: "台湾居民来往内地通行证" },
        { value: "99", text: "其他法定有效证件" },
        { value: "109", text: "深圳市居民健康卡" },
        { value: "110", text: "健康档案" },
        { value: "111", text: "出生医学证明" },
        { value: "112", text: "深圳市社会保障卡电脑号" },
      ],
      // Validitychecked:true,
      CYchecked: false,
      copyClickDialogVisible: false,
      sortClickDialogVisible: false,
      //团检名单模型
      clusInfo: {
        id: "",
        clusName: "",
        lnc_Code: "",
        state: "",
        sort: "",
      },
      SMSTemplateName: "",
      SMSdialogVisible: false,
      userName: "",
      AllClusdialogVisible: false,
      CombName: "",
      settingComboList: [],
      templateRadio: "2",
      sortScopeClickDialogVisible: false,
      propData: [
        //width  有需要就加，其余的自适应
        {
          propValue: "name",
          label: "序号",
        },
        {
          propValue: "clus_Code",
          label: "套餐编码",
        },
        {
          propValue: "clus_Name",
          label: "套餐名称",
        },
        {
          propValue: "price",
          label: "价格",
        },
        {
          propValue: "state",
          label: "状态",
        },
      ],
      allKeyWords: [],
      keyWordsJson: [],
      lastKeyCode: 0,
      TipsDialogVisible: false,
      TipsResult: {
        success: "",
        warning: "",
        error: "",
      },
      smsChecked: false,
      effeChecked: false,
      groupCheckList: [],
      filtertext: "",
      sexCheckList: [],
      sexFilters: [
        {
          text: "男",
          value: "男",
        },
        {
          text: "女",
          value: "女",
        },
      ],
      addPackageChecked: false,
      addPackageData: [],
      addPackageCodes: [],
      addPackageDatas: [],
    };
  },
  created() {
    this.loadBtn();
    var lncList = JSON.parse(storage.session.get("lncList"));
    if (lncList == null) {
      this.drawer = true;
    } else {
      this.lnc_Code = lncList.lnc_Code;
      this.lnc_Name = lncList.lnc_Name;
      this.GetTeamList();
      this.GetAddPackage();
    }

    // this.uploadUrl = this.apiHost + "/Home/UpPicture";
    this.GetClusList();
    this.GetAllSMSTemplateList();
  },
  computed: {
    // 显示当前时间
    currentDateStr() {
      let { year, month } = this.current;
      return `${year}-${this.pad(month + 1)}`;
    },
    filters() {
      // 使用 Set 来删除重复项
      let uniqueData = [...new Set(this.tableConstData)];
      let grouped = uniqueData.reduce((result, item) => {
        let key = item.group;
        result[key] = result[key] || [];
        result[key].push(item);
        return result;
      }, {});
      let filterData = [];
      Object.keys(grouped).forEach((key) => {
        let filter = {};
        filter.text = key;
        filter.value = key;
        filterData.push(filter);
        // 执行其他操作，例如将filters对象传递给其他函数或更新DOM元素等
      });
      let grouplist = [];
      grouplist = filterData.filter((item) => {
        return !this.filtertext || item.text.includes(this.filtertext);
      });
      // this.filters = filterData;
      return grouplist;
      // return filterData;
    },
  },
  // mounted() {
  //   this.init();
  // },
  methods: {
    loadBtn() {
      var pData = {
        kw: this.drawerIpnut,
      };
      ajax
        .post(apiUrls.GetGetlncmenu, pData, { nocrypt: true })
        .then((r) => {
          if (!r.data.success) {
            this.$message.error(r.data.returnMsg);
            return;
          }
          this.drawerData = r.data.returnData;
        })
        .catch((err) => {
          this.$message.error("获取单位失败,请联系管理员");
        });
    },

    querys() {
      this.loadBtn();
    },
    // 点击选择单位
    drawerBtn(index) {
      this.lnc_Name = this.drawerData[index].lnc_Name;
      this.lnc_Code = this.drawerData[index].lnc_Code;
      storage.session.set("lncList", JSON.stringify(this.drawerData[index]));

      this.drawer = false;
      this.init();
      this.GetTeamList();
      this.GetClusList();
      this.GetAddPackage();
    },
    handleClick(tab, event) {
      // console.log(tab.index);
      this.TeamState = tab.index;
      this.GetTeamList();
      // this.getGroup();
    },
    //全选
    toggleSelection(rows) {
      console.log(rows);
      if (rows) {
        // rows.forEach(row => {
        //   this.$refs.tableData.toggleRowSelection(row);
        // });
        this.$refs.tableData.toggleAllSelection(); //全选
      } else {
        this.$refs.tableData.clearSelection();
      }
    },
    //获取选中行id
    handleSelectionChangePeople(rows) {
      this.ids = rows.map((row) => row.id);
      let NameList = rows.map((row) => row.name);
      this.NameList = NameList.join(",");
    },
    handleSelectionChangeids(rows) {
      this.clusIds = rows.map((row) => row.id);
    },
    //显示模态框
    showAddorEditDialog(row) {
      if (this.lnc_Code == "") {
        this.$message.warning("请输入单位编码");
        return;
      }
      if (row == undefined) {
        this.dialogTitleOne = "添加单个客户>单位名称:" + this.lnc_Name;
      } else {
        this.dialogTitleOne = "查看/编辑客户>单位名称:" + this.lnc_Name;
      }
      this.TeamList.id = row ? row.id : "";
      this.TeamList.name = row ? row.name : "";
      this.TeamList.sex = row ? row.sex : "";
      this.TeamList.age = row ? row.age : "";
      this.TeamList.idCard = row ? row.idCard : "";
      this.TeamList.matrimony = row ? row.matrimony : "";
      this.TeamList.nation = row ? row.nation : "汉族";
      this.TeamList.birthday = row ? row.birthday : "";
      this.TeamList.id_type = row ? row.id_type : "01";
      this.TeamList.tel = row ? row.tel : "";
      this.TeamList.department = row ? row.department : "";
      this.TeamList.group = row ? row.group : "";
      this.dialogVisibleOne = true; //成功后关闭对话框
    },
    // 页数改变事件
    handleSizeChange(size) {
      this.size = size;
      this.tableData = this.paging(size, this.index);
    },
    // 本地分页的方法
    paging(size, current) {
      const tableList = JSON.parse(JSON.stringify(this.tableCopyTableList));
      const tablePush = [];
      tableList.forEach((item, index) => {
        if (size * (current - 1) <= index && index <= size * current - 1) {
          tablePush.push(item);
        }
      });
      return tablePush;
    },
    //获取信息
    GetTeamList() {
      var that = this;
      that.loading = true;
      // if (this.lnc_Code=="") {
      //   that.loading = false;
      //   this.$message.error("未识别到单位编码");
      //   return;
      // }
      var pData = {
        lnc_Code: this.lnc_Code,
        status: this.TeamState,
      };
      ajax
        .post(apiUrls.GetTeamList, pData, { nocrypt: true })
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }

          // 初始化数据
          let returnArray = r.data.returnData.team;
          let data = returnArray.map((val) => {
            switch (val.state) {
              case 0:
                val.state = "未操作";
                break;
              case 1:
                val.state = "未使用";
                break;
              case 2:
                val.state = "异常";
                break;
              case 3:
                val.state = "已使用";
                break;
              case 4:
                val.state = "已过期";
                break;
              default:
                break;
            }
            switch (val.periodOfValidity) {
              case "至":
                val.periodOfValidity = "/";
                break;
              default:
                break;
            }
            return val;
          });
          that.tableConstData = data;
          that.tableCopyTableList = data;
          that.tableData = data;
          // this.getGroup();
          that.waitNumber = r.data.returnData.errorNum;
          this.$refs.tableData.clearSelection();
          this.groupCheckList = [];
          that.loading = false;
        })
        .catch((err) => {
          alert("获取单位失败,请稍后重试");
        });
    },
    //获取分类
    // getGroup() {
    //   // 使用 Set 来删除重复项
    //   let uniqueData = [...new Set(this.tableData)];
    //   let grouped = uniqueData.reduce((result, item) => {
    //     let key = item.group;
    //     result[key] = result[key] || [];
    //     result[key].push(item);
    //     return result;
    //   }, {});
    //   let filterData = [];
    //   Object.keys(grouped).forEach((key) => {
    //     let filter = {};
    //     filter.text = key;
    //     filter.value = key;
    //     filterData.push(filter);
    //     // 执行其他操作，例如将filters对象传递给其他函数或更新DOM元素等
    //   });
    //   this.filters = filterData;
    //   this.$refs.tableData.clearFilter();
    // },
    // getGroup() {
    //   let grouped = this.tableData.reduce((result, item) => {
    //     let key = item.group;
    //     result[key] = result[key] || [];
    //     result[key].push(item);
    //     return result;
    //   }, {});
    //   let filters = [];
    //   Object.keys(grouped).forEach((key) => {
    //     let filter = {};
    //     filter.text = key;
    //     filter.value = key;
    //     this.filters.push(filter);
    //     // 执行其他操作，例如将filters对象传递给其他函数或更新DOM元素等
    //   });
    // },
    //筛选
    filterGroup(value, row) {
      // console.log(value, row);
      this.groupName = value;
      return row.group === value;
    },
    GetSMSTemplateList() {
      var that = this;
      that.loading = false;
      var pData = {
        data: {
          code: "1004",
        },
      };
      ajax
        .post(apiUrls.GetAllSMSTemplateByCode, pData)
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          // 初始化数据
          let tableConstData = r.data.returnData.st;
          that.SMSNounList = r.data.returnData.sn;
          this.SMSTemplateTask(tableConstData);
        })
        .catch((err) => {
          alert("获取短信模板失败,请稍后重试");
        });
    },
    showSMSTemplate() {
      this.GetSMSTemplateList();
      // this.selectSMSTemplateTask("1004");
      let idArr = this.ids;
      if (idArr.length == 0) {
        this.$message.warning("请选择客户信息");
        return;
      }
      // let tableData = this.tableData;
      // let userName = [];
      // for (let i = 0; i < tableData.length; i++) {
      //   for (let z = 0; z < idArr.length; z++) {
      //     if (tableData[i].id == idArr[z]) {
      //       userName.push(tableData[i].name);
      //     }
      //   }
      // }
      // this.userName = userName.join("、");
      this.SMSdialogVisible = true;
    },
    SendReminderMessages() {
      let idArr = this.ids;
      if (idArr.length == 0) {
        this.$message.warning("请选择客户信息");
        return;
      }
      let confirmMsg =
        "确定给（" + this.userName + "）发送短信通知吗, 是否继续?";
      this.$confirm(confirmMsg, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "error",
      }).then(() => {
        let pData = {
          data: {
            ids: idArr,
          },
          sMSTemplate: this.SMSTemplateList,
        };
        ajax
          .post(apiUrls.SendSMSMessagesByTeam, pData)
          .then((r) => {
            if (!r.data.success) {
              this.$message.error(r.data.returnMsg);
              return;
            }
            this.$message.success(r.data.returnMsg);
            this.SMSdialogVisible = false;
          })
          .catch((err) => {
            console.log(err);
            this.$message.error("系统繁忙！请稍后再试");
          });
      });
    },
    //删除单位
    DeleteTeamList(ids) {
      let idArr = [];
      if (ids) {
        idArr = [ids];
      } else {
        idArr = this.ids;
      }
      if (idArr.length == 0) {
        this.$message.warning("请选择客户信息");
        return;
      }

      this.$confirm("确定删除此客户信息吗, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "error",
      }).then(() => {
        let pData = {
          data: {
            ids: idArr,
          },
        };
        ajax
          .post(apiUrls.DeleteTeamListById, pData)
          .then((r) => {
            if (!r.data.success) {
              this.$message.error(r.returnMsg);
              return;
            }
            this.$message.success("删除成功");
            this.GetTeamList();
          })
          .catch((err) => {
            console.log(err);
            this.$message.error("系统繁忙！请稍后再试");
          });
      });
    },
    //检查输入的参数
    checkCustomer() {
      if (!this.lnc_Code) {
        this.$message.warning("未获取到单位");
        return false;
      }
      if (!this.lnc_Name) {
        this.$message.warning("未获取到单位");
        return false;
      }
      if (!this.TeamList.name) {
        this.$message.warning("请输入姓名");
        return false;
      }
      if (!this.TeamList.tel) {
        this.$message.warning("请输入手机号");
        return false;
      }
      if (dataUtils.isTel(this.TeamList.tel) != true) {
        this.$message.warning(dataUtils.isTel(this.TeamList.tel));
        return;
      }
      if (!this.TeamList.id_type) {
        this.$message.warning("请选择证件类型");
        return false;
      }
      if (!this.TeamList.idCard) {
        this.$message.warning("请输入证件号");
        return false;
      }
      if (this.TeamList.id_type != "01") {
        if (this.TeamList.age == "") {
          this.$message.warning("请输入年龄");
          return;
        }
        if (this.TeamList.sex == "") {
          this.$message.warning("请选择性别");
          return;
        }
        if (this.TeamList.birthday == "") {
          this.$message.warning("请选择出生日期");
          return;
        }
      }
      if (
        dataUtils.isCardID(this.TeamList.idCard) != true &&
        this.TeamList.id_type == "01"
      ) {
        this.$message.warning(dataUtils.isCardID(this.TeamList.idCard));
        return;
      }
      if (!this.TeamList.matrimony) {
        this.$message.warning("请选择婚姻状况");
        return false;
      }
      return true;
    },
    addSingleCustomer() {
      //参数验证
      if (!this.checkCustomer()) {
        return;
      }
      var addOrEdit = ""; //请求变量 新增/编辑 api地址不一样
      if (this.TeamList.id) {
        addOrEdit = apiUrls.updateSingleCustomer;
      } else {
        this.TeamList.id = "0";
        addOrEdit = apiUrls.addSingleCustomer;
      }
      let pData = {
        // code: this.radio,
        name: this.TeamList.name,
        id: this.TeamList.id,
        sex: this.TeamList.sex,
        age: this.TeamList.age,
        tel: this.TeamList.tel,
        matrimony: this.TeamList.matrimony,
        idCard: this.TeamList.idCard,
        remark: this.TeamList.remark,
        nation: this.TeamList.nation,
        employeeId: this.TeamList.employeeId,
        department: this.TeamList.department,
        group: this.TeamList.group,
        position: this.TeamList.position,
        id_type: this.TeamList.id_type,
        address: this.TeamList.address,
        birthday: this.TeamList.birthday,
        profession: this.TeamList.profession,
        InjuryDuration: this.TeamList.InjuryDuration,
        seniority: this.TeamList.seniority,
        classification: this.TeamList.classification,
        factors: this.TeamList.factors,
        doctor: this.TeamList.doctor,
        expense: this.TeamList.expense,
        medical: this.TeamList.medical,
        lnc_Code: this.lnc_Code,
        lnc_Name: this.lnc_Name,
      };
      ajax
        .post(addOrEdit, pData, { nocrypt: true })
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          this.$message.success("操作成功");
          this.dialogVisibleOne = false; //成功后关闭对话框
          this.GetTeamList(); //重新加载
        })
        .catch((err) => {
          console.log(err);
          this.$message.error("系统繁忙！请稍后再试");
        });
    },
    //检查输入的参数
    checkAdminInfo() {
      if (!this.lnc_Code) {
        this.$message.warning("未获取到单位");
        return false;
      }
      if (!this.lnc_Name) {
        this.$message.warning("未获取到单位");
        return false;
      }
      if (this.importTableData.length <= 0) {
        this.$message.warning("客户名单为空");
        return false;
      }
      return true;
    },
    //导入名单
    addTeamList() {
      //参数验证
      if (!this.checkAdminInfo()) {
        return;
      }
      this.overallLoading = this.$loading({
        lock: true,
        text: "正在导入中",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      let pData = {
        team: {
          teamList: this.importTableData,
        },
      };
      ajax
        .post(apiUrls.AddTeamList, pData)
        .then((r) => {
          this.TipsResult = r.data.returnData;
          this.overallLoading.close();
          this.TipsDialogVisible = true;
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          this.$message.success("操作成功");
          this.dialogVisible = false; //成功后关闭对话框
          this.GetTeamList(); //重新加载
        })
        .catch((err) => {
          console.log(err);
          this.$message.error("系统繁忙！请稍后再试");
        });
    },
    //关闭提示对话框
    closeTipsDialogVisible(Tips) {
      if (Tips == "warning") {
        this.activeName = "third";
        this.TipsDialogVisible = false;
        return;
      }
      this.activeName = "first";
      this.TipsDialogVisible = false;
    },

    //表格筛选
    GetNewData() {
      this.tableCopyTableList = this.tableConstData.filter((item) => {
        //筛选
        // if(val){
        return (
          !this.UserName ||
          item.name.includes(this.UserName) ||
          item.idCard.includes(this.UserName) ||
          item.tel.includes(this.UserName)
        );
        // }else{
        //   return !this.UserName || item.name.includes(this.UserName);
        // }
      });
      this.tableData = this.tableCopyTableList;
    },
    // 上传文件之前的钩子
    beforeUploadFile(file) {
      // 检查文件类型是否为Excel文件
      // const isExcel = /^\[MSExcel.xlsx]/i.test(file.type);
      // if (!isExcel) {
      //   this.$message.error('只能上传Excel文件');
      //   return false; // 停止上传
      // }
      // 添加到文件列表中
      this.fileList.push(file);
      return true; // 继续上传
    },
    // 上传文件的请求
    upload() {
      this.uploadLoading = true;
      this.overallLoading = this.$loading({
        lock: true,
        text: "正在导入中",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      // let url = "https://localhost:5001" + "/Home/UpPicture";
      const formData = new FormData();
      formData.append("file", this.fileList[0]); // 将第一个Excel文件添加到请求中
      formData.append("lnc_Code", this.lnc_Code);
      formData.append("lnc_Name", this.lnc_Name);
      return axios.post(this.apiHost + "/Home/UpPicture", formData); // 发送POST请求到服务器进行上传
    },
    // 处理上传成功的回调函数
    handleUploadSuccess(r, file) {
      this.overallLoading.close();
      if (!r.data.success) {
        alert(r.data.returnMsg);
        this.uploadLoading = false;
        return;
      }
      this.importTableData = r.data.returnData;
      this.uploadLoading = false;
      this.$message.success("导入成功");
      // this.fileList = [];
      // 处理上传成功后的逻辑，例如更新UI等操作
    },
    handleButton1Click() {
      // 处理按钮1的点击事件
      if (this.lnc_Code == "") {
        this.$message.warning("请输入单位编码");
        return;
      }
      this.dialogTitleOne = "添加单个客户>单位名称:" + this.lnc_Name;
      this.dialogVisibleOne = true;
    },
    handleButton2Click() {
      if (this.lnc_Code == "") {
        this.$message.warning("请输入单位编码");
        return;
      }
      this.fileList = [];
      this.importTableData = [];
      this.dialogTitle = "批量导入客户>单位名称:" + this.lnc_Name;
      this.dialogVisible = true;
      // 处理按钮2的点击事件
    },
    handleRemove(file, fileList) {
      this.fileList = [];
    },
    handlePreview(file) {
      // console.log(file);
    },
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${
          files.length + fileList.length
        } 个文件`
      );
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`);
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    AtnTeamList() {
      let idArr = this.ids;
      if (idArr.length == 0) {
        this.$message.warning("请选择客户信息");
        return;
      }
      this.selectSMSTemplateTask("1004");
      this.AtnDialogTitle = "激活客户名单";
      this.effectiveDate = "";
      // this.GetAllSMSTemplateList();
      this.addPackageChecked = false;
      this.addPackageCodes = [];
      this.AtnDialogVisible = true;
      this.$nextTick(() => {
        if (this.checkClus.id) {
          var clus = this.tableClusData.filter((item) => {
            return this.checkClus.id == item.id;
          });
          // console.log(clus);
          this.$refs.tableClusData.toggleRowSelection(clus[0]);
          this.clusIds = [this.checkClus.id];
          this.$refs.addPackageData.clearSelection();
          // console.log(this.clusIds);
        }
      });
    },
    showAtnTeaam() {
      if (this.clusIds.length == 0) {
        this.$message.warning("请选择套餐");
        return;
      }
      if (!this.VerifyParameters()) {
        return;
      }

      this.screenClusDataById(this.clusIds);
      this.showAtnDialogTitle = true;
    },
    cancelAtnDialogVisible() {
      this.$refs.tableClusData.clearSelection();
      this.clusIds = [];

      // this.$refs.multipleTable.clearSelection();
      this.AtnDialogVisible = false;
    },
    VerifyParameters() {
      if (this.effectiveDate.length == 0) {
        this.$message.warning("请选择有效期");
        return false;
      }
      let date = new Date(this.effectiveDate[1]);
      date.setDate(date.getDate() - this.selectedDay);
      var year = date.getFullYear();
      var month = (date.getMonth() + 1).toString().padStart(2, "0");
      var day = date.getDate().toString().padStart(2, "0");

      var formattedDate = `${year}-${month}-${day}`;
      this.expireTime = formattedDate + " " + this.timeValue;
      console.log(this.expireTime);
      return true;
    },
    getParameters() {},
    verifyActivationTeam() {
      if (!this.lnc_Code) {
        this.$message.warning("未获取到单位");
        return false;
      }
      if (!this.SMSTemplateList.preview) {
        this.$message.warning("未获取到消息模板");
        return false;
      }
      if (!this.SMSTemplateList.parameter) {
        this.$message.warning("未获取到消息模板");
        return false;
      }
      if (this.effectiveDate.length != 2) {
        this.$message.warning("未获取有效期");
        return false;
      }
      if (!this.expireTime) {
        this.$message.warning("未获取到过期时间");
        return false;
      }
      if (this.clusIds.length <= 0) {
        this.$message.warning("未获取到套餐信息");
        return false;
      }
      return true;
    },
    activationTeaam() {
      var that = this;
      //参数验证
      if (!this.verifyActivationTeam()) {
        return;
      }
      // that.loading = false;
      let user = JSON.parse(storage.session.get("user"));
      var pData = {
        data: {
          ids: this.ids,
          code: this.smsChecked,
        },
        teamList: {
          startDate: this.effectiveDate[0],
          endDate: this.effectiveDate[1],
          expireTime: this.expireTime,
          clusIds: this.clusIds.join(","),
          addPackageCodes: this.addPackageCodes.join(","),
          lnc_Code: this.lnc_Code,
          group: this.groupName,
          Operator: user.admin_Name,
        },
        sMSTemplate: this.SMSTemplateList,
      };
      // console.log(pData);
      // return
      ajax.post(apiUrls.activationTeaamList, pData).then((r) => {
        if (!r.data.success) {
          alert(r.data.returnMsg);
          return;
        }
        this.$message.success(r.data.returnMsg);
        this.GetTeamList();
        this.showAtnDialogTitle = false;
        this.$refs.tableClusData.clearSelection();
        this.AtnDialogVisible = false;
      });
    },
    handleClusClick() {
      if (this.activeClus == "firstClus") {
        this.screenClusData(this.lnc_Code);
        return;
      }
      if (this.activeClus == "secondClus") {
        this.screenClusData("person");
        return;
      }
      if (this.activeClus == "thirdClus") {
        this.tableClusData = this.tableAllClusData;
        return;
      }
    },
    //获取所有套餐信息
    GetClusList() {
      var that = this;
      that.loading = false;
      ajax
        .post(apiUrls.GetAllClusList)
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          // console.log(r.data.returnData);
          // 初始化数据

          let tableCopyTableList = r.data.returnData.map((val) => {
            if (val.errorMsg) {
              val.showFlag = true;
            }
            switch (val.clus_sex) {
              case "1":
                val.clus_sex = "男";
                break;
              case "0":
                val.clus_sex = "女";
                break;
              case "%":
                val.clus_sex = "不限";
                break;
              default:
                break;
            }
            return val;
          });
          that.tableAllClusData = tableCopyTableList;
          // console.log(tableCopyTableList);
          this.screenClusData(this.lnc_Code);
          this.filterCombo();
        })
        .catch((err) => {
          alert("获取套餐失败,请稍后重试");
        });
    },
    screenClusData(code) {
      var that = this;
      if (code == "person") {
        that.tableClusData = that.tableAllClusData.filter((item) => {
          //筛选
          return (
            item.lnc_Code == null ||
            item.lnc_Code.includes("0451") ||
            item.clusType != null
          );
        });
        return;
      }
      that.tableClusData = that.tableAllClusData.filter((item) => {
        //筛选
        return !code || item.lnc_Code.includes(code);
      });
      // console.log(that.tableClusData);
      // this.tableData = this.tableCopyTableList;
    },
    screenClusDataById(ids) {
      var that = this;
      that.tableClusDatas = that.tableAllClusData.filter((item) => {
        return that.clusIds.includes(item.id);
      });
      that.addPackageDatas = that.addPackageData.filter((item) => {
        return that.addPackageCodes.includes(item.item_Code);
      });
      // this.tableData = this.tableCopyTableList;
    },
    //短信模板
    GetAllSMSTemplateList() {
      var that = this;
      that.loading = false;
      var pData = {
        code: "team",
      };
      ajax
        .post(apiUrls.GetAllSMSTemplate, pData, { nocrypt: true })
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          // 初始化数据
          // 初始化数据
          // that.tableSMSConstData = r.data.returnData.st;
          // console.log(r.data.returnData.st);
          that.tableSMSConstData = r.data.returnData.st
            ? r.data.returnData.st.filter((item) => {
                return !item.lnc_Code;
              })
            : [];
          that.smsAlltableData = r.data.returnData.st;

          that.SMSNounList = r.data.returnData.sn;
          // this.SMSTemplateTask(that.tableConstData[this.num], this.num);
          this.selectSMSTemplateTask("1004");
        })
        .catch((err) => {
          alert("获取短信模板失败,请稍后重试");
        });
    },
    selectLastSMSTemplateTask() {
      const index = this.tableSMSConstData.findIndex(
        (item) => item.appid === "success" && item.lnc_Code === this.lnc_Code
      );
      if (index < 0) {
        this.selectSMSTemplateTask("1004");
        return;
      }
      // console.log(this.tableSMSConstData);
      this.SMSTemplateTask(this.tableSMSConstData[index], index);
    },
    selectSMSTemplateTask(val) {
      const index = this.tableSMSConstData.findIndex(
        (item) => item.sms_Code === val
      );
      // console.log(this.tableSMSConstData);
      this.SMSTemplateTask(this.tableSMSConstData[index], index);
    },
    SMSTemplateTask(SMSTemplate, index) {
      // this.num = index;
      // if (SMSTemplate.state == "T") {
      //   this.switchValue = true;
      // } else {
      //   this.switchValue = false;
      // }
      // console.log(SMSTemplate,index);
      this.SMSTemplateList = SMSTemplate;
      this.SMSPreview = "";
      this.SMSParameter = SMSTemplate.parameter;
      var nounId = SMSTemplate.nounId.split(",");
      var SMSnounIds = [];
      for (let i = 0; i < nounId.length; i++) {
        for (let z = 0; z < this.SMSNounList.length; z++) {
          if (nounId[i] == this.SMSNounList[z].code) {
            SMSnounIds.push(this.SMSNounList[z]);
          }
        }
      }
      // console.log(SMSnounIds);
      this.SMSTemplateName = SMSTemplate.sms_Name;
      this.SMSnounIds = SMSnounIds;
      this.SMSPreview = this.formatString(
        SMSTemplate.preview,
        SMSTemplate.parameter
      );
      this.SMSContent = this.formatContent(
        this.SMSTemplateList.preview,
        this.SMSTemplateList.parameter
      );
    },
    formatString(formatted, parameter) {
      if (parameter) {
        var parameterList = parameter.split(",");
        var args = [];
        for (let i = 0; i < parameterList.length; i++) {
          for (let z = 0; z < this.SMSNounList.length; z++) {
            if (parameterList[i] == this.SMSNounList[z].code) {
              args.push(
                "<span id=" +
                  this.SMSNounList[z].code +
                  " style='color: blue;'>" +
                  this.SMSNounList[z].smSdefault +
                  "</span>"
              );
            }
          }
        }
        for (let i = 0; i < args.length; i++) {
          let regexp = new RegExp("\\{" + i + "\\}", "gi");
          formatted = formatted.replace(regexp, args[i]);
        }
      }
      return formatted;
    },
    formatContent(formatted, parameter) {
      if (parameter) {
        var parameterList = parameter.split(",");
        var args = [];
        // var spanElement = [];
        for (let i = 0; i < parameterList.length; i++) {
          for (let z = 0; z < this.SMSNounList.length; z++) {
            if (parameterList[i] == this.SMSNounList[z].code) {
              args.push("【" + this.SMSNounList[z].name + "】");
              // spanElement.push(this.SMSNounList[z].code +i);
            }
          }
        }
        for (let i = 0; i < args.length; i++) {
          let regexp = new RegExp("\\{" + i + "\\}", "gi");
          formatted = formatted.replace(regexp, args[i]);
        }
      }

      // this.spanElementList=spanElement.join(",");
      return formatted;
    },
    handleTextareaClick() {
      var obj = document.querySelector("textarea");
      this.cursorIndex = this.getFocus(obj);
      this.dealFocusExtend(obj, this.cursorIndex);
    },
    handleKeyup(e) {
      //每次在文本域中输入的时候都要获取其光标位置，以便于其他操作
      var obj = document.querySelector("textarea");
      this.cursorIndex = this.getFocus(obj);

      //由于我们是禁止输入中文中括号的，而中文中括号输入左右情况不同，需要分别处理
      // console.log(e.keyCode);
      if (e.keyCode == 219) {
        e.preventDefault();
        //这里获取到光标左侧的内容
        var leftChar = obj.value.slice(this.cursorIndex - 1, this.cursorIndex);

        //只有输入结束的是右中括号，而且它的前一个字符是左中括号才把它删除，防止把关键字删除掉
        if (
          /\】/g.test(leftChar) &&
          obj.value.charAt(this.cursorIndex - 2) === "【"
        ) {
          obj.value =
            obj.value.slice(0, this.cursorIndex - 2) +
            obj.value.slice(this.cursorIndex, obj.value.length);
        }
      } else if (e.keyCode == 221) {
        e.preventDefault();
        //右中括号就好办多了，因为它不会自动带出左中括号
        var leftChar = obj.value.slice(this.cursorIndex - 1, this.cursorIndex);
        if (/\】/g.test(leftChar)) {
          obj.value =
            obj.value.slice(0, this.cursorIndex - 1) +
            obj.value.slice(this.cursorIndex, obj.value.length);
        }
      }
      //防止上下左右键移动光标进入关键字中
      if (
        (e.keyCode == 37 ||
          e.keyCode == 39 ||
          e.keyCode == 38 ||
          e.keyCode == 40) &&
        this.lastKeyCode !== 219
      ) {
        dealFocusMove(obj, this.cursorIndex);
      } else if (e.keyCode == 8) {
        //backspace删除的时候删除整个关键字
        // console.log(obj, this.cursorIndex, this.allKeyWords);
        this.dealFocusL(obj, this.cursorIndex, this.allKeyWords);
      } else if (e.keyCode == 46) {
        //delete删除的时候也是删除整个关键字
        this.dealFocusR(obj, this.cursorIndex, this.allKeyWords);
      }
      if (e.keyCode !== 37 && e.keyCode !== 39) {
        //这里防止手动按得左右键影响左中括号判断
        this.lastKeyCode = e.keyCode;
      }
      this.updateSMSPreview();
    },
    handleKeydown(e) {
      if (e.keyCode == 221 || e.keyCode == 219) {
        e.preventDefault();
      }
      if (
        (e.keyCode == 37 || e.keyCode == 39) &&
        this.this.lastKeyCode === 219
      ) {
        e.preventDefault();
      }
    },

    // initializeKeyWordsJson() {
    //   const newData = this.data1.concat(this.data2).concat(this.data3);
    //   for (let i = 0; i < newData.length; i++) {
    //     if (this.keyWordsJson[newData[i].name] !== null) {
    //       this.keyWordsJson[newData[i].name] = newData[i].id;
    //     }
    //   }
    // },
    // getFocus() {
    //   return this.$refs.textarea.selectionStart;
    // },
    //处理删除关键字
    dealFocusL(obj, index, allKeyWords) {
      var text = obj.value.slice(0, index);
      var resL,
        resR,
        i = 0,
        j = 0;
      var lastIndex = 0;
      var regL = /\【/g;
      var regR = /\】/g;
      while ((resL = regL.exec(text))) {
        i++;
        //获取左中括号位置
        lastIndex = regL.lastIndex;
      }
      while ((resR = regR.exec(text))) {
        j++;
      }
      if (i != j) {
        var textAll = obj.value;
        obj.value =
          textAll.substring(0, lastIndex - 1) +
          textAll.substring(index, textAll.length);
        this.SMSContent = obj.value;
        // console.log(obj.value);
        // allKeyWords.splice(i - 1, 1);
        obj.setSelectionRange(lastIndex - 1, lastIndex - 1);
        // 移动光标后触发点击事件，使光标闪烁，保持焦点
        obj.click();
      }
    },
    //delete关键字
    dealFocusR(obj, index, allKeyWords) {
      var text = obj.value.slice(index, obj.value.length);
      text = text.split("").reverse().join("");
      var resL,
        resR,
        i = 0,
        j = 0;
      var lastIndex = 0;
      var regL = /\【/g;
      var regR = /\】/g;
      while ((resL = regL.exec(text))) {
        i++;
      }
      while ((resR = regR.exec(text))) {
        j++;
        lastIndex = regR.lastIndex;
      }
      if (i != j) {
        //获取右中括号位置
        var textAll = obj.value;
        lastIndex = index + text.length - lastIndex + 1;
        allKeyWords.splice(j - 1, 1);
        obj.value =
          textAll.substring(0, index) +
          textAll.substring(lastIndex, textAll.length);
        this.SMSContent = obj.value;
        obj.setSelectionRange(index, index);
        // 移动光标后触发点击事件，使光标闪烁，保持焦点
        obj.click();
      }
    },
    //处理光标上下左右移动
    dealFocusMove(obj, index) {
      var text = obj.value.slice(0, index);
      var resL,
        resR,
        i = 0,
        j = 0;
      var lastIndex = 0;
      var _lastIndex = 0;
      var regL = /\【/g;
      var regR = /\】/g;
      while ((resL = regL.exec(text))) {
        i++;
        lastIndex = regL.lastIndex;
      }
      while ((resR = regR.exec(text))) {
        j++;
      }
      if (i != j) {
        if (index == lastIndex) {
          var rightText = regR.exec(obj.value.slice(index, obj.value.length));
          _lastIndex = rightText["index"];
          index = _lastIndex + index + 1;
        } else {
          index = lastIndex - 1;
        }
        obj.selectionStart = index;
        obj.selectionEnd = index;
      }
    },
    //处理鼠标定位光标
    dealFocusExtend(obj, index) {
      var text = obj.value.slice(index, obj.value.length);
      // var text = obj.value;
      // console.log(text);
      var resL,
        resR,
        i = 0,
        j = 0;
      var lastIndex = 0;
      var firstRightBracketIndex = -1; // 记录第一个右中括号的索引
      var regL = /\【/g;
      var regR = /\】/g;
      while ((resL = regL.exec(text))) {
        i++;
      }
      while ((resR = regR.exec(text))) {
        j++;
        if (firstRightBracketIndex === -1) {
          firstRightBracketIndex = resR.index + index + 1; // 记录第一个右中括号的索引
        }
        lastIndex = regR.index;
      }
      // console.log(index);
      if (i != j) {
        // var text = obj.value;
        index = obj.value.length;
        obj.selectionStart = firstRightBracketIndex;
        obj.selectionEnd = firstRightBracketIndex;
      }
    },
    //获取光标位置
    getFocus(elem) {
      var index = 0;
      if (document.selection) {
        // IE Support
        elem.focus();
        var Sel = document.selection.createRange();
        if (elem.nodeName === "TEXTAREA") {
          //textarea
          var Sel2 = Sel.duplicate();
          Sel2.moveToElementText(elem);
          var index = -1;
          while (Sel2.inRange(Sel)) {
            Sel2.moveStart("character");
            index++;
          }
        } else if (elem.nodeName === "INPUT") {
          // input
          Sel.moveStart("character", -elem.value.length);
          index = Sel.text.length;
        }
      } else if (elem.selectionStart || elem.selectionStart == "0") {
        // Firefox support
        index = elem.selectionStart;
      }
      return index;
    },
    selectDetail(e) {
      console.log(e);
      var obj = document.querySelector("textarea");
      this.cursorIndex = this.getFocus(obj);
      //首先判断是否有光标，这样我们的光标位置是不存在的
      if (this.cursorIndex !== null) {
        //这里判断是否是我们要点击的是不是关键字
        if (
          e.target.tagName !== "TEXTAREA" &&
          e.target.getAttribute("isFocus")
        ) {
          //要添加东西当然要先放入光标了，这里会记住之前的光标位置，所以直接focus即可
          obj.focus();
          this.cursorIndex = this.getFocus(obj);
          var text = obj.value;
          //文本中关键字以中括号包裹的形式显示
          var textNode =
            text.substring(0, this.cursorIndex) +
            "【" +
            e.target.innerHTML +
            "】" +
            text.substring(this.cursorIndex, text.length);
          this.allKeyWords.push(e.target.innerHTML);
          obj.value = textNode;
          //添加完之后我们要刷新光标位置
          this.SMSContent = obj.value;
          this.updateSMSPreview();
          this.cursorIndex = this.cursorIndex + e.target.innerHTML.length + 2;
          obj.selectionStart = this.cursorIndex;
          obj.selectionEnd = this.cursorIndex;
        } else if (
          e.target.tagName == "TEXTAREA" &&
          e.target.getAttribute("isFocus")
        ) {
          //点击文本区域操作
          this.cursorIndex = getFocus(obj);
        } else {
          //点击其他地方要将光标位置置空，防止点击关键字添加
          this.cursorIndex = null;
          console.log(1);
        }
      }
    },
    updateSMSPreview() {
      var obj = document.querySelector("textarea");
      // var templatetypename = document.querySelector('[data-type="templateName"]').value||"";
      //模板原始内容
      var templatename = obj.value || "";
      var regex = /\【([^\【\】]+)\】/g;
      let placeholders = [];
      this.SMSTemplateList.preview = templatename.replace(regex, (match) => {
        placeholders.push(`{${placeholders.length}}`);
        return placeholders[placeholders.length - 1];
      });
      this.SMSTemplateList.parameter = this.getParameter(templatename);
      // console.log(placeholders);
      // console.log(this.SMSTemplateList.parameter);
      console.log(this.SMSTemplateList.preview, this.SMSTemplateList.parameter);
      this.SMSPreview = this.formatPreviewString(
        this.SMSTemplateList.preview,
        this.SMSTemplateList.parameter
      );
      // console.log(this.SMSPreview);
    },
    formatPreviewString(formatted, parameter) {
      // console.log(formatted, parameter);
      if (parameter) {
        var parameterList = parameter.split(",");
        var args = [];
        for (let i = 0; i < parameterList.length; i++) {
          for (let z = 0; z < this.SMSNounList.length; z++) {
            if (parameterList[i] == this.SMSNounList[z].code) {
              args.push(
                "<span id=" +
                  this.SMSNounList[z].code +
                  " style='color: blue;'>" +
                  this.SMSNounList[z].smSdefault +
                  "</span>"
              );
            }
          }
        }
        for (let i = 0; i < args.length; i++) {
          let regexp = new RegExp("\\{" + i + "\\}", "gi");
          formatted = formatted.replace(regexp, args[i]);
        }
      }
      return formatted;
    },
    getParameter(message) {
      // console.log(message);
      // 使用正则表达式提取<span>标签内容
      var regEx = /\【([^\【\】]+)\】/g;
      var spanContents = [];
      var match;

      while ((match = regEx.exec(message))) {
        // 获取匹配到的<span>标签内容
        var spanContent = match[1];
        spanContents.push(spanContent);
      }
      // console.log(spanContents);
      // console.log(spanContents,this.SMSnounIds);
      let nounIds = [];
      for (var i = 0; i < spanContents.length; i++) {
        for (let j = 0; j < this.SMSnounIds.length; j++) {
          // console.log(this.allKeyWords[i]);
          if (spanContents[i] == this.SMSnounIds[j].name) {
            nounIds.push(this.SMSnounIds[j].code);
          }
        }
        // keywords.push(this.keyWordsJson[this.allKeyWords[i]]);
      }
      // console.log(nounIds);
      return nounIds.join(",");
    },

    //代预约
    DYTeamList() {
      let idArr = this.ids;
      if (idArr.length == 0) {
        this.$message.warning("请选择客户信息");
        return;
      }
      this.stateShow = false;
      this.ClassKey = "";
      this.sumtime_Name = "";
      this.CurrentClus = [];
      this.init();
      let tableData = this.tableData;
      let teamName = [];
      for (let i = 0; i < tableData.length; i++) {
        for (let z = 0; z < this.ids.length; z++) {
          if (tableData[i].id == this.ids[z]) {
            teamName.push(tableData[i].name);
          }
        }
      }
      this.teamName = teamName.join("、");
      this.DYYdialogVisible = true;
    },
    //检查输入的参数
    checkDYConfirmInfo() {
      if (!this.lnc_Code) {
        this.$message.warning("未获取到单位");
        return false;
      }
      if (!this.lnc_Name) {
        this.$message.warning("未获取到单位");
        return false;
      }
      if (this.ids.length <= 0) {
        this.$message.warning("客户名单为空");
        return false;
      }
      if (!this.ClassKey) {
        this.$message.warning("请选择体检时间");
        return false;
      }
      if (!this.sumtime_Code || !this.sumtime_Name) {
        this.$message.warning("请选择体检时间段");
        return false;
      }
      if (!this.CurrentClus.clus_Code) {
        this.$message.warning("请选择套餐");
        return false;
      }
      if (!this.CurrentClus.clus_Name) {
        this.$message.warning("请选择套餐");
        return false;
      }
      return true;
    },
    DYConfirm() {
      this.overallLoading = this.$loading({
        lock: true,
        text: "正在预约中",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      if (!this.checkDYConfirmInfo()) {
        this.overallLoading.close();
        return;
      }
      let user = JSON.parse(storage.session.get("user"));
      // console.log(user);
      // return
      let pData = {
        data: {
          ids: this.ids,
        },
        OrdData: {
          sumtime_Code: this.sumtime_Code,
          sumtime_Name: this.sumtime_Name,
          clus_Code: this.CurrentClus.clus_Code,
          clus_Name: this.CurrentClus.clus_Name,
          price: this.CurrentClus.price,
          begin_Time: this.ClassKey,
          lnc_Code: this.lnc_Code,
          company_Name: this.lnc_Name,
          Operator: user.admin_Name,
        },
      };
      ajax
        .post(apiUrls.DYYTeaamList, pData)
        .then((r) => {
          this.overallLoading.close();
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          this.$message.success(r.data.returnMsg);
          this.DYYdialogVisible = false; //成功后关闭对话框
          this.GetTeamList(); //重新加载
        })
        .catch((err) => {
          console.log(err);
          this.$message.error("系统繁忙！请稍后再试");
        });
    },
    //时间段选择判断
    timeBtn(items, index) {
      var that = this;
      if (items.team_Surplus <= 0) {
        Toast("该时段已无号源！请选择其他时段");
        return;
      }
      that.TeamSpans = index;
      that.sumtime_Name = items.sumtime_Name;
      that.sumtime_Code = items.sumtime_Code;
    },
    init() {
      // 初始化当前时间
      this.setCurrent();
      this.calendarCreator();
      // this.switchHaoyuanClass();
    },
    // 判断当前月有多少天
    getDaysByMonth: function (year, month) {
      return new Date(year, month + 1, 0).getDate();
    },
    getFirstDayByMonths: function (year, month) {
      return new Date(year, month, 1).getDay();
    },
    getLastDayByMonth: function (year, month) {
      return new Date(year, month + 1, 0).getDay();
    },
    // 对小于 10 的数字，前面补 0
    pad: function (str) {
      return str < 10 ? `0${str}` : str;
    },
    // 点击上一月
    prevMonth: function () {
      this.current.month--;

      // 因为 month的变化 会超出 0-11 的范围， 所以需要重新计算
      this.correctCurrent();
      // 生成新日期
      this.calendarCreator();
    },
    // 点击下一月
    nextMonth: function () {
      this.current.month++;
      // 因为 month的变化 会超出 0-11 的范围， 所以需要重新计算
      this.correctCurrent();
      // 生成新日期
      this.calendarCreator();
    },
    // 格式化时间，与主逻辑无关
    stringify: function (year, month, date) {
      let str = [year, this.pad(month + 1), this.pad(date)].join("-");
      return str;
    },
    // 设置或初始化 current
    setCurrent: function (d = new Date()) {
      let year = d.getFullYear();
      let month = d.getMonth();
      let date = d.getDate();
      this.current = {
        year,
        month,
        date,
      };
    },
    // 修正 current
    correctCurrent: function () {
      let { year, month, date } = this.current;

      let maxDate = this.getDaysByMonth(year, month);
      // 预防其他月跳转到2月，2月最多只有29天，没有30-31
      date = Math.min(maxDate, date);

      let instance = new Date(year, month, date);
      this.setCurrent(instance);
    },
    // 生成日期
    calendarCreator: function () {
      // 一天有多少毫秒
      const oneDayMS = 24 * 60 * 60 * 1000;

      let list = [];
      let { year, month } = this.current;

      // 当前月份第一天是星期几, 0-6
      let firstDay = this.getFirstDayByMonths(year, month);
      // 填充多少天 firstDay-1则为周一开始，
      let prefixDaysLen = firstDay === 0 ? 0 : firstDay;
      // 毫秒数
      let begin = new Date(year, month, 1).getTime() - oneDayMS * prefixDaysLen;

      // 当前月份最后一天是星期几, 0-6
      let lastDay = this.getLastDayByMonth(year, month);
      // 填充多少天， 和星期的排放顺序有关
      let suffixDaysLen = lastDay === 0 ? 0 : 6 - lastDay;
      // 毫秒数
      let end =
        new Date(year, month + 1, 0).getTime() + oneDayMS * suffixDaysLen;

      while (begin <= end) {
        // 享元模式，避免重复 new Date
        this.shareDate.setTime(begin);
        let year = this.shareDate.getFullYear();
        let curMonth = this.shareDate.getMonth();
        let date = this.shareDate.getDate();
        let week = this.shareDate.getDay(); // 当前周几
        list.push({
          year: year,
          month: curMonth,
          date: date,
          Thing: "待开",
          week: week,
          ThingName: "",
          disable: curMonth !== month,
          value: this.stringify(year, curMonth, date),
        });

        begin += oneDayMS;
      }
      this.calendarList = list;
      this.judgeHaoyuan();
    },

    // 号源显示
    judgeHaoyuan: function () {
      var that = this;
      if (!this.lnc_Code) {
        return;
      }
      // 当前时间
      var nowDate = this.stringify(
        new Date().getFullYear(),
        new Date().getMonth(),
        new Date().getDate()
      );

      var pData = {
        lnccode: this.lnc_Code,
        start: "0",
        end: "30",
      };
      ajax
        .post(apiUrls.GetTeamSumList, pData, { nocrypt: true })
        .then((r) => {
          var TeamList = r.data.returnData;
          for (var i = 0; i < that.calendarList.length; i++) {
            that.prevMonthIconShow();
            if (nowDate > that.calendarList[i].value) {
              that.calendarList[i].Thing = "";
              continue;
            }
            for (var j = 0; j < TeamList.length; j++) {
              if (that.calendarList[i].value == TeamList[j].team_Date) {
                if (TeamList[j].team_Flag == "T") {
                  that.calendarList[i].Thing = "休假";
                  that.calendarList[i].ThingName = "休假";
                  break;
                }
                var team_Surplus = TeamList[j].team_Surplus;
                if (TeamList[j].team_Sum <= 0) {
                  that.calendarList[i].Thing = "待开";
                  that.calendarList[i].ThingName = "待开";
                  break;
                }
                if (team_Surplus > 10) {
                  that.calendarList[i].Thing = "余" + team_Surplus + "人";
                  that.calendarList[i].ThingName = "充足";
                } else if (team_Surplus == 0) {
                  that.calendarList[i].Thing = "约满";
                  that.calendarList[i].ThingName = "约满";
                } else if (team_Surplus <= 10) {
                  that.calendarList[i].Thing = "余" + team_Surplus + "人";
                  that.calendarList[i].ThingName = "紧张";
                }
              }
            }
          }
        })
        .catch((e) => {
          alert("服务异常！请稍等");
          return;
        });
    },
    // 根据日期显示添加类名
    switchHaoyuanClass: function (value) {
      switch (value) {
        case "紧张":
          return "haoyuan-green";
          break;
        case "约满":
          return "haoyuan-red";
          break;
        case "休假":
          return "haoyuan-red";
          break;
        case "充足":
          return "haoyuan-adequate";
          break;
        case "":
          return "";
          break;
      }
    },

    // 号源匹配触发点击
    matchDate: function (date) {
      if (date == "充足" || date == "紧张") {
        return true;
      } else if (date === "约满" || "待开" || "") {
        return false;
      }
    },

    // 当前年月之前不能跳转回去
    prevMonthIconShow: function () {
      if (this.current.year == new Date().getFullYear()) {
        if (this.current.month > new Date().getMonth()) {
          this.pre = true;
          this.pre_ = false;
        } else {
          this.pre = false;
          this.pre_ = true;
        }
      } else if (this.current.year > new Date().getFullYear()) {
        this.pre = true;
        this.pre_ = false;
      }
    },
    // 日期点击事件
    dayClick: function (date, key, index) {
      // 判断能点击的日期是否为号源充足和紧张,休假，待开，""都不可点击
      if (key == "充足" || key == "紧张") {
        this.years = this.current.year;
        this.months = this.current.month + 1;
        this.ClassKey =
          this.years +
          "-" +
          (this.months < 10 ? "0" + this.months : this.months) +
          "-" +
          (date < 10 ? "0" + date : date);
        this.week = this.calendarList[index].week;
        var pData = {
          date_Time: this.ClassKey,
          lnccode: this.lnc_Code,
        };
        //不需要号源时段的项目注释掉
        this.TeamSpans = "";
        var that = this;
        ajax
          .post(apiUrls.GetTeamSumTimeList, pData, { nocrypt: true })
          .then((r) => {
            if (r.data.success) {
              that.stateShow = true;
              var List = r.data.returnData;
              that.sumtimeList = List;
            }
          })
          .catch((e) => {
            Toast("系统异常！请联系管理员");
            return;
          });
        //
        return true;
      } else {
        this.ClassKey = false;
        return false;
      }
    },
    handleCurrentChange(row) {
      this.CurrentClus = row;
    },

    RevokeAtnTeamList() {
      let idArr = this.ids;
      if (idArr.length == 0) {
        this.$message.warning("请选择客户信息");
        return;
      }
      this.$confirm("确定撤销客户激活信息信息吗, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "error",
      }).then(() => {
        let pData = {
          data: {
            ids: idArr,
          },
        };
        ajax
          .post(apiUrls.RevokeAtnTeamList, pData)
          .then((r) => {
            if (!r.data.success) {
              this.$message.error(r.data.returnMsg);
              return;
            }
            this.$message.success(r.data.returnMsg);
            this.GetTeamList();
          })
          .catch((err) => {
            console.log(err);
            this.$message.error("系统繁忙！请稍后再试");
          });
      });
    },
    pickerHandleChange() {
      let date = new Date(this.effectiveDate[1]);
      date.setDate(date.getDate() - this.selectedDay);
      var year = date.getFullYear();
      var month = (date.getMonth() + 1).toString().padStart(2, "0");
      var day = date.getDate().toString().padStart(2, "0");

      var formattedDate = `${year}-${month}-${day}`;
      this.expireTime = formattedDate + " " + this.timeValue;
    },
    showupdateValidityPeriod() {
      let idArr = this.ids;
      if (idArr.length == 0) {
        this.$message.warning("请选择客户信息");
        return;
      }

      this.effectiveDate = [];
      this.timeValue = "09:00:00";
      this.selectedDay = 5;
      this.expireTime = "";
      this.ValidityPeriod = true;
    },
    verifyupdateValidityPeriod() {
      if (!this.lnc_Code) {
        this.$message.warning("未获取到单位");
        return false;
      }
      if (this.effectiveDate.length != 2) {
        this.$message.warning("未获取有效期");
        return false;
      }
      if (!this.expireTime) {
        this.$message.warning("未获取到过期时间");
        return false;
      }
      return true;
    },
    updateValidityPeriod() {
      var that = this;
      //参数验证
      if (!this.verifyupdateValidityPeriod()) {
        return;
      }
      let send = "";
      if (this.CYchecked) {
        send = "1";
      } else {
        send = "2";
      }
      // that.loading = false;
      var pData = {
        data: {
          ids: this.ids,
          code: send,
        },
        teamList: {
          startDate: this.effectiveDate[0],
          endDate: this.effectiveDate[1],
          expireTime: this.expireTime,
        },
      };
      ajax.post(apiUrls.updateValidityPeriod, pData).then((r) => {
        if (!r.data.success) {
          alert(r.data.returnMsg);
          return;
        }
        this.$message.success(r.data.returnMsg);
        this.GetTeamList();
        this.ValidityPeriod = false;
      });
    },
    // 筛选套餐
    filterCombo() {
      let unitComboList = [];
      let officialComboList = [];
      this.tableAllClusData.map((item) => {
        if (this.lnc_Code == item.lnc_Code) {
          unitComboList.push(item);
        }
        if (item.lnc_Code == "*") {
          officialComboList.push(item);
        }
      });
      if (!this.lnc_Code) {
        this.unitComboList = this.tableAllClusData;
        this.officialComboList = officialComboList;
        this.fixed_unitComboList = this.tableAllClusData;
        this.fixed_officialComboList = officialComboList;
        return;
      }

      this.fixed_unitComboList = unitComboList;
      this.unitComboList = unitComboList;
      this.officialComboList = officialComboList;
      this.fixed_officialComboList = officialComboList;
    },
    //知道了按钮
    ClusGotItClick(id) {
      if (this.comboActive == "unitCombo") {
        this.unitComboList.forEach((item) => {
          this.$set(item, "visible", false); // 先将所有的 item.visible 设置为 false
          console.log("this.comboActive");
        });
      } else {
        this.officialComboList.forEach((item) => {
          this.$set(item, "visible", false); // 先将所有的 item.visible 设置为 false
        });
      }
      let pData = {
        data: {
          id: id,
        },
      };
      ajax
        .post(apiUrls.ClusGotItClick, pData)
        .then((r) => {
          if (!r.data.success) {
            this.$message.error(r.data.returnMsg);
            return;
          }
          this.$message.success("更新成功");
          // this.$forceUpdate();
          // 手动刷新 el-popover
          // let myPopover ='popover' + id;
          // this.$refs.myPopover.doClose();
          // this.$refs.myPopover.handleShowPopper();
          this.GetClusList();
        })
        .catch((err) => {
          console.log(err);
          debugger;
          this.$message.error("系统繁忙！请稍后再试");
        });
    },
    // 搜索套餐
    searchCombo() {
      if (this.comboActive == "unitCombo") {
        this.unitComboList = this.fixed_unitComboList.filter((item) => {
          return item.clus_Name.includes(this.searchVal);
        });
        return;
      }
      this.officialComboList = this.fixed_officialComboList.filter((item) => {
        return item.clus_Name.includes(this.searchVal);
      });
    },
    // 套餐类型切换的回调
    tabClick() {
      this.searchVal = "";
      this.officialComboList = this.fixed_officialComboList;
      this.unitComboList = this.fixed_unitComboList;
      this.enabled = false;
    },
    // 编辑的回调
    editClick(row) {
      if (!this.lnc_Code) {
        this.$message.warning("请选择单位");
        return;
      }
      this.editFlag = true;
      this.$nextTick(() => {
        this.$refs.editClus_ref.showAddorEditDialog(row);
      });
    },
    // 新建
    creatClus() {
      if (!this.lnc_Code) {
        this.$message.warning("请选择单位");
        return;
      }
      this.editFlag = true;
      this.$nextTick(() => {
        this.$refs.editClus_ref.showAddorEditDialog();
      });
    },
    // 套餐的点击回调
    clusClick(row) {
      this.checkClus = row;
      console.log(row);
    },
    //删除套餐
    DeleteClus(ids) {
      let idArr = [];
      if (ids) {
        idArr = [ids];
      } else {
        idArr = this.clusIds;
      }
      if (idArr.length == 0) {
        this.$message.warning("请选择套餐");
        return;
      }

      this.$confirm("确定删除此套餐吗, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "error",
      }).then(() => {
        let pData = {
          data: {
            ids: idArr,
          },
        };
        ajax
          .post(apiUrls.DeleteClusById, pData)
          .then((r) => {
            if (!r.data.success) {
              this.$message.error(r.data.returnMsg);
              return;
            }
            this.$message.success("删除成功");
            this.GetClusList();
          })
          .catch((err) => {
            console.log(err);
            debugger;
            this.$message.error("系统繁忙！请稍后再试");
          });
      });
    },
    // 复制套餐
    copyClick(row) {
      if (row) {
        this.clusInfo.clusName = row.clus_Name;
        this.clusIds = [row.id];
      } else {
        this.clusInfo.clusName = "";
        this.clusIds = this.clusIds;
      }
      this.drawerIpnut = "";
      this.loadBtn();
      this.copyClickDialogVisible = true;
      // this.$nextTick(() => {
      //   this.$refs.editClus_ref.showAddorEditDialog(row, true)
      // })
    },
    CopyClus() {
      // if (!this.clusInfo.clusName) {
      //   this.$message({
      //     message: "请输入套餐名称",
      //     type: "error",
      //   });
      //   return;
      // }
      // if (!this.clusInfo.id) {
      //   this.$message({
      //     message: "未获取到套餐id,请重新尝试",
      //     type: "error",
      //   });
      //   this.copyClickDialogVisible = false;
      //   return;
      // }
      let idArr = this.clusIds;
      if (idArr.length == 0) {
        this.$message.warning("请选择套餐");
        return;
      }
      if (!this.clusInfo.lnc_Code) {
        this.$message({
          message: "请选择单位",
          type: "error",
        });
        return;
      }
      let datas = {
        data: {
          clus_Name: this.clusInfo.clusName,
          ids: idArr,
          lnc_Code: this.clusInfo.lnc_Code,
        },
      };
      ajax.post(apiUrls.CopyClus, datas).then((r) => {
        let { success, returnMsg } = r.data;
        // this.checkClus.showFlag = false;
        if (!success) {
          this.$message({
            message: returnMsg,
            type: "error",
          });
          return;
        }
        this.$message({
          message: returnMsg,
          type: "success",
        });
        this.$refs.tableClusData.clearSelection();
        this.copyClickDialogVisible = false;
        this.GetClusList();
      });
    },
    // 开启或关闭排序
    sortClick() {
      this.enabled = !this.enabled;
    },
    // 排序拖动的回调
    checkMove(e) {
      if (this.comboActive == "unitCombo") {
        this.moveCheck = this.unitComboList[e.draggedContext.futureIndex];
      } else {
        this.moveCheck = this.officialComboList[e.draggedContext.futureIndex];
      }
    },
    // 排序开始的回调
    sortStart(e) {
      this.dragging = true;
      console.log(e);
    },
    // 排序结束的回调
    sortEnd(e) {
      console.log(e);
      let reorder = e.item._underlying_vm_.reorder;
      this.dragging = false;
      e.item._underlying_vm_.reorder = this.moveCheck.reorder;
      this.moveCheck.reorder = reorder;
      let datas = {
        clusData: {
          clusterList:
            this.comboActive == "unitCombo"
              ? this.fixed_unitComboList
              : this.fixed_officialComboList,
        },
      };
      ajax
        .post(apiUrls.ClusReorder, datas)
        .then((r) => {
          console.log(r);
        })
        .catch((err) => {
          console.log(err);
          this.$message.error("系统繁忙！请稍后再试");
        });
    },

    searchCombSetting() {
      if (this.comboActive == "unitCombo") {
        this.settingComboList = this.fixed_unitComboList.filter((item) => {
          return item.clus_Name.includes(this.CombName);
        });
        return;
      }
      this.settingComboList = this.fixed_officialComboList.filter((item) => {
        return item.clus_Name.includes(this.CombName);
      });
    },
    showAllClusdialogVisible(val) {
      if (val == "O") {
        this.settingComboList = this.officialComboList;
      } else {
        this.settingComboList = this.unitComboList;
      }
      this.AllClusdialogVisible = true;
    },
    handleClusAvatarSuccess(r, file) {
      // this.imageUrl = URL.createObjectURL(file.raw);
      // console.log(res);

      if (!r.data.success) {
        alert(r.data.returnMsg);
        this.overallLoading.close();
        return;
      }
      this.AllClusdialogVisible = false;
      this.$message.success("导入套餐成功");
      this.GetClusList();
      this.overallLoading.close();
    },
    beforeClusAvatarUpload(file) {
      // const isJPG = file.type === 'application/vnd.ms-excel' || file.type === 'application/vnd.ms-excel';
      // const isLt2M = file.size / 1024 / 1024 < 2;

      // if (!isJPG) {
      //   this.$message.error('上传头像图片只能是 JPG 格式!');
      // }
      // if (!isLt2M) {
      //   this.$message.error('上传头像图片大小不能超过 2MB!');
      // }
      // return isJPG && isLt2M;
      this.overallLoading = this.$loading({
        lock: true,
        text: "正在导入中",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      // let url = "https://localhost:5001" + "/Home/UpPicture";
      const formData = new FormData();
      formData.append("file", file.file); // 将第一个Excel文件添加到请求中
      formData.append("lnc_Code", this.lnc_Code);
      formData.append("lnc_Name", this.lnc_Name);
      return axios.post(this.apiHost + "/Home/UpPictureClus", formData); // 发送POST请求到服务器进行上传
    },
    //一键复制套餐
    CopyClusList(lnc_Code) {
      if (!lnc_Code) {
        this.$message({
          message: "请选择单位",
          type: "error",
        });
        return;
      }
      let idArr = this.clusIds;
      if (idArr.length == 0) {
        this.$message.warning("请选择套餐");
        return;
      }
      this.$confirm("确定复制选中套餐吗, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "error",
      }).then(() => {
        let datas = {
          data: {
            clus_Name: this.clusInfo.clusName,
            ids: idArr,
            lnc_Code: lnc_Code,
          },
        };
        ajax
          .post(apiUrls.CopyClus, datas)
          .then((r) => {
            if (!r.data.success) {
              this.$message.error(r.data.returnMsg);
              return;
            }
            this.$message.success("复制成功");
            this.GetClusList();
          })
          .catch((err) => {
            console.log(err);
            debugger;
            this.$message.error("系统繁忙！请稍后再试");
          });
      });
    },
    //一键导出导出套餐
    ExportExcel() {
      let ids = this.clusIds;
      console.log(ids);
      if (ids.length == 0) {
        this.$message.warning("请选择套餐");
        return;
      }
      var pData = {
        ids: ids,
      };
      // var pdfData = toolsUtils.encrypt(this.ClusList.id);
      var pdfData = encodeURIComponent(
        toolsUtils.encrypt(JSON.stringify(pData) + "/" + "excel@2023")
      );
      console.log(pdfData);
      setTimeout(function () {
        //原因是window.open会中断正在执行的进程，这样能保证其它代码执行完成再执行这个。
        window.location.href =
          "http://192.168.240.103:36889" + "/Home/ExportExcel?code=" + pdfData; //改变页面的location
      }, 300);
    },
    showsortClickDialogVisible(val) {
      if (val == "O") {
        this.settingComboList = this.officialComboList;
      } else {
        this.settingComboList = this.unitComboList;
      }
      this.sortClickDialogVisible = true;
    },
    sortSHandleCurrentChange(row) {
      // this.currentRow = val;
      // console.log(val);
      this.clusInfo.id = row.id;
      this.sortScopeClickDialogVisible = true;
    },
    sortConfirmClus() {
      console.log(this.settingComboList);
      let clus = this.settingComboList.filter((item) => {
        //筛选
        return item.id == this.clusInfo.id;
      });
      if (clus.length > 1) {
        this.$message.warning("ID获取到两个套餐！系统异常");
      }
      console.log(clus);
    },
    depaFilterGroup() {
      // this.tableData = this.tableCopyTableList;
      this.tableCopyTableList = this.tableConstData.filter((item) => {
        return (
          (!this.UserName || item.name.includes(this.UserName)) &&
          (!this.groupCheckList.length ||
            this.groupCheckList.some((group) => item.group === group)) &&
          (!this.sexCheckList.length ||
            this.sexCheckList.some((sex) => item.sex === sex))
        );
      });
      this.groupName = this.groupCheckList.join(",");
      this.tableData = this.tableCopyTableList;
    },
    parseIDCard() {
      if (this.TeamList.id_type == "01") {
        var user = dataUtils.parseIDCard(this.TeamList.idCard);
        if (user != null) {
          // console.log(user);
          (this.TeamList.sex = user.gender),
            (this.TeamList.birthday = user.birthday),
            (this.TeamList.age = user.age);
        }
      }
    },
    winOpenGroupOrder() {
      if (!this.lnc_Name) {
        this.$message.warning("未获取到单位名称");
        return;
      }
      let url =
        this.indexHost +
        "/#/TeamExamination/GroupOrder?lnc_Name=" +
        this.lnc_Name;
      window.open(url);
    },
    GetAddPackage() {
      var that = this;
      // that.loading = true;
      // if (this.lnc_Code=="") {
      //   that.loading = false;
      //   this.$message.error("未识别到单位编码");
      //   return;
      // }
      var pData = {
        lnc_Code: this.lnc_Code,
      };
      ajax
        .post(apiUrls.GetaddPackageByCode, pData, { nocrypt: true })
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          that.addPackageData = r.data.returnData;
        })
        .catch((err) => {
          alert("获取加项包失败,请稍后重试");
        });
    },
    handleaddPackageChangeids(rows) {
      this.addPackageCodes = rows.map((row) => row.item_Code);
      console.log(this.addPackageCodes);
    },
    // 查看联系人
    contactPersonClick() {
      this.editCP = true;
      this.$nextTick(() => {
        this.$refs.contactPerson_ref.showAddorEditDialog(
          JSON.parse(storage.session.get("lncList"))
        );
      });
    },
  },
};
</script>

<style lang="scss">
.TeamPOP_page {
  display: flex;
  flex-direction: column;
  overflow: auto;
  height: 100%;
}

.TjBox .company {
  height: 40px;
}

.content_wrap {
  display: flex;
  flex: 1;
  flex-shrink: 0;
  overflow: auto;

  .combo_wrap {
    width: 300px;
    background: #fff;
    display: flex;
    flex-direction: column;
    overflow: auto;
    border-right: 1px solid #e5e5e5;

    .el-tabs__content {
      flex: 1;
      flex-shrink: 0;
    }

    .el-tab-pane {
      height: 100%;
      display: flex;
      flex-direction: column;
      height: 100%;
      overflow: auto;

      .combo_search {
        display: flex;
        align-items: center;
        padding: 0 5px;

        i {
          font-size: 20px;
          cursor: pointer;
          margin: 0 5px;
          color: #6a9be4;
        }

        span {
          font-size: 16px;
          color: #6a9be4;
          cursor: pointer;
        }
      }

      .combo_list {
        flex: 1;
        flex-shrink: 0;
        overflow: auto;
        padding: 0;

        li {
          cursor: pointer;
          padding: 0 5px;
          border-bottom: 1px solid #b2bec3;
          padding-bottom: 5px;
          color: #018bf0;
          position: relative;
          height: auto;

          h1 {
            font-size: 16px;
            color: #000000;
          }

          .sort_p {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            margin: 0;
            font-size: 30px;
          }
        }

        .combo_name {
          width: 90%;
          display: flex;
          justify-content: space-between;
          font-family: PingFangSC-Medium;

          h2 {
            color: #d0021b;
          }
        }

        .line-container {
          display: flex;

          .line-container-combo span {
            font-size: 20px;
            margin-left: 5px;
            color: red;
          }
        }

        .line-container > div:first-child,
        .line-container > .combo_operate {
          margin-right: 10px;
        }

        .combo_operate {
          margin-left: auto;
          display: flex;
          justify-content: flex-end;

          span {
            padding: 0 5px;
            font-size: 12px;
            color: #989a9c;
          }
        }

        .active {
          border-color: #018bf0;
          background: #018bf0;
          color: #fff;

          h1,
          h2 {
            color: #fff;
          }

          span {
            color: #fff;
            border-color: #fff;
          }

          .line-container-combo span {
            color: #fff;
          }
        }
      }
    }
  }

  .table_wrap {
    box-sizing: border-box;
    flex: 1;
    flex-shrink: 0;
    width: auto !important;
    overflow: auto;
    margin: 0 !important;
    background: #fff;
    padding: 0 10px;

    .table_top {
      padding: 0 5px !important;
    }
  }
}

.lnc_list {
  p {
    cursor: pointer;
    margin: 0;
    padding: 5px;

    &:hover {
      background: #018bf0;
      color: #fff;
    }
  }
}

/* 表格按钮 */
.lncDiv {
  // display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 96%;
  margin: 20px auto;

  .lncTop {
    width: 100%;
    margin-top: 10px;
  }

  .lncMid {
    margin-top: 0 !important;
    width: 100%;

    .pageNation {
      margin-top: 10px;

      .el-pagination {
        display: flex;
        justify-content: flex-end;
      }
    }
  }
}

.haoyuan-red {
  color: red;
}

.haoyuan-bg {
  background-color: baga(250, 250, 250, 0.3);
  color: #ccc;
  cursor: default !important;
}

.button-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  .el-button {
    margin-left: 0px;
    margin-top: 5px;
  }
}

.el-upload-list {
  width: 10%;
}

.cell .el-table__column-filter-trigger i {
  font-size: 18px;
}

.container {
  overflow: hidden;
  /* 清除浮动 */
}

.radio-container {
  float: left;
  width: 10%;
  font-size: 27px;
}

.form-container {
  margin-left: 10%;
  width: 90%;
}

.el-radio__inner {
  width: 16px;
  height: 16px;
}

// .el-form-item__label {
//   font-size: 39px; /* 修改小红点的字体大小 */
//   line-height: 16px; /* 修改小红点的行高 */
// }
.el-form-item.is-required:not(.is-no-asterisk)
  .el-form-item__label-wrap
  > .el-form-item__label:before,
.el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label:before {
  font-size: 20px;
  /* 修改小红点的字体大小 */
  line-height: 16px;
  /* 修改小红点的行高 */
}

.box-card {
  width: 100% !important;
}

.el-box-card-item-sms {
  border: 1px solid rgb(189, 179, 179);
  padding: 20px 10px;
}

.el-box-card-item-content {
  padding-left: 80px;
  padding: 10px 0px 10px 80px;
  background-color: #e5e5e5;
}

.el-box-card-item-time {
  border: 1px solid rgb(189, 179, 179);
  padding: 20px 10px;
}

// .el-date-editor el-input el-input--prefix el-input--suffix el-date-editor--date{
//   width: 20%;
// }
.Atn_cont_Div {
  width: 50%;
  margin-top: 20px;
  display: flex;
  justify-content: center;
  border: 1px solid #837f7f;
  height: 600px;
}

.textareaDiv {
  font-size: 12px !important;
}

.textareaCon .textareaNou {
  font-size: 12px;
}

.text {
  font-size: 16px;
}

.item {
  padding: 18px 0;
}

.col-box {
  padding-left: 7.5px;
  padding-right: 7.5px;
}

.activatePreview {
  overflow: auto;
}

.box-card {
  width: 100%;
  // border: 1px solid red($color: #000000);
  border: 1px solid rgb(221, 222, 225);
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.smsDiv,
.textTable {
  margin-top: 10px;
  // color:rgb(152, 167, 18);
}

.textTable span {
  font-family: Arial;
  font-weight: 700;
}

.tabs_first_Div {
  widows: 100%;
  height: 60px;
  background: 1px scroll red;
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  justify-content: center;
}

.DYConfirmDiv {
  font-family: "Lucida Sans", "Lucida Sans Regular", "Lucida Grande",
    "Lucida Sans Unicode", Geneva, Verdana, sans-serif;
  margin-top: 13px;
  margin-left: 13px;
  font-size: 18px;
}

.DYConfirmSpan {
  margin-left: 8px;
  color: rgb(224, 103, 22);
}

.DYConfirmDiv div {
  margin-top: 3px;
  font-size: 16px;
}

.popoverDiv {
  position: absolute;
  // top: -5px;
  bottom: 20px;
  right: 0;
  z-index: 999;
  width: 250px;
  height: auto;
  background-color: #f5f5f5;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  color: #ffa500;
  font-size: 16px;
}

.popover-contentDiv span {
  color: #ffa500;
}

.popover-contentDiv {
  padding: 10px;
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 220px;
  /* 设置h1标题的宽度为220px */
}

@media (max-width: 220px) {
  .ellipsis {
    width: auto;
    /* 当屏幕宽度小于等于220px时，自动调整宽度 */
  }
}

.AllClusButtonTop {
  display: flex;
  // justify-content: flex-end;
}

.AllClusButtonDiv {
  display: flex;
  margin-bottom: 1.28rem;
  justify-content: flex-end;
}

.AllClusButton {
  // width: calc(100% / 3);
  // // display: flex;
  // // justify-self: center;
  // // align-items: center;
  margin: auto;
  /* 使元素在容器中居中 */
  text-align: center;
  /* 使元素在容器中居中 */
  margin-right: 20px;
}

.combo_setting {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.sexImg {
  width: 28px;
  height: 28px;
  margin-right: 5px;
}

.TipsContainer {
  font-size: 20px;
  margin-left: 20px;
  height: 100px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.TipsContainer span {
  font-size: 20px;
  color: #409eff;
  margin: auto 10px;
}

.TipsContainer button {
  font-size: 20px;
}

.toggleSelection {
  display: flex;
  margin-bottom: 10px;
  justify-content: space-between;
}

.toggleSelectionCon {
  display: flex;
  align-items: center;
  margin-left: 10px;
  margin-left: 10px;
  font-size: 16px;
}

.dialog-content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  // height: 100%;
  height: "calc( 100vh - 300px)";
  overflow: auto;
}

.button-area {
  height: 60px;
  position: fixed;
  z-index: 100;
  bottom: 0px;
  left: 0px;
  right: 0px;
  background-color: #cdc9cd;
  display: flex;
  align-items: center;
  justify-content: center;
}

// .el-card__body{
//   height: 100%;
//   max-height: 400px;
//   overflow: auto;
// }
.box-card-Atn-cont {
  width: 100%;
  max-height: 400px;
  overflow: auto;
}

.ellipsis-column_name {
  color: rgb(0, 20, 202);
}
.contactPersonSpan {
  font-size: 16px;
  color: #00aaff;
  margin-left: 5px;
}
</style>
