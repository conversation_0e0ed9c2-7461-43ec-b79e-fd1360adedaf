<template>
  <div>
    <div class="lncDiv">
      <div style="width: 100%">项目列表</div>
      <div class="lncTop">
        <div class="lncTop table_top">
          <div style="display: flex">
            <el-input
              placeholder="项目名称(为空时默认加载全部)"
              style="width: 240px"
              v-model="combName"
              size="small"
            ></el-input>
            <div class="getSele">
              <el-button-group>
                <el-button
                  :class="warningOrNode('F')"
                  @click="itemCombState = false"
                  >禁用</el-button
                >
                <el-button
                  :class="warningOrNode('T')"
                  @click="itemCombState = true"
                  >启用</el-button
                >
              </el-button-group>
            </div>
            <div class="getSeleState">
              <el-checkbox v-model="StateChecked">查看隐藏项</el-checkbox>
            </div>
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="GetNewData"
              style="margin-left: 10px"
              size="small"
              >查询</el-button
            >
          </div>
          <div class="divRight">
            <!-- <el-button type="success" icon="el-icon-plus" @click="SyncComb()" size="small">同步</el-button> -->
            <el-button
              type="success"
              @click="exportExcel()"
              icon="el-icon-download"
              size="small"
              >Excel导出</el-button
            >
            <el-button
              type="danger"
              icon="el-icon-delete"
              @click="DeleteComb()"
              size="small"
              >批量删除</el-button
            >
          </div>
        </div>
      </div>
    </div>

    <div class="lncMid">
      <el-table
        :data="tableData"
        v-loading="loading"
        element-loading-text="拼命加载中"
        border
        stripe
        :fit="true"
        row-key="id"
        @selection-change="handleSelectionChangePeople"
        :height="height"
      >
        <el-table-column
          type="selection"
          width="55"
          align="center"
          :reserve-selection="true"
        ></el-table-column>
        <el-table-column
          prop="comb_Code"
          label="项目编码"
          width="200"
          align="center"
          sortable
        ></el-table-column>
        <el-table-column prop="sort_Name" label="分类" align="center" sortable>
        </el-table-column>
        <el-table-column
          prop="sort"
          label="执行科室"
          align="center"
          sortable
        ></el-table-column>
        <el-table-column
          prop="comb_Name"
          label="项目名称"
          align="center"
          sortable
        ></el-table-column>
        <el-table-column
          prop="comb_Price"
          label="项目价格"
          align="center"
          sortable
        ></el-table-column>
        <el-table-column label="项目描述" align="center" sortable>
          <template slot-scope="{ row }">
            <div class="ellipsis-column" v-bind:title="row.note">
              {{ row.note }}
            </div>
          </template>
        </el-table-column>
        <!-- <el-table-column
            prop="peisStatus"
            label="院内状态"
            align="center"
            sortable
          ></el-table-column> -->
        <el-table-column
          prop="state"
          label="状态"
          align="center"
          sortable
        ></el-table-column>
        <el-table-column
          prop="isPriority"
          label="是否独立放号"
          align="center"
          sortable
        ></el-table-column>
        <el-table-column
          prop="createTime"
          label="添加时间"
          align="center"
          sortable
        ></el-table-column>
        <el-table-column fixed="right" label="操作" align="center" width="200">
          <template slot-scope="scope">
            <el-button
              @click="showAddorEditDialog(scope.row)"
              type="primary"
              plain
              >编辑</el-button
            >
            <el-button @click="DeleteComb(scope.row.id)" type="danger" plain
              >删除</el-button
            >
            <!--单个删除-->
          </template>
        </el-table-column>
      </el-table>
      <div class="pageNation">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="index"
          :page-sizes="[50, 100, 200, 300, 500]"
          :page-size="size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableCopyTableList.length"
        ></el-pagination>
      </div>

      <!--新增/编辑对话框-->
      <el-dialog :title="dialogTitle" :visible.sync="dialogVisible">
        <el-tabs v-model="activeName" @tab-click="handleCombClick">
          <el-tab-pane label="项目信息" name="first"></el-tab-pane>
          <el-tab-pane label="项目关系" name="second"></el-tab-pane>
        </el-tabs>
        <div
          v-if="activeName == 'first'"
          style="display: flex; white-space: nowrap"
        >
          <div style="width: 40%">
            <el-form :label-position="'right'" label-width="80px">
              <el-form-item label="项目编码">
                <el-input
                  v-model="ClusItemCombList.comb_Code"
                  placeholder="请输入项目编码"
                  size="small"
                  readonly
                ></el-input>
              </el-form-item>
              <el-form-item label="项目名称">
                <el-input
                  v-model="ClusItemCombList.comb_Name"
                  placeholder="请输入项目名称"
                  size="small"
                  readonly
                ></el-input>
              </el-form-item>
              <!-- <el-form-item label="院内状态">
                  <el-input
                    v-model="ClusItemCombList.peisStatus"
                    placeholder=""
                    size="small"
                    readonly
                  ></el-input>
                </el-form-item> -->
              <el-form-item label="状态">
                <el-select v-model="ClusItemCombList.state" size="small">
                  <el-option label="启用" value="T"></el-option>
                  <el-option label="隐藏" value="C"></el-option>
                  <el-option label="禁用" value="F"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="独立放号">
                <el-select v-model="ClusItemCombList.isPriority" size="small">
                  <el-option label="是" value="T"></el-option>
                  <el-option label="否" value="F"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="项目分类">
                <el-select
                  v-model="ClusItemCombList.classification"
                  size="small"
                >
                  <el-option
                    v-for="item in classOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-form>
          </div>
          <div style="width: 60%">
            <el-form :label-position="'right'" label-width="80px">
              <el-form-item label="性别">
                <el-select v-model="ClusItemCombList.sex" size="small">
                  <el-option label="通用" value="%"></el-option>
                  <el-option label="男" value="1"></el-option>
                  <el-option label="女" value="0"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="项目价格">
                <el-input
                  v-model="ClusItemCombList.comb_Price"
                  type="number"
                  placeholder="请输入项目价格"
                  size="small"
                ></el-input>
              </el-form-item>
              <el-form-item label="项目描述">
                <el-input
                  type="textarea"
                  :rows="2"
                  v-model="ClusItemCombList.Note"
                  placeholder="请输入项目描述"
                  size="small"
                ></el-input>
              </el-form-item>
              <el-form-item label="注意事项">
                <el-switch
                  v-model="combSwitch"
                  active-color="#13ce66"
                  inactive-color="#ff4949"
                  @change="handleChangeSwitch"
                >
                </el-switch>
                <br />
                <el-input
                  type="textarea"
                  :rows="4"
                  v-model="ClusItemCombList.attentionMsg"
                  placeholder="请输入注意事项"
                  size="small"
                ></el-input>
              </el-form-item>
            </el-form>
          </div>
        </div>
        <div v-if="activeName == 'second'">
          <div class="el-collapse-item is-active">
            <div>
              <el-button type="success" size="small" @click="connectItems('TZ')"
                >同组关系</el-button
              >
            </div>
            <div class="el-collapse-item__content">
              <div class="el-collapse-item__content_lay">
                <span v-for="(item, index) in tzData" :key="index"
                  >【{{ item.comb_Name }}】</span
                >
              </div>
            </div>
          </div>
          <div class="el-collapse-item is-active">
            <div>
              <el-button type="danger" size="small" @click="connectItems('ME')"
                >互斥关系</el-button
              >
            </div>
            <div class="el-collapse-item__content">
              <div class="el-collapse-item__content_lay">
                <span
                  v-for="(item, index) in meData"
                  :key="index"
                  style="margin-right: 5px"
                  >【{{ item.comb_Name }}】</span
                >
              </div>
            </div>
          </div>
          <div class="el-collapse-item is-active">
            <div>
              <el-button type="primary" size="small" @click="connectItems('BD')"
                >依赖于</el-button
              >
            </div>
            <div class="el-collapse-item__content">
              <div class="el-collapse-item__content_lay">
                <span v-for="(item, index) in bdData" :key="index"
                  >【{{ item.comb_Name }}】</span
                >
              </div>
            </div>
          </div>
        </div>
        <div slot="footer">
          <el-button type="danger" size="small" @click="dialogVisible = false"
            >取消</el-button
          >
          <el-button type="primary" size="small" @click="addOrEditLnc"
            >保存</el-button
          >
        </div>
      </el-dialog>
      <!-- 关联项目 -->
      <el-dialog
        title="关联项目"
        :visible.sync="visibleItemList"
        width="50%"
        :close-on-click-modal="false"
      >
        <el-transfer
          class="custom-transfer"
          filterable
          v-model="itemValue"
          :data="itemData"
          :titles="['组合列表', '已选组合']"
          style="width='70%'"
          @change="transferChange"
          ref="myTransfer"
        >
          <template slot-scope="{ option }">
            <span>{{ option.label }}</span></template
          >
        </el-transfer>
        <div slot="footer">
          <el-button type="danger" size="small" @click="visibleItemList = false"
            >取消</el-button
          >
          <el-button type="primary" size="small" @click="confirmConnectItem"
            >确定</el-button
          >
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { ajax, storage } from "../../common";
// import{Toast} from 'vant';
import apiUrls from "../../config/apiUrls";
export default {
  name: "Lnclist",
  data() {
    return {
      ids: "", //id集合 用于批量删除或单个删除
      height: "calc( 100vh - 250px)",
      tableData: [], //表数据
      tableCopyTableList: [], //表数据集合
      tableConstData: [], //存放数据 用于筛选数据
      index: 1, //当前页数
      size: 50, //页码
      dialogVisible: false, //新增编辑对话框是否显示
      dialogTitle: "", //对话框的标题
      loading: false,
      //项目模型
      ClusItemCombList: {
        id: "",
        comb_Code: "",
        comb_Name: "",
        comb_Price: "",
        Note: "",
        attentionMsg: "",
        combSwitch: "",
        ME: "",
        BD: "",
        TZ: "",
        state: "",
        isPriority: "",
        createTime: "",
        peisStatus: "",
        sex: "",
        classification: "",
      },
      visibleItemList: false,
      itemValue: [],
      itemData: [],
      itemAllData: [],
      activeName: "first",
      firtItemName: "",
      combSwitch: false,
      activeNames: ["1", "2", "3", "4"],
      bdData: "",
      tzData: "",
      meData: "",
      combName: "",
      itemCombState: true,
      classOptions: [],
      StateChecked: false,
    };
  },
  created() {
    this.GetAllClusItemComb();
    this.GetClassificationList();
  },
  methods: {
    //获取选中行id
    handleSelectionChangePeople(rows) {
      this.ids = rows.map((row) => row.id);
    },
    //显示模态框
    showAddorEditDialog(row) {
      if (row == undefined) {
        this.dialogTitle = "新增项目";
      } else {
        this.dialogTitle = "查看/编辑项目";
      }
      this.ClusItemCombList.id = row ? row.id : "";
      this.ClusItemCombList.comb_Code = row ? row.comb_Code : "";
      this.ClusItemCombList.comb_Name = row ? row.comb_Name : "";
      this.ClusItemCombList.comb_Price = row ? row.comb_Price : "";
      this.ClusItemCombList.Note = row ? row.note : "";
      this.ClusItemCombList.attentionMsg = row ? row.attentionMsg : "";
      this.combSwitch =
        (row ? row.combSwitch : "true") == "true" ? true : false;
      this.ClusItemCombList.combSwitch = row ? row.combSwitch : "";
      this.ClusItemCombList.createTime = row ? row.createTime : "";
      // this.ClusItemCombList.ME = row ? row.me : "";
      // this.ClusItemCombList.BD = row ? row.bd : "";
      // this.ClusItemCombList.TZ = row ? row.tz : "";
      this.ClusItemCombList.sort = row ? row.sort : "";
      this.ClusItemCombList.peisStatus = row ? row.peisStatus : "";
      this.ClusItemCombList.classification = row ? row.classification : "";
      // this.ClusItemCombList.state =
      //   (row ? row.state : "启用") == "启用" ? "T" : "F";
      // console.log(row.state);
      this.ClusItemCombList.state = row
        ? row.state != "禁用"
          ? row.state == "隐藏"
            ? "C"
            : "T"
          : "F"
        : "F";
      this.ClusItemCombList.isPriority =
        (row ? row.isPriority : "是") == "是" ? "T" : "F";
      this.ClusItemCombList.sex = row
        ? row.sex != "通用"
          ? row.sex == "男"
            ? "1"
            : "0"
          : "%"
        : "%";
      this.getCombRelation();
      this.dialogVisible = true;
    },
    handleChangeSwitch() {
      this.ClusItemCombList.combSwitch = this.combSwitch;
    },
    // 页数改变事件
    handleSizeChange(size) {
      this.size = size;
      this.tableData = this.paging(size, this.index);
    },
    // 页码改变事件
    handleCurrentChange(current) {
      this.index = current;
      this.tableData = this.paging(this.size, current);
    },
    // 本地分页的方法
    paging(size, current) {
      const tableList = JSON.parse(JSON.stringify(this.tableCopyTableList));
      const tablePush = [];
      tableList.forEach((item, index) => {
        if (size * (current - 1) <= index && index <= size * current - 1) {
          tablePush.push(item);
        }
      });
      return tablePush;
    },
    //获取体检分类
    GetClassificationList() {
      var that = this;
      that.loading = false;
      ajax
        .post(apiUrls.GetClassificationList)
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          // 初始化数据
          this.itemAllData = [];
          that.tableConstData = r.data.returnData;
          this.tableConstData.forEach((item) => {
            this.classOptions.push({
              value: item.sort_Code,
              label: item.sort_Name,
            });
          });
          // this.GetNewData();
          that.tableData = that.paging(that.size, that.index);
        })
        .catch((err) => {
          alert("获取体检分类失败,请稍后重试");
        });
    },
    //获取所有项目信息
    GetAllClusItemComb() {
      var that = this;
      that.loading = false;
      ajax
        .post(apiUrls.GetAllClusItemComb)
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          // 初始化数据
          this.itemAllData = [];
          that.tableConstData = r.data.returnData;
          that.tableCopyTableList = r.data.returnData;
          this.tableConstData.forEach((item) => {
            this.itemAllData.push({
              key: item.comb_Code,
              label: item.comb_Name,
            });
          });
          this.GetNewData();
          that.tableData = that.paging(that.size, that.index);
        })
        .catch((err) => {
          alert("获取项目失败,请稍后重试");
        });
    },
    SyncComb() {
      this.loading = true;
      ajax.post(apiUrls.SyncComb).then((r) => {
        if (r.data.success) {
          this.$message.success(r.data.returnMsg);
          this.GetAllClusItemComb();
        }
        this.loading = false;
      });
    },
    //删除项目
    DeleteComb(ids) {
      let idArr = [];
      if (ids) {
        idArr = [ids];
      } else {
        idArr = this.ids;
      }
      if (idArr.length == 0) {
        this.$message.warning("请选择项目");
        return;
      }

      this.$confirm("确定删除此项目吗, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "error",
      }).then(() => {
        let pData = {
          data: {
            ids: idArr,
          },
        };
        ajax
          .post(apiUrls.DeleteCombById, pData)
          .then((r) => {
            if (!r.data.success) {
              this.$message.error(r.returnMsg);
              return;
            }
            this.$message.success("删除成功");
            this.GetAllClusItemComb();
          })
          .catch((err) => {
            console.log(err);
            this.$message.error("系统繁忙！请稍后再试");
          });
      });
    },
    //检查输入的参数
    checkAdminInfo() {
      if (!this.ClusItemCombList.comb_Code) {
        this.$message.warning("请输入项目编码");
        return false;
      }
      if (!this.ClusItemCombList.comb_Name) {
        this.$message.warning("请输入项目名称");
        return false;
      }
      // console.log(this.ClusItemCombList.comb_Price);
      if (
        this.ClusItemCombList.comb_Price === null ||
        this.ClusItemCombList.comb_Price === ""
      ) {
        // this.$message.warning("请输入项目价格");
        // return false;
        this.ClusItemCombList.comb_Price = 0;
      }
      return true;
    },
    //新增或者修改项目
    addOrEditLnc() {
      //参数验证
      if (!this.checkAdminInfo()) {
        return;
      }

      var addOrEdit = ""; //请求变量 新增/编辑 api地址不一样
      if (this.ClusItemCombList.id) {
        addOrEdit = apiUrls.UpdateComb;
      } else {
        this.ClusItemCombList.id = "0";
        // addOrEdit = apiUrls.AddLnc;
      }
      var pData = {
        clusItemComb: this.ClusItemCombList,
      };
      ajax
        .post(addOrEdit, pData)
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          this.$message.success("操作成功");
          this.dialogVisible = false; //成功后关闭对话框
          this.GetAllClusItemComb(); //重新加载
        })
        .catch((err) => {
          console.log(err);
          this.$message.error("系统繁忙！请稍后再试");
        });
    },
    //表格筛选
    GetNewData() {
      this.tableCopyTableList = this.tableConstData.filter((item) => {
        //筛选
        // if (this.itemCombState.length == 0) {
        //   return !this.combName || item.name.includes(this.combName);
        // }
        // for (let i = 0; i < this.itemCombState.length; i++) {
        //   if (this.itemCombState[i] == item.state) {
        //     return !this.combName || item.name.includes(this.combName);
        //   }
        // }
        if (this.itemCombState) {
          if (this.StateChecked) {
            return (
              (!this.combName || item.comb_Name.includes(this.combName)) &&
              (item.state == "启用" || item.state == "隐藏")
            );
          }
          //筛选
          return (
            (!this.combName || item.comb_Name.includes(this.combName)) &&
            item.state == "启用"
          );
        } else {
          if (this.StateChecked) {
            return (
              (!this.combName || item.comb_Name.includes(this.combName)) &&
              (item.state == "禁用" || item.state == "隐藏")
            );
          }
          //筛选
          return (
            (!this.combName || item.comb_Name.includes(this.combName)) &&
            item.state == "禁用"
          );
        }
      });
      this.tableData = this.paging(this.size, this.index);
    },
    handleCombClick() {
      // if (this.activeName == "second") {
      //   this.getCombRelation();
      // }
    },
    getCombRelation() {
      // let a = this.itemAllData.filter((item) => {
      //   return item.key == this.ClusItemCombList.TZ;
      // });
      // console.log(a);
      // this.meData = this.itemAllData.filter((item) => {
      //   return item.key == this.ClusItemCombList.ME;
      // });
      // this.tzData = this.itemAllData.filter((item) => {
      //   return item.key == this.ClusItemCombList.TZ;
      // });
      // this.bdData = this.itemAllData.filter((item) => {
      //   return item.key == this.ClusItemCombList.BD;
      // });
      // console.log(this.tzData);
      if (!this.ClusItemCombList.sort) {
        this.$message.warning("未识别到分类");
      }
      // if (!this.ClusItemCombList.relation) {
      //   this.$message.warning("获取项目关系出错");
      // }
      if (!this.ClusItemCombList.comb_Code) {
        this.$message.warning("未识别到项目编码");
      }
      var pData = {
        combRin: {
          sort: this.ClusItemCombList.sort,
          comb_Code: this.ClusItemCombList.comb_Code,
        },
      };
      ajax
        .post(apiUrls.getCombRelation, pData)
        .then((r) => {
          this.meData = r.data.returnData.me;
          this.tzData = r.data.returnData.tz;
          this.bdData = r.data.returnData.bd;
          // this.$message.success("更新成功");
          this.visibleItemList = false;
        })
        .catch((err) => {
          console.log(err);
          this.$message.error("系统繁忙！请稍后再试");
        });
    },
    //关联项目
    connectItems(firtName) {
      storage.session.set("value", this.ClusItemCombList.comb_Code);
      this.firtItemName = firtName;

      this.itemValue = [];
      this.itemData = this.itemAllData.filter(
        (item) => item.key !== this.ClusItemCombList.comb_Code
      );
      if (firtName == "ME") {
        this.itemValue = this.meData.map((i) => i.comb_Code);
      } else if (firtName == "BD") {
        this.itemValue = this.bdData.map((i) => i.comb_Code);
      } else if (firtName == "TZ") {
        this.itemValue = this.tzData.map((i) => i.comb_Code);
      } else {
        this.$message.error("系统繁忙！请稍后再试");
      }
      this.visibleItemList = true;
      if (this.$refs.myTransfer) {
        this.$refs.myTransfer.$children["0"]._data.query = "";
        // 清空右边搜索
        this.$refs.myTransfer.$children["3"]._data.query = "";
      }
    },
    //关联项目确认
    confirmConnectItem() {
      var that = this;
      let value = storage.session.get("value");
      let code = this.firtItemName;
      if (!this.ClusItemCombList.sort) {
        this.$message.warning("未识别到分类");
      }
      if (!code) {
        this.$message.warning("获取项目关系出错");
      }
      if (!value) {
        this.$message.warning("未识别到项目编码");
      }
      this.combs = this.itemValue.join(",");
      var pData = {
        combRin: {
          sort: this.ClusItemCombList.sort,
          comb_Code: value,
          relation: code,
          sonComb: this.combs,
        },
      };
      ajax
        .post(apiUrls.UpdateCombRelation, pData)
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          this.$message.success("更新成功");
          // this.$refs.gxTransfer.clearQuery();
          this.getCombRelation();
        })
        .catch((err) => {
          console.log(err);
          this.$message.error("系统繁忙！请稍后再试");
        });

      // const questionCode = storage.session.get("questionCode");
      // this.GetAllClusItemComb();
      // });
    },
    transferChange(value, direction, moveKeys) {},
    warningOrNode(val) {
      if (val == "T" && this.itemCombState) {
        return "el-button el-button--warning";
      }
      if (val == "F" && !this.itemCombState) {
        return "el-button el-button--warning";
      }
    },
    //导出表格
    exportExcel() {
      require.ensure([], () => {
        const { export_json_to_excel } = require("../../common/Export2Excel"); //js存放的位置
        let excelName = "项目列表";
        const tHeader = [
          "医院项目类别",
          "医院体检项目",
          "执行科室",
          "医院项目编码",
          "医院项目价格（元）",
          "适用性别",
          "注意事项",
          "临床意义",
        ]; //生成Excel表格的头部标题栏
        const filterVal = [
          "sort_Name",
          "comb_Name",
          "sort",
          "comb_Code",
          "comb_Price",
          "sex",
          "attentionMsg",
          "note",
        ]; //生成Excel表格的内容栏（根据自己的数据内容属性填写）
        const list = this.tableData; //需要导出Excel的数据
        const data = this.formatJson(filterVal, list);
        export_json_to_excel(tHeader, data, excelName); //这里可以定义你的Excel表的默认名称
      });
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map((v) => filterVal.map((j) => v[j]));
    },
  },
};
</script>

<style lang="scss">
.lncDiv {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 96%;
  margin: 20px auto;

  .lncTop {
    width: 100%;
    margin-top: 10px;
    display: flex;
    margin-bottom: 20px;

    .getSele {
      width: 150px;
      margin-left: 10px;

      .el-input__inner {
        padding: 0 10px !important;
        width: 200px !important;
      }
    }

    .getSeleState {
      width: 100px;
      margin-left: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
    }
  }

  .lncMid {
    margin-top: 20px;
    width: 100%;

    .pageNation {
      margin-top: 10px;

      .el-pagination {
        display: flex;
        justify-content: flex-end;
      }
    }
  }
}

.custom-transfer {
  width: 100% !important;
}

.custom-transfer .el-transfer-panel,
.custom-transfer .el-transfer-panel:last-child {
  width: 40% !important;
  height: 500px;
}

.el-transfer-panel,
.custom-transfer .el-transfer-panel:last-child {
  height: 500px;
}

.el-checkbox-group el-transfer-panel__list is-filterable {
  width: 100%;
  height: 500px;
}

.el-transfer-panel__body {
  height: 100%;
}

.el-transfer-panel__list.is-filterable {
  height: 80%;
}

.el-collapse-item {
  margin-top: 20px;
  border-bottom: 1px solid #ebeef5;
}

.el-collapse-item__header {
  height: 20px;
}

.el-collapse-item__content {
  padding-bottom: 0px;
}

.el-collapse-item__content_lay {
  height: 35px;
}

.ellipsis-column {
  max-width: 150px;
  /* 根据需要调整最大宽度 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ellipsis-column:hover {
  white-space: normal;
  overflow: visible;
  z-index: 9999;
  /* 可选：调整悬停时的层级 */
  background-color: #fff;
  /* 可选：调整悬停时的背景颜色 */
}
</style>
