<template>
  <div>
    <div class="page" v-loading="loading" element-loading-text="获取中。。。">
      <!-- 页头 -->
      <div class="page-top">
        <div>问卷调查</div>
        <div class="page-option">
          <!-- <el-select v-model="questionLevel" placeholder="请选择问题级别">
            <el-option 
            v-for="item in questionLevelOptions" 
            :key="item.key" :label="item.label" 
            :value="item.key"></el-option>
          </el-select> -->
          <el-button type="primary" size="small" @click="addQuestionClick"
            >增加问题</el-button
          >
        </div>
      </div>
      <!-- 表格数据 -->
      <div class="page-table">
        <el-table
          :data="tableData"
          border
          stripe
          :fit="true"
          @selection-change="handleSelectionChangePeople"
          :height="height"
        >
          <el-table-column
            prop="questionName"
            label="问题列表"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="questionType"
            label="问题类型"
            align="center"
            sortable
          ></el-table-column>
          <el-table-column 
          prop="questionLevel" 
          label="问题级别" 
          align="center"
          sortable></el-table-column>
          <el-table-column 
          prop="questionOrder" 
          label="问题顺序" 
          align="center" 
          sortable
          ></el-table-column>
          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <div class="operation">
                <div>
                  <el-button
                    @click="showAddorEditDialog(scope.row)"
                    type="primary"
                    size="small"
                    plain
                    >编辑修改</el-button
                  >
                </div>
                <div>
                  <el-button
                    @click="releEditDialog(scope.row)"
                    type="success"
                    size="small"
                    plain
                    >答案关联</el-button
                  >
                </div>
                <div>
                  <el-button
                    type="danger"
                    size="small"
                    @click="deleteQuestionClick(scope.row)"
                    >删除问题</el-button
                  >
                </div>
              </div>

              <!--单个删除-->
            </template>
          </el-table-column>
        </el-table>
        <div class="pageNation">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="index"
            :page-sizes="[50, 100, 200, 300, 500]"
            :page-size="size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableCopyTableList.length"
          ></el-pagination>
        </div>
      </div>
      <!-- 新增/修改问题 -->
      <el-dialog
        :title="addOrEditTitle"
        :visible.sync="addOrEditQuestionDialogVisible"
        width="30%"
        :close-on-click-modal="false"
        center
      >
        <el-form 
        :model="questionForm" :rules="rules" ref="questionForm" label-width="6rem">
          <div class="edit-question">
            <!-- 问题内容 -->
            <el-form-item label="问题描述" prop="questionName">
              <el-input
              type="textarea"
              :rows="2"
              v-model="questionForm.questionName"
              placeholder="请输入问题"
              show-word-limit
              ></el-input>
            </el-form-item>
            <!-- 问题扩展说明 -->
            <el-form-item label="问题说明" prop="questionExplain">
              <el-input
              type="textarea"
              :rows=2
              v-model="questionForm.questionExplain"
              placeholder="请输入问题扩展说明">
              </el-input>
            </el-form-item>
            <!-- 问题级别 -->
            <el-form-item label="问题级别" prop="questionLevel">
              <el-select v-model="questionForm.questionLevel" placeholder="请选择问题级别">
              <el-option
              v-for="item in questionLevelOptions"
              :key="item.key"
              :label="item.label"
              :value="item.key"></el-option>
            </el-select>
            </el-form-item>
            <el-form-item label="作答方式" prop="questionType">
              <!-- 作答方式 -->
            <el-select v-model="questionForm.questionType" placeholder="请选择问题类型">
              <el-option
                v-for="item in questionTypeOptions"
                :key="item.key"
                :label="item.label"
                :value="item.key"
              ></el-option>
            </el-select>
            </el-form-item>
            <el-form-item label="问题顺序" prop="questionOrder">
              <!-- 问题排序（前端初步判断是否重复，后台接口最终确认是否重复） -->
              <el-input type="number" v-model="questionForm.questionOrder" placeholder="请输入问题顺序"></el-input>
            </el-form-item>
            <el-form-item label="是否启用" prop="state" v-if="addOrEditTitle==='修改问题'">
              <el-select v-model="questionForm.state" placeholder="请选择状态">
                <el-option 
                v-for="item in stateOptions" 
                :key="item.value" 
                :label="item.label" 
                :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <div class="dialogButton">
              <el-form-item>
                <el-button
                  type="danger"
                  size="small"
                  @click="addOrEditQuestionDialogVisible = false"
                  >取消</el-button
                  >
                  <el-button type="primary" size="small" @click="confirmAddClassify('questionForm')"
                    >确定</el-button
                  >
            </el-form-item>
            </div>
          </div>
        </el-form>
      </el-dialog>

      <!--答案关联-->
      <el-dialog
        title="答案列表"
        :visible.sync="answerDialogVisible"
        :close-on-click-modal="false"
        center
      >
        <div style="width: 100%">
            <el-form>
              <el-form-item v-for="(item, index) in answerList" :key="index">
                <div>
                  <el-input
                    type="textarea"
                    placeholder="请输入内容"
                    v-model="item.answerName">
                  </el-input>
                </div>
                <div class="button-select">
                  <el-button type="danger" size="medium" @click="deleteItems(item)">删除</el-button>
                  <el-button v-if="item.answerCode" size="medium" type="info" @click="connectItems(item)">关联项目</el-button>
                  <el-select v-if="item.answerCode" v-model="item.subQuestion" clearable="" placeholder="请选择子问题">
                    <el-option
                    v-for="item in subQuestionOptions"
                    :key="item.key"
                    :label="item.label"
                    :value="item.key"></el-option>
                  </el-select>
                </div>
                
              </el-form-item>
            </el-form>
        </div>
        <div slot="footer">
          <el-button type="success" size="small" @click="addinput"
              >添加</el-button
            >
          <el-button type="primary" size="small" @click="insertOrUpdateAnswer"
            >确定</el-button
          >
          <el-button
            type="danger"
            size="small"
            @click="answerDialogVisible = false"
            >取消</el-button
          >
        </div>
      </el-dialog>

      <!-- 关联项目 -->
      <el-dialog
        title="关联项目"
        :visible.sync="visibleItemList"
        width="50%"
        :close-on-click-modal="false"
      >
        <el-transfer
          class="transfer-dom"
          filterable
          v-model="itemValue"
          :data="itemData"
          :titles="['组合列表', '已选组合']"
          @change="transferChange"
        >
          <template slot-scope="{ option }">
            <span>{{ option.label }}</span></template
          >
        </el-transfer>
        <div slot="footer">
          <el-button type="primary" size="small" @click="confirmConnectItem"
            >确定</el-button
          >
          <el-button type="danger" size="small" @click="visibleItemList = false"
            >取消</el-button
          >
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { ajax, storage } from "../../common";
import apiUrls from "../../config/apiUrls";
export default {
  name: "Questionnaire",
  data() {
    return {
      rules:{
        questionName:[
          {required:true,message:"请输入问题描述",trigger:"blur"}
        ],
        questionLevel:[
          {required:true,message:"请选择问题级别",trigger:"blur"},
        ],
        questionType:[
          {required:true,message:"请选择问题作答方式",trigger:"blur"},
        ],
        questionOrder:[
          {required:true,message:"请填写问题序号",trigger:"blur"}
        ]
      },
      ids: "", //id集合 用于批量删除或单个删除
      height: "calc( 100vh - 250px)",
      tableData: [], //表数据
      tableCopyTableList: [], //表数据集合
      index: 1, //当前页数
      size: 50, //页码
      addOrEditQuestionDialogVisible: false, //新增对话框是否显示
      loading: false,
      addOrEditTitle:"",
      items: [],
      questionTitle: "",
      questionLevelOptions:"",//问题级别选项
      subQuestionOptions:"",//子问题选项
      questionTypeOptions: [
      ], //填写类型选项
      stateOptions:[
        {
          value:"0",
          label:"禁用"
        },
        {
          value:"1",
          label:"启用"
        }
      ],
      questionForm:{
        questionName:"",//问题描述
        questionExplain:"",//问题扩展说明
        questionType: "", //问题类型
        questionOrder:"",//问题顺序
        questionLevel:"",//问题级别
        state:"",//问题状态
      },
      answerDialogVisible: false,
      answerList: [],
      sortIndex: "", //问题顺序
      visibleItemList: false,
      itemData: [], //穿梭框元数据
      itemValue: [], //穿梭框选定数据
      combs: "",
      questionLevel:"",//页头问题级别选择
      editor:"",//操作者
      subQuestion:[]
    };
  },
  created() {
    //加载问题
    this.getQuestionList();
    this.getWxItems();
    this.getQuestionLevelList();
    this.getQuestionTypeList();
    this.editor = JSON.parse(storage.session.get("user")).admin_Code;
  },
  methods: {
    //获取问题级别列表
    getQuestionLevelList(){
      ajax.post(apiUrls.GetQuestionLevelList)
      .then(r=>{
        if(!r.data.success)
        {
          this.$message(r.data.returnMsg);
          return;
        }
        this.questionLevelOptions = r.data.returnData;
      })
    },
    //获取问题作答方式列表
    getQuestionTypeList(){
      ajax.post(apiUrls.GetQuestionTypeList)
      .then(r=>{
        if(!r.data.success){
          this.$message(r.data.returnMsg);
          return;
        }
        this.questionTypeOptions = r.data.returnData;
      })
    },
    //获取所有项目
    getWxItems() {
      ajax.post(apiUrls.GetWxItems).then((r) => {
        this.itemData = [];
        let res = JSON.parse(r.data.returnData);
        // console.log(res);
        res.forEach((item) => {
          this.itemData.push({
            key: item.comb_Code,
            label: item.comb_Name,
          });
        });
      });
    },
    getQuestionList() {
      this.loading = true;
      ajax
        .post(apiUrls.GetQuestionnaireList)
        .then((r) => {
          let data = r.data;
          if (!data.success) {
            this.$message.warning(r.data.returnMsg);
            this.loading = false;
            return;
          }
          this.tableData = data.returnData;
          this.loading = false;
          this.tableCopyTableList = this.tableData;
        })
        .catch((e) => {
          this.$message.console.error("获取异常数据失败！请联系管理员");
          this.loading = false;
          return;
        });
    },
    //增加答案
    addinput() {
      this.answerList.push({
        questionCode: this.answerList[0].questionCode,
        name: "",
      });
    },
    showAddorEditDialog(row) {
      this.addOrEditTitle = "修改问题";
      this.addOrEditQuestionDialogVisible = true;
      let pData = {
        questionCode: row.questionCode,
      };
      ajax
        .post(apiUrls.GetQuestionnaireDetail, pData, { nocrypt: true })
        .then((r) => {
          let data = r.data;
          if (data.success) {
            this.questionForm = data.returnData;
            return;
          }
          this.$message.warning(r.data.returnMsg);
          return;
        })
        .catch((e) => {
          this.$message.console.error("获取异常数据失败！请联系管理员");
        });
    },
    //答案关联
    releEditDialog(row) {
      storage.session.set("questionCode", row.questionCode);
      this.getAnswerList(row.questionCode);
      this.answerDialogVisible = true;
      this.getNextQuestionList(row.questionLevel);
    },
    // 获取下一级问题列表
    getNextQuestionList(questionLevel){
      let pData = {
        questionLevel:questionLevel
      }
      ajax.post(apiUrls.GetNextQuestionList,pData,{nocrypt:true})
      .then(r => {
        this.subQuestionOptions = r.data.returnData;
      })
    },
    //获取答案列表
    getAnswerList(questionCode) {
      let pData = {
        questionCode: questionCode,
      };
      // console.log(pData);
      ajax.post(apiUrls.GetAnswerList, pData, { nocrypt: true }).then((r) => {
        if (r.data.success) {
          this.answerList = r.data.returnData;
          return;
        }
      });
    },
    //修改答案信息
    insertOrUpdateAnswer() {
      this.answerList.forEach(item=>{
        item.editor = this.editor;
      })
      let pData = this.answerList;
      // console.log(pData);
      ajax.post(apiUrls.AddAnswerList, pData).then((r) => {
        if (r.data.success) {
          this.$message.success(r.data.returnMsg);
          // this.answerDialogVisible = false;
          this.getAnswerList(this.answerList[0].questionCode);
          return;
        }
        this.$message.warning(r.data.returnMsg);
      });
    },
    //获取选中行id
    handleSelectionChangePeople(rows) {
      this.ids = rows.map((row) => row.questionCode);
    },

    // 页数改变事件
    handleSizeChange(size) {
      this.size = size;
      this.tableData = this.paging(size, this.index);
    },
    // 页码改变事件
    handleCurrentChange(current) {
      this.index = current;
      this.tableData = this.paging(this.size, current);
    },
    // 本地分页的方法
    paging(size, current) {
      const tableList = JSON.parse(JSON.stringify(this.tableCopyTableList));
      const tablePush = [];
      tableList.forEach((item, index) => {
        if (size * (current - 1) <= index && index <= size * current - 1) {
          tablePush.push(item);
        }
      });
      return tablePush;
    },
    //增加问题按钮
    addQuestionClick() {
      this.addOrEditTitle = "新增问题";
      //清空输入框和选择框
      this.questionForm = {};
      //编辑备注显示标题
      this.addOrEditQuestionDialogVisible = true;
    },
    //调用接口增加问题
    confirmAddClassify(formName) {
      this.$refs[formName].validate((valid) => {
        if(valid){
          // 新增
          if(this.addOrEditTitle === "新增问题"){
            this.questionForm.creator = JSON.parse(storage.session.get("user")).id;
            ajax.post(apiUrls.AddQuestion, this.questionForm, { nocrypt: true }).then((r) => {
              if (!r.data.success) {
                this.$message.error(r.data.returnMsg);
                this.addOrEditQuestionDialogVisible = false;
                return;
              }
              this.$message.success("新增成功");
              this.addOrEditQuestionDialogVisible = false;
              this.getQuestionList();
            });
            }
          // 修改
          if(this.addOrEditTitle === "修改问题"){
            this.questionForm.editor = JSON.parse(storage.session.get("user")).id;
            ajax.post(apiUrls.UpdateQuestion,this.questionForm,{nocrypt:true})
            .then(r => {
              if(!r.data.success){
                this.$message.error(r.data.returnMsg);
                this.addOrEditQuestionDialogVisible = false;
                return;
              }
              this.$message.success("修改成功");
              this.addOrEditQuestionDialogVisible = false;
              this.getQuestionList();
            })
          }
          
        }else{
          return false;
        }
      });
    },
    //删除问题
    deleteQuestionClick(row) {
      this.$confirm("此操作将永久删除该问题，是否继续？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const pData = {
            questionCode: row.questionCode,
          };
          ajax
            .post(apiUrls.DeleteQuestion, pData, { nocrypt: true })
            .then((r) => {
              if (!r.data.success) {
                this.$message.error("系统错误，请联系管理员!");
                return;
              }
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getQuestionList();
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    //删除答案
    deleteItems(item){
      let pData = {
        answerCode:item.answerCode
      }
      if(item.answerCode){
        this.$confirm('此操作将永久删除该答案, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          ajax.post(apiUrls.DelAnswer,pData,{nocrypt:true})
          .then(r=>{
            if(!r.data.success){
              this.$message({
                type: 'error',
                message: r.data.returnMsg
              });
              return;
            }
            this.$message({
              type: 'success',
              message: r.data.returnMsg
            });
            this.getAnswerList(item.questionCode)
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });          
        });
      }else{
        let index = this.answerList.indexOf(item);
        // console.log(index);
        if(index !== -1){
          this.answerList.splice(index,1);
        }
      }
    },
    //关联项目
    connectItems(item) {
      this.insertOrUpdateAnswer();
      // console.log(item);
      this.itemValue = [];
      if (item.combs != null) {
        this.itemValue = item.combs.split(",");
      }
      // console.log(item);
      storage.session.set("value", item.answerCode);
      this.visibleItemList = true;
    },
    //关联项目确认
    confirmConnectItem() {
      let value = storage.session.get("value");
      // console.log("1221",this.itemValue);
      //将关联项目存入答案表中
      this.combs = "";
      this.itemValue.forEach((element) => {
        this.combs += element + ",";
      });
      this.combs = this.combs.substring(0, this.combs.length - 1);
      const pData = {
        value: value,
        combs: this.combs,
      };
      ajax.post(apiUrls.AddCombs, pData, { nocrypt: true }).then((r) => {
        if (!r.data.success) {
          this.$message.error("系统错误！");
          return;
        }
        this.$message.success("更新成功");
        this.visibleItemList = false;
        const questionCode = storage.session.get("questionCode");
        this.getAnswerList(questionCode);
      });
    },
    filterMethod(query, item) {},
    transferChange(value, direction, moveKeys) {
    },
  },
};
</script>

<style lang="scss" scoped>
.page {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 96%;
  margin: 20px auto;
  .page-top {
    display: flex;
    width: 100%;
    margin-top: 10px;
    justify-content: space-between;
  }
  .page-option{
    display: flex;
    gap: 10px;
  }
  .page-table {
    margin-top: 20px;
    width: 100%;
    .pageNation {
      margin-top: 10px;
      .el-pagination {
        display: flex;
        justify-content: flex-end;
      }
    }
    .operation {
      display: flex;
      justify-content: space-around;
    }
  }
  .transfer-dom {
    width: 100%;
    display: flex;
    .el-transfer-panel {
      width: 0;
      flex: 1;
    }
  }
  .edit-question{
    display: flex;
    flex-direction:column;
    gap:0.5rem;
  }
  .dialogButton{
    display: flex;
    flex-direction: row;
    align-content: center;
  }
  ::v-deep.el-form-item__content{
    gap: 10px;
    display: flex;
    flex-direction: row;
  }
  .button-select{
    display: flex;
    align-items: center;
    gap:10px;
  }
}
</style>
<style>
.el-textarea {
  padding: 5px !important;
}
</style>
