<template>
  <div>
    <div class="ordDiv">
      <div style="width:100%;">订单列表</div>
      <div class="lncTop">
        <div class="getNew">
          <el-input placeholder="手机号码" v-model="tel" size="small"></el-input>
        </div>
        <el-button
          type="primary"
          icon="el-icon-search"
          @click="GetsendSMSRecord"
          size="small"
          style="margin-left: 10px;"
        >查询</el-button>
      </div>
      <div class="lncMid">
        <el-table
          :data="tableData"
          border
          stripe
          :default-sort="{ prop: 'id', order: 'descending' }"
          @selection-change="handleSelectionChangePeople"
          id="ordertable"
          :height="height"
          row-key="id"
          :fit="true"
        >
          <el-table-column
            type="selection"
            width="55"
            :reserve-selection="true"
          ></el-table-column>

         <el-table-column
            :prop="item.propValue"
            :label="item.label"
            :width="item.width"
            align="center"
            sortable
            v-for="(item, index) in propData"
            :key="index"
          ></el-table-column>
        </el-table>
        <div class="pageNation">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="index"
            :page-sizes="[50, 100, 200, 300, 500]"
            :page-size="size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableCopyTableList.length"
          ></el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ajax } from "../../common";
import apiUrls from "../../config/apiUrls";
import baseData from "../../../public/baseData.json";
import FileSaver from "file-saver";
import XLSX from "xlsx";
export default {
  name:'PersonalOrder',
  data() {
    return {
      ids: "", //id集合 用于批量删除或单个删除
      height: "calc( 100vh - 250px)",
      tableData: [], //表格数据源
      tableCopyTableList: [], //保存数据 用于分页
      tableConstData: [], //存放数据 用于筛选数据
      index: 1, //默认在第一页
      size: 50, //默认一页50条数据
      beginTime: [], //日期控件的值
      tel:'',
      propData: [ //width  有需要就加，其余的自适应
        {
          propValue: "id",
          label: "id",
          width:"60"
        },
        {
          propValue: "tel",
          label: "联系电话",
          width:"180"
        },
        {
          propValue: "msgid",
          label: "msgid",
          width:"180"
        },
         {
          propValue: "content",
          label: "内容",
        },
        {
          propValue: "state",
          label: "状态",
          width:"90"
        },
        {
          propValue: "createTime",
          label: "添加时间",
          width:"180"
        }
      ],
    };
  },
  created() {
  },
  methods: {
    // 页数改变事件
    handleSizeChange(size) {
      this.size = size;
      this.tableData = this.paging(size, this.index);
    },
    // 页码改变事件
    handleCurrentChange(current) {
      this.index = current;
      this.tableData = this.paging(this.size, current);
    },
    // 本地分页的方法
    paging(size, current) {
      const tableList = JSON.parse(JSON.stringify(this.tableCopyTableList));
      const tablePush = [];
      tableList.forEach((item, index) => {
        if (size * (current - 1) <= index && index <= size * current - 1) {
          tablePush.push(item);
        }
      });
      return tablePush;
    },
    //获取所有个检订单
    GetsendSMSRecord() {
      var that=this;
      if(!this.tel){
        this.$message.warning("请输入手机号");
        return;
      }
      
      var pData={
        tel:this.tel
      }

      ajax
        .post(apiUrls.GetsendSMSRecord,pData,{nocrypt:true})
        .then(r => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }

          var data=r.data.returnData;
          // 初始化数据
          that.tableConstData = data;
          that.tableCopyTableList = data;
          that.tableData = this.paging(this.size, this.index);
        })
        .catch(err => {
          alert("获取订单失败,请稍后重试");
        });
    },
    //表格的事件
    handleSelectionChangePeople(rows) {
      this.ids = rows.map(row => row.id);
    },
  }
};
</script>

<style lang="scss">
.ordDiv {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 96%;
  margin: 20px auto;
  .lncTop {
    width: 100%;
    margin-top: 10px;
    display: flex;
    // justify-content: space-between;
    .getNew {
      width: 180px;
      .el-input__inner {
        padding: 0 5px !important;
        text-align: center;
        width: 180px !important;
      }
    }
    .getSele {
      width: 100px;
      margin-left: 10px;
      .el-input__inner {
        padding: 0 10px !important;
        width: 100px !important;
      }
    }
    .getPicker {
      width: 300px;
      margin-left: 10px;
      .el-range-editor.el-input__inner {
        padding: 3px 6px !important;
        width: 300px;
        display: flex;
        justify-content: space-between;
      }
      .el-date-editor .el-range-separator {
        padding: 0 !important;

        margin-left: 5px;
        margin-right: 5px;
      }
      .el-date-editor .el-range-input {
        width: 50% !important;
      }
    }
  }
  .lncMid {
    margin-top: 20px;
    width: 100%;
    .pageNation {
      margin-top: 10px;
      .el-pagination {
        display: flex;
        justify-content: flex-end;
      }
    }
  }
}
</style>
