<template>
  <div>
    <div class="TjBox">
      <!-- 单位日期 -->
      <div class="company" id="company">
        <!--单位选择 -->
        <div class="companySwitch">
          <el-button @click="drawer = true" type="primary" size="medium">
            切换单位
          </el-button>
          <span>单位：<strong class="companylnc_Name">{{ lnc_Name }}</strong></span>
          <input type="hidden" v-model="lnc_Code" />
          <!--抽屉 -->
          <el-drawer title="单位号源" :visible.sync="drawer" :with-header="false">
            <div class="drawerAll">
              <div class="drawerTop">单位列表</div>
              <div class="drawerSearch">
                <!-- <div class="searchTwo">体检单位预约列表</div> -->
                <div class="searchTwo">
                  <el-input placeholder="单位编码/单位名称" prefix-icon="el-icon-search" v-model="drawerIpnut"
                    size="mini"></el-input>
                  <el-button type="primary" plain size="mini" @click="querys">查询</el-button>
                </div>
              </div>
              <div class="drawerList">
                <div class="drawerTr1">
                  <div class="drawerTd1">
                    <span>单位编码</span>
                  </div>
                  <div class="drawerTd2">
                    <span>单位名称</span>
                  </div>
                </div>
                <div v-for="(item, index) in drawerData" :key="index" class="drawerTr2"
                  :class="{ hoverIndex: index == hoverIndex }" @mouseover="hoverIndex = index" @mouseout="hoverIndex = -1"
                  @click="drawerBtn(index)">
                  <div class="drawerTd3">
                    <!-- <span>{{ item.drawerCode }}</span> -->
                    <span>{{ item.lnc_Code }}</span>
                  </div>
                  <div class="drawerTd4">
                    <!-- <span>{{ item.drawerName }}</span>-->
                    <span>{{ item.lnc_Name }}</span>
                  </div>
                </div>
              </div>
            </div>
          </el-drawer>
        </div>
      </div>
    </div>

    <div class="clusDiv">
      <div style="width: 100%">组合列表</div>
      <div class="clusTop">
        <el-input placeholder="组合名称(为空时默认加载全部)" style="width: 240px" v-model="addCombinaList.clus_Name"
          size="small"></el-input>
        <el-button type="primary" icon="el-icon-search" @click="GetNewData" style="margin-left: 10px"
          size="small">查询</el-button>
        <el-button type="success" icon="el-icon-plus" @click="showAddorEditDialog()" size="small">新增组合</el-button>
        <el-button type="danger" icon="el-icon-delete" @click="DeleteClus()" size="small">批量删除</el-button>
      </div>

      <div class="clusMid">
        <el-table :data="tableData" v-loading="loading" element-loading-text="拼命加载中" border stripe :fit="true"
          row-key="id" @selection-change="handleSelectionChangePeople" :height="height">
          <el-table-column type="selection" width="55" align="center" :reserve-selection="true"></el-table-column>
          <el-table-column prop="item_Code" label="组合编码" width="200" align="center" sortable></el-table-column>
          <el-table-column prop="item_Name" label="组合名称" align="center"></el-table-column>
          <el-table-column prop="item_Note" label="组合简介" align="center"></el-table-column>
          <el-table-column prop="price" label="价格" align="center" sortable></el-table-column>
          <el-table-column prop="state" label="状态" align="center"></el-table-column>
          <el-table-column prop="createTime" label="添加时间" align="center"></el-table-column>
          <el-table-column label="操作" align="center" width="400" fixed="right">
            <template slot-scope="scope">
              <el-button @click="showEditItemDialog(scope.row)" type="success" plain>查看/编辑项目</el-button>
              <el-button @click="showAddorEditDialog(scope.row)" type="primary" plain>编辑</el-button>
              <el-button @click="DeleteClus(scope.row.id)" type="danger" plain>删除</el-button>
              <!--单个删除-->
            </template>
          </el-table-column>
        </el-table>
        <div class="pageNation">
          <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="index"
            :page-sizes="[50, 100, 200, 300, 500]" :page-size="size" layout="total, sizes, prev, pager, next, jumper"
            :total="tableCopyTableList.length"></el-pagination>
          <!--新增/编辑对话框-->
          <el-dialog :title="dialogTitle" :visible.sync="dialogVisible">
            <el-form :label-position="'right'" label-width="80px">
              <el-form-item label="组合编码">
                <el-input type="text" v-model="addCombinaList.item_Code" :placeholder="addCombinaList.item_Code == ''
                  ? '系统生成'
                  : addCombinaList.item_Code
                  " size="small" disabled></el-input>
              </el-form-item>
              <el-form-item label="组合名称">
                <el-input type="text" v-model="addCombinaList.item_Name" placeholder="请输入组合名称" size="small"></el-input>
              </el-form-item>
              <el-form-item label="组合价格">
                <el-input type="text" v-model="addCombinaList.price" placeholder="请输入组合价格" size="small"></el-input>
              </el-form-item>
              <el-form-item label="组合描述">
                <el-input type="textarea" :rows="4" v-model="addCombinaList.item_Note" placeholder="请输入组合描述"
                  size="small"></el-input>
              </el-form-item>
              <el-form-item label="是否启用">
                <el-select v-model="addCombinaList.state" size="small">
                  <el-option label="启用" value="T"></el-option>
                  <el-option label="禁用" value="F"></el-option>
                </el-select>
              </el-form-item>
            </el-form>
            <div slot="footer">
              <el-button type="danger" size="small" @click="dialogVisible = false">取消</el-button>
              <el-button type="primary" size="small" @click="addOrEditAddCombist">确定</el-button>
            </div>
          </el-dialog>
          <!--查看/编辑加项包对话框-->
          <el-dialog title="查看/编辑组合包含项" :visible.sync="showItemDialogVisible" top="5vh">
            <el-form :label-position="'top'" label-width="80px">
              <el-form-item label="基本信息">
                <!-- <el-table :data="addCombinaListTableData" border style="width: 100%">
                  <el-table-column prop="item_Name" label="组合名称">
                  </el-table-column>
                  <el-table-column prop="item_Code" label="组合编码">
                  </el-table-column>
                </el-table> -->
                <el-table border :data="addCombinaListTableData" style="width: 100%">
                  <el-table-column prop="item_Name" label="组合名称">
                  </el-table-column>
                  <el-table-column prop="item_Code" label="组合编码" width="200">
                  </el-table-column>
                </el-table>
              </el-form-item>
              <el-form-item label="包含项目(仅能选择启用状态的项目)">
                <div style="display: flex;justify-content: space-between;">
                  <div style="width:40%">
                    <!-- <el-input type="text" v-model="addCombinaList.item_Name" placeholder="请输入项目名称" size="small"></el-input> -->
                    <el-select v-model="item_Name" filterable placeholder="请输入项目名称" style="width: 100%;"
                      @change="selectCombItemData">
                      <!-- <el-option
                        v-for="item in CombData"
                        :key="item.comb_Code"
                        :label="item.comb_Name"
                        :value="item.comb_Code">
                      </el-option> -->
                      <el-option key="comb_Code" value="comb_Code" disabled>
                        <span style="float: left;margin-right: 10px;">编码</span>
                        <span>项目名称</span>
                        <span style="float: right; color: #8492a6; font-size: 13px">价格</span>
                      </el-option>
                      <el-option v-for="item in CombData" :key="item.comb_Code" :label="item.comb_Name"
                        :value="item.comb_Code">
                        <span style="float: left;margin-right: 10px;">{{ item.comb_Code }}</span>
                        <span>{{ item.comb_Name }}</span>
                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.comb_Price }}</span>
                      </el-option>
                    </el-select>
                  </div>
                  <div style="width:40%">
                    <div>
                      <span
                        style="font-size: 18px;color: red;font-family:'Franklin Gothic Medium', 'Arial Narrow', Arial, sans-serif">编辑项目后请务必点击保存按钮！</span>
                      <el-button style="margin-left: 10px;" type="success"
                        @click="addOrEditAddCombistItemList">保存</el-button>
                    </div>
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="已选项目">
                <el-table :data="itemTableData" style="width: 100%">
                  <el-table-column prop="comb_Name">
                    <template slot="header" slot-scope="scope">
                      <span style="font-family: 'Avenir',math,monospace;font-size: 16px;"> 组合对应包含项目({{
                        itemTableData.length }}项)</span>
                    </template>
                    <template slot-scope="scope">
                      <span v-if="isExistenceItem(scope.row.comb_Code)">{{ scope.row.comb_Name }}<span
                          style="margin-left: 7px;font-size: 11px;color: red;position: absolute;bottom: 9px;">NEW</span></span>
                      <span style="margin-left: 10px" v-else>{{ scope.row.comb_Name }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="comb_Code">
                  </el-table-column>
                  <el-table-column width="200">
                    <template slot="header" slot-scope="scope">
                      <h4> 共计：<span style="color: red;font-size: 16px;">￥{{ itemTableDataPrice }}</span></h4>
                    </template>
                    <template slot-scope="scope">
                      <div style="display: flex;justify-content:center">
                        <el-button type="danger" @click="daleteCombItemData(scope.row)" icon="el-icon-delete" circle
                          size="mini"></el-button>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
            </el-form>
            <!-- <div slot="footer">
              <el-button
                type="danger"
                size="small"
                @click="dialogVisible = false"
                >取消</el-button
              >
              <el-button
                type="primary"
                size="small"
                @click="addOrEditAddCombist"
                >确定</el-button
              >
            </div> -->
          </el-dialog>
        </div>
      </div>
    </div>
  </div>
</template>
  
<script>
import temporaryData from "../../common/temporaryData.js"; //号源临时假数据
import { ajax } from "../../common/ajax";
import apiUrls from "../../config/apiUrls";
import { storage, toolsUtils } from "@/common";
export default {
  name: "BookTj",
  data() {
    return {
      drawer: false, //切换号源抽屉
      darwerValue: "所有单位", //号源值
      drawerIpnut: "", //单位编码名称
      hoverIndex: -1, //表示当前hover的是第几个div 初始为 -1 或 null 不能为0 0表示第一个div
      sourceValue: "总号源",
      everyWidth: "width:calc( 100% / 16)",
      height: "calc( 100vh - 350px)",
      lnc_Code: "",
      drawerData: [], //单位列表
      ContactList: [{}],
      CombAllData: [],
      CombData: [],
      checkboxGroup1: [],
      lnc_Name: "",
      checkedAllCities: [],
      checkedCities: [],
      selectCombList: [],
      ids: "", //id集合 用于批量删除或单个删除
      tableData: [], //表数据
      tableCopyTableList: [], //表数据集合
      tableConstData: [], //存放数据 用于筛选数据
      index: 1, //当前页数
      size: 50, //页码
      dialogVisible: false, //新增编辑对话框是否显示
      dialogTitle: "", //对话框的标题
      BusinessType: "",
      loading: false,
      //组合模型
      addCombinaList: {
        id: "",
        item_Code: "",
        item_Name: "",
        state: "",
        price: "",
        item_sex: "",
        item_Note: "",
      },
      item_Name: "",
      showItemDialogVisible: false,
      addCombinaListTableData: [],
      itemTableData: [],
      allItemData: [],
    };
  },
  created() {
    this.loadBtn();
    var lncList = JSON.parse(storage.session.get("lncList"));
    if (lncList == null) {
      this.drawer = true;
    } else {
      this.lnc_Code = lncList.lnc_Code;
      this.lnc_Name = lncList.lnc_Name;
      this.GetAddCombination();
    }
    this.GetAllClusItemComb();
    //this.dateEvent();
  },
  computed: {
    itemTableDataPrice() {
      let price = 0;
      this.itemTableData.forEach((items) => {
        price += items.comb_Price;
      })
      return price.toFixed(2);
    }
  },
  methods: {
    load() { },
    //获取选中行id
    handleSelectionChangePeople(rows) {
      this.ids = rows.map((row) => row.id);
    },
    //显示模态框
    showAddorEditDialog(row) {
      if (row == undefined) {
        this.dialogTitle = "新增组合";
        this.BusinessType = "Add";
      } else {
        this.dialogTitle = "查看/编辑组合";
        this.BusinessType = "Edit";
        // this.GetClusItemComb(row.item_Code);
      }
      this.allowAddComb = [];
      this.checkedCities = [];
      this.price = 0;
      this.addCombinaList.id = row ? row.id : "";
      this.addCombinaList.item_Code = row ? row.item_Code : "";
      this.addCombinaList.item_Name = row ? row.item_Name : "";
      this.addCombinaList.price = row ? row.price : "";
      this.addCombinaList.state =
        (row ? row.state : "启用") == "启用" ? "T" : "F";
      this.addCombinaList.item_Note = row ? row.item_Note : "";
      this.addCombinaList.lnc_Code = row ? row.lnc_Code : "";
      // this.drawer = true;
      this.dialogVisible = true;
    },
    // 页数改变事件
    handleSizeChange(size) {
      this.size = size;
      this.tableData = this.paging(size, this.index);
    },
    // 页码改变事件
    handleCurrentChange(current) {
      this.index = current;
      this.tableData = this.paging(this.size, current);
    },
    // 本地分页的方法
    paging(size, current) {
      const tableList = JSON.parse(JSON.stringify(this.tableCopyTableList));
      const tablePush = [];
      tableList.forEach((item, index) => {
        if (size * (current - 1) <= index && index <= size * current - 1) {
          tablePush.push(item);
        }
      });
      return tablePush;
    },
    //获取所有组合信息
    GetAddCombination() {
      var that = this;
      that.loading = false;
      var pData = {
        data: {
          lnc_Code: this.lnc_Code,
        },
      };
      ajax
        .post(apiUrls.GetAddCombination, pData)
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }

          // 初始化数据
          that.tableConstData = r.data.returnData;
          this.tableCopyTableList = r.data.returnData;
          // that.tableCopyTableList = r.data.returnData.map((val) => {
          //   switch (val.clus_sex) {
          //     case "1":
          //       val.clus_sex = "男";
          //       break;
          //     case "0":
          //       val.clus_sex = "女";
          //       break;
          //     case "%":
          //       val.clus_sex = "不限";
          //       break;
          //     default:
          //       break;
          //   }
          //   switch (val.clusType) {
          //     case "01":
          //       val.clusType = "健康体检";
          //       break;
          //     case "02":
          //       val.clusType = "职业体检";
          //       break;
          //     case "03":
          //       val.clusType = "从业体检";
          //       break;
          //     case "04":
          //       val.clusType = "招工体检";
          //       break;
          //     case "05":
          //       val.clusType = "学生体检";
          //       break;
          //     case "06":
          //       val.clusType = "征兵体检";
          //       break;
          //     default:
          //       break;
          //   }
          //   return val;
          // });
          // that.tableData = that.paging(that.size, that.index);
          that.tableData = that.paging(that.size, that.index);
        })
        .catch((err) => {
          alert("获取组合失败,请稍后重试");
        });
    },
    //删除组合
    DeleteClus(ids) {
      let idArr = [];
      if (ids) {
        idArr = [ids];
      } else {
        idArr = this.ids;
      }
      if (idArr.length == 0) {
        this.$message.warning("请选择组合");
        return;
      }

      this.$confirm("确定删除此组合吗, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "error",
      }).then(() => {
        let pData = {
          data: {
            ids: idArr,
          },
        };
        ajax
          .post(apiUrls.DeleteAddCombinationById, pData)
          .then((r) => {
            if (!r.data.success) {
              this.$message.error(r.data.returnMsg);
              return;
            }
            this.$message.success("删除成功");
            this.GetAddCombination();
          })
          .catch((err) => {
            console.log(err);
            debugger;
            this.$message.error("系统繁忙！请稍后再试");
          });
      });
    },
    //检查输入的参数
    checkAdminInfo() {
      if (!this.addCombinaList.item_Name) {
        this.$message.warning("请输入组合名称");
        return false;
      }
      // if (this.addCombinaList.item_sex == "男") {
      //   this.addCombinaList.item_sex = "1";
      // } else if (this.addCombinaList.item_sex == "女") {
      //   this.addCombinaList.itemsex = "0";
      // } else {
      //   this.addCombinaList.item_sex = "%";
      // }
      return true;
    },
    //新增或者修改组合
    addOrEditAddCombist() {
      //参数验证
      if (!this.checkAdminInfo()) {
        return;
      }
      var addOrEdit = ""; //请求变量 新增/编辑 api地址不一样
      if (this.addCombinaList.id) {
        if (!this.addCombinaList.item_Code) {
          this.$message.warning("请输入组合编码");
          return false;
        }
        addOrEdit = apiUrls.updateAddCombination;
      } else {
        this.addCombinaList.id = "0";
        this.addCombinaList.lnc_Code = this.lnc_Code;
        addOrEdit = apiUrls.AddToAddCombination;
      }
      var pData = {
        addCombInfo: this.addCombinaList,
      };
      ajax
        .post(addOrEdit, pData)
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          this.$message.success("操作成功");
          this.dialogVisible = false; //成功后关闭对话框
          this.GetAddCombination(); //重新加载
        })
        .catch((err) => {
          console.log(err);
          this.$message.error("系统繁忙！请稍后再试");
        });
    },
    //表格筛选
    GetNewData() {
      this.tableCopyTableList = this.tableConstData.filter((item) => {
        //筛选
        return (
          !this.addCombinaList.clus_Name ||
          item.clus_Name.includes(this.addCombinaList.clus_Name)
        );
      });
      this.tableData = this.paging(this.size, this.index);
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          done();
        })
        .catch((_) => { });
    },
    querys() {
      this.loadBtn();
    },
    // 点击选择单位
    drawerBtn(index) {
      this.lnc_Name = this.drawerData[index].lnc_Name;
      this.lnc_Code = this.drawerData[index].lnc_Code;
      storage.session.set(
        "lncList",
        JSON.stringify(this.drawerData[index])
      );
      this.GetAddCombination();
      this.drawer = false;
    },
    loadBtn() {
      var pData = {
          kw: this.drawerIpnut,
      };
      ajax
        .post(apiUrls.GetGetlncmenu, pData, { nocrypt: true })
        .then((r) => {
          if (!r.data.success) {
            this.$message.error(r.data.returnMsg);
            return;
          }
          this.drawerData = r.data.returnData;
        })
        .catch((err) => {
          this.$message.error("获取单位失败,请联系管理员");
        });
    },
    //获取组合项目
    GetClusItemComb(clus_Code) {
      var pData = {
        data: {
          clus_Code: clus_Code,
        },
      };
      ajax
        .post(apiUrls.GetClusItemCombByCode, pData)
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          let data = r.data.returnData;
          // 初始化数据
          this.checkedCities = data.clusComb;
          for (let i = 0; i < data.clusComb.length; i++) {
            this.price += data.clusComb[i].comb_Price;
          }
          this.allowAddComb = data.clusCombAdd;
        })
        .catch((err) => {
          alert("获取项目失败,请稍后重试");
        });
    },
    //获取所有项目信息
    GetAllClusItemComb() {
      var that = this;
      that.loading = false;
      ajax
        .post(apiUrls.GetAllClusItemComb)
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          // 初始化数据
          //筛选启用的项目(客户端只能显示启用项目)
          that.CombAllData = r.data.returnData;
          this.FilterEnabledItems();
          // that.CombAllData = r.data.returnData;
          // that.CombData = that.CombAllData;
        })
        .catch((err) => {
          alert("获取项目失败,请稍后重试");
        });
    },
    FilterEnabledItems() {
      var data = this.CombAllData.filter((item) => {
        return item.state == "启用";
      });
      this.CombData = data;
    },
    handleCombClick(tab, event) { },
    showEditItemDialog(row) {
      this.addCombinaListTableData = [row];
      this.getAddCombistItemList();
      // toolsUtils.sleep(500);
      this.showItemDialogVisible = true;
    },
    selectCombItemData(val) {
      let checkedItem = this.CombData.filter((item) => {
        //筛选
        return (!val || item.comb_Code.includes(val));
      });
      this.itemTableData.push(checkedItem[0]);
    },
    daleteCombItemData(row) {
      // this.itemTableData.push(checkedItem[0]);
      let itemTableData = this.itemTableData.filter(item => item !== row);
      this.itemTableData = itemTableData;
    },
    isExistenceItem(code) {
      var that = this;
      // console.log(that.allItemData);
      let isExistenceData = this.allItemData.filter((item) => { return item.comb_Code.includes(code); });
      if (isExistenceData.length <= 0) {
        return true;
      } else {
        return false;
      }
    },
    //获取组合项目
    getAddCombistItemList() {
      var pData = {
        addCombInfo: {
          item_Code: this.addCombinaListTableData[0].item_Code,
        }
      };
      ajax
        .post(apiUrls.getAddCombistItemList, pData)
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          this.itemTableData = r.data.returnData;
          // 使用深拷贝创建新对象并将其赋值给 this.allItemData
          this.allItemData = JSON.parse(JSON.stringify(this.itemTableData));
          // this.$message.success("操作成功");
          // this.dialogVisible = false; //成功后关闭对话框
          // this.showItemDialogVisible = false;
          // this.GetAddCombination(); //重新加载
        })
        .catch((err) => {
          console.log(err);
          this.$message.error("系统繁忙！请稍后再试");
        });
    },
    //新增或者修改组合
    addOrEditAddCombistItemList() {
      //参数验证
      // if (this.itemTableData.length<=0) {
      //   this.$message.error("获取单位失败,请联系管理员");
      //   return;
      // }
      // var addOrEdit = ""; //请求变量 新增/编辑 api地址不一样
      // if (this.addCombinaList.id) {
      //   if (!this.addCombinaList.item_Code) {
      //     this.$message.warning("请输入组合编码");
      //     return false;
      //   }
      //   addOrEdit = apiUrls.updateAddCombination;
      // } else {
      //   this.addCombinaList.id = "0";
      //   this.addCombinaList.lnc_Code = this.lnc_Code;
      //   addOrEdit = apiUrls.AddToAddCombination;
      // }
      var pData = {
        addCombInfo: {
          item_Code: this.addCombinaListTableData[0].item_Code,
          itemList: this.itemTableData
        }
      };
      ajax
        .post(apiUrls.addOrEditAddCombistItemList, pData)
        .then((r) => {
          if (!r.data.success) {
            alert(r.data.returnMsg);
            return;
          }
          this.$message.success("操作成功");
          this.dialogVisible = false; //成功后关闭对话框
          this.showItemDialogVisible = false;
          this.GetAddCombination(); //重新加载
        })
        .catch((err) => {
          console.log(err);
          this.$message.error("系统繁忙！请稍后再试");
        });
    },
  },
};
</script>
  
<style scoped>
.clusDiv {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 96%;
  margin: 20px auto;
}

.clusDiv .clusTop {
  width: 100%;
  margin-top: 10px;
  display: flex;
}

.clusMid {
  margin-top: 20px;
  width: 100%;
}

.clusMid .pageNation {
  margin-top: 10px;
}

.pageNation .el-pagination {
  display: flex;
  justify-content: flex-end;
}

.haoyuan-red {
  color: red;
}

.haoyuan-bg {
  background-color: baga(250, 250, 250, 0.3);
  color: #ccc;
  cursor: default !important;
}

.main_item {
  widows: 100%;
  height: 4.18rem;
  display: flex;
  align-items: center;
  padding: 0.4rem 0.2rem;
  background-color: #ffffff;
  font-size: 15px;
  box-shadow: 5px 5px 3px #909399;
  margin-bottom: 0.3rem;
}

.el-footer {
  background-color: #b3c0d1;
  color: #333;
  text-align: center;
  line-height: 60px;
}

.el-header {
  width: 100%;
  height: 300px !important;
  background-color: #b3c0d1;
}

.container-header {
  width: 100%;
}

.container-header .container-header-form {
  width: 82%;
  display: inline-table;
}

.container-header .el-form-item {
  margin-bottom: 0.3rem;
}

.CombHeader {
  width: 100%;
  height: 80px;
  border-bottom: 1px solid #ccc;
  margin-top: 2.28rem;
  margin-left: 1.28rem;
  display: flex;
  align-items: center;
}

.textDivItem {
  width: 100%;
  height: 30px;
  line-height: 30px;
  text-align: left;
  font-size: 18px;
  font-family: "Microsoft YaHei", "微软雅黑", "SimHei", "黑体", "Hei",
    sans-serif;
  padding-left: 15px;
}

.CombAll {
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  margin-top: 1.18rem;
  margin-left: 2.18rem;
}

.CombAllDiv {
  /* width: 220px !important; */
  display: flex !important;
  line-height: 40px !important;
  margin-left: 10px;
  align-items: left !important;
  font-size: 15px !important;
  box-shadow: 5px 5px 3px #909399 !important;
  margin-bottom: 1.18rem !important;
}

.CombItem {
  /* width: 220px; */
  height: 40px;
  float: left;
  clear: none;
  margin-right: 10px;
  background-color: #ffffff !important;
  /* flex-wrap: wrap; */
  /* margin-right: 1rem !important; */
}

.CombItem:nth-child(6n + 1) {
  clear: left;
}

.haoyuanCCC {
  background-color: #909399 !important;
  color: #000000 !important;
}

.allowAddCombCCC {
  background-color: #7199e9 !important;
  color: #000000 !important;
}

/* .CombItem el-checkbox {
  width: 100%;
  height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
} */
.CombItemStyle {
  /* width: 220px; */
  text-align: left;
  display: flex;
  align-items: center;
}

.CombItemStyle .checkeDiv {
  width: 15px;
  height: 15px;
  margin-left: 0.2rem;
  border: 1px solid black;
  display: flex;
  align-items: center;
}

.checkeDiv .el-icon-check {
  color: #ffffff;
}

.CombItemStyle .CombItemName {
  width: 120px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-left: 0.6rem;
}

.CombItemStyle .CombItemPrice {
  width: 70px;
  font-size: 15px;
  text-align: center;
}

.asideACombItem {
  width: 350px !important;
  height: 80px !important;
  display: flex !important;
  line-height: 80px !important;
  /* align-items: center; */
  align-items: center !important;
  padding: 0.4rem 0.2rem !important;
  background-color: #ffffff !important;
  font-size: 14px !important;
  box-shadow: 5px 5px 3px #909399 !important;
  margin-bottom: 1.18rem !important;
  /* margin-right: 1rem !important; */
  margin-bottom: 0rem !important;
  border-bottom: 1px solid #ccc;
}

.asideAll {
  height: 50%;
}

.asideSelect {
  margin-top: 1.28rem;
}

.asideTitle {
  /* width: 320px !important; */
  display: flex !important;
  /* align-items: center; */
  align-items: center !important;
  padding: 0.4rem 0.2rem !important;
  background-color: #ffffff !important;
  font-size: 14px !important;
  box-shadow: 5px 5px 3px #909399 !important;
  margin-bottom: 1.18rem !important;
  /* margin-right: 1rem !important; */
  margin-bottom: 0rem !important;
  border-bottom: 1px solid #ccc;
  height: 40px !important;
  line-height: 40px !important;
}

.asideTitle .asideMaLeft {
  margin-left: 14px;
}

.asideAStyle {
  /* width: 350px; */
  display: flex;
  text-align: left;
}

.asideAStyle .asideAName {
  width: 170px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-left: 14px;
}

.asideAStyle .asideAPrice {
  width: 40px;
  text-align: right;
}

.asideAStyle .asideDelete {
  width: 15px;
  height: 15px;
  margin-left: 2.8rem;
}

.asideAStyle .asideicon-view {
  width: 15px;
  height: 15px;
  margin-left: 0.8rem;
}

.viewContainer {
  width: 15px;
  height: 15px;
}

.el-icon-view {
  position: relative;
  z-index: 2;
}

.asideicon-onView {
  width: 15px;
  height: 15px;
  position: absolute;
  top: 0;
  left: 0;
  background: linear-gradient(to bottom right,
      transparent 0%,
      transparent calc(50% - 1px),
      #000000 50%,
      transparent calc(50% + 1px),
      transparent 100%);
}

.el-textarea {
  width: 520px;
}

.el-form-item__label {
  margin-top: 0px !important;
}

.combNameDialog {
  border-radius: 5px;
  margin: 5px auto;
  color: red;
  margin-left: 10px;
}
</style>
  